// Decompiled with JetBrains decompiler
// Type: ipis.frmSuryaLogo
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmSuryaLogo : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("pBar")]
  private ProgressBar _pBar;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;

  [DebuggerNonUserCode]
  static frmSuryaLogo()
  {
  }

  [DebuggerNonUserCode]
  public frmSuryaLogo()
  {
    frmSuryaLogo.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmSuryaLogo.__ENCList)
    {
      if (frmSuryaLogo.__ENCList.Count == frmSuryaLogo.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmSuryaLogo.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmSuryaLogo.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmSuryaLogo.__ENCList[index1] = frmSuryaLogo.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmSuryaLogo.__ENCList.RemoveRange(index1, checked (frmSuryaLogo.__ENCList.Count - index1));
        frmSuryaLogo.__ENCList.Capacity = frmSuryaLogo.__ENCList.Count;
      }
      frmSuryaLogo.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.pBar = new ProgressBar();
    this.Label1 = new Label();
    this.SuspendLayout();
    ProgressBar pBar1 = this.pBar;
    Point point1 = new Point(175, 510);
    Point point2 = point1;
    pBar1.Location = point2;
    this.pBar.Name = "pBar";
    ProgressBar pBar2 = this.pBar;
    Size size1 = new Size(264, 23);
    Size size2 = size1;
    pBar2.Size = size2;
    this.pBar.TabIndex = 0;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 18.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(161, 549);
    Point point3 = point1;
    label1_1.Location = point3;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(287, 29);
    Size size3 = size1;
    label1_2.Size = size3;
    this.Label1.TabIndex = 1;
    this.Label1.Text = "Powered by Ninja Media";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.BackgroundImage = (Image) ipis.My.Resources.Resources.NMC_Mush;
    this.BackgroundImageLayout = ImageLayout.None;
    size1 = new Size(657, 583);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.pBar);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmSuryaLogo";
    this.StartPosition = FormStartPosition.CenterScreen;
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual ProgressBar pBar
  {
    [DebuggerNonUserCode] get { return this._pBar; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._pBar = value; }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }
}

}