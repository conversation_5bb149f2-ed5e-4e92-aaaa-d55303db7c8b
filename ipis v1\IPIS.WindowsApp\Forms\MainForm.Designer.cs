namespace IPIS.WindowsApp.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            menuStrip1 = new MenuStrip();
            fileToolStripMenuItem = new ToolStripMenuItem();
            exitToolStripMenuItem = new ToolStripMenuItem();
            stationsToolStripMenuItem = new ToolStripMenuItem();
            addStationToolStripMenuItem = new ToolStripMenuItem();
            manageStationsToolStripMenuItem = new ToolStripMenuItem();
            trainsToolStripMenuItem = new ToolStripMenuItem();
            schedulesToolStripMenuItem = new ToolStripMenuItem();
            displayBoardsToolStripMenuItem = new ToolStripMenuItem();
            helpToolStripMenuItem = new ToolStripMenuItem();
            aboutToolStripMenuItem = new ToolStripMenuItem();
            toolStrip1 = new ToolStrip();
            addStationButton = new ToolStripButton();
            toolStripSeparator1 = new ToolStripSeparator();
            refreshButton = new ToolStripButton();
            statusStrip1 = new StatusStrip();
            statusLabel = new ToolStripStatusLabel();
            splitContainer1 = new SplitContainer();
            groupBox1 = new GroupBox();
            stationsDataGridView = new DataGridView();
            groupBox2 = new GroupBox();
            infoLabel = new Label();
            menuStrip1.SuspendLayout();
            toolStrip1.SuspendLayout();
            statusStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)splitContainer1).BeginInit();
            splitContainer1.Panel1.SuspendLayout();
            splitContainer1.Panel2.SuspendLayout();
            splitContainer1.SuspendLayout();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)stationsDataGridView).BeginInit();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // menuStrip1
            // 
            menuStrip1.ImageScalingSize = new Size(20, 20);
            menuStrip1.Items.AddRange(new ToolStripItem[] { fileToolStripMenuItem, stationsToolStripMenuItem, trainsToolStripMenuItem, schedulesToolStripMenuItem, displayBoardsToolStripMenuItem, helpToolStripMenuItem });
            menuStrip1.Location = new Point(0, 0);
            menuStrip1.Name = "menuStrip1";
            menuStrip1.Padding = new Padding(15, 5, 0, 5);
            menuStrip1.Size = new Size(3000, 62);
            menuStrip1.TabIndex = 0;
            menuStrip1.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { exitToolStripMenuItem });
            fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            fileToolStripMenuItem.Size = new Size(103, 52);
            fileToolStripMenuItem.Text = "&File";
            // 
            // exitToolStripMenuItem
            // 
            exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            exitToolStripMenuItem.Size = new Size(538, 66);
            exitToolStripMenuItem.Text = "E&xit";
            exitToolStripMenuItem.Click += exitToolStripMenuItem_Click;
            // 
            // stationsToolStripMenuItem
            // 
            stationsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { addStationToolStripMenuItem, manageStationsToolStripMenuItem });
            stationsToolStripMenuItem.Name = "stationsToolStripMenuItem";
            stationsToolStripMenuItem.Size = new Size(173, 52);
            stationsToolStripMenuItem.Text = "&Stations";
            // 
            // addStationToolStripMenuItem
            // 
            addStationToolStripMenuItem.Name = "addStationToolStripMenuItem";
            addStationToolStripMenuItem.Size = new Size(538, 66);
            addStationToolStripMenuItem.Text = "&Add Station";
            addStationToolStripMenuItem.Click += addStationButton_Click;
            // 
            // manageStationsToolStripMenuItem
            // 
            manageStationsToolStripMenuItem.Name = "manageStationsToolStripMenuItem";
            manageStationsToolStripMenuItem.Size = new Size(538, 66);
            manageStationsToolStripMenuItem.Text = "&Manage Stations";
            // 
            // trainsToolStripMenuItem
            // 
            trainsToolStripMenuItem.Name = "trainsToolStripMenuItem";
            trainsToolStripMenuItem.Size = new Size(139, 52);
            trainsToolStripMenuItem.Text = "&Trains";
            // 
            // schedulesToolStripMenuItem
            // 
            schedulesToolStripMenuItem.Name = "schedulesToolStripMenuItem";
            schedulesToolStripMenuItem.Size = new Size(207, 52);
            schedulesToolStripMenuItem.Text = "S&chedules";
            // 
            // displayBoardsToolStripMenuItem
            // 
            displayBoardsToolStripMenuItem.Name = "displayBoardsToolStripMenuItem";
            displayBoardsToolStripMenuItem.Size = new Size(281, 52);
            displayBoardsToolStripMenuItem.Text = "&Display Boards";
            // 
            // helpToolStripMenuItem
            // 
            helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { aboutToolStripMenuItem });
            helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            helpToolStripMenuItem.Size = new Size(123, 52);
            helpToolStripMenuItem.Text = "&Help";
            // 
            // aboutToolStripMenuItem
            // 
            aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            aboutToolStripMenuItem.Size = new Size(538, 66);
            aboutToolStripMenuItem.Text = "&About";
            // 
            // toolStrip1
            // 
            toolStrip1.ImageScalingSize = new Size(20, 20);
            toolStrip1.Items.AddRange(new ToolStripItem[] { addStationButton, toolStripSeparator1, refreshButton });
            toolStrip1.Location = new Point(0, 62);
            toolStrip1.Name = "toolStrip1";
            toolStrip1.Padding = new Padding(0, 0, 8, 0);
            toolStrip1.Size = new Size(3000, 61);
            toolStrip1.TabIndex = 1;
            toolStrip1.Text = "toolStrip1";
            // 
            // addStationButton
            // 
            addStationButton.DisplayStyle = ToolStripItemDisplayStyle.Text;
            addStationButton.Name = "addStationButton";
            addStationButton.Size = new Size(209, 52);
            addStationButton.Text = "Add Station";
            addStationButton.Click += addStationButton_Click;
            // 
            // toolStripSeparator1
            // 
            toolStripSeparator1.Name = "toolStripSeparator1";
            toolStripSeparator1.Size = new Size(6, 61);
            // 
            // refreshButton
            // 
            refreshButton.DisplayStyle = ToolStripItemDisplayStyle.Text;
            refreshButton.Name = "refreshButton";
            refreshButton.Size = new Size(142, 52);
            refreshButton.Text = "Refresh";
            refreshButton.Click += refreshButton_Click;
            // 
            // statusStrip1
            // 
            statusStrip1.ImageScalingSize = new Size(20, 20);
            statusStrip1.Items.AddRange(new ToolStripItem[] { statusLabel });
            statusStrip1.Location = new Point(0, 1737);
            statusStrip1.Name = "statusStrip1";
            statusStrip1.Padding = new Padding(2, 0, 35, 0);
            statusStrip1.Size = new Size(3000, 63);
            statusStrip1.TabIndex = 2;
            statusStrip1.Text = "statusStrip1";
            // 
            // statusLabel
            // 
            statusLabel.Name = "statusLabel";
            statusLabel.Size = new Size(116, 48);
            statusLabel.Text = "Ready";
            // 
            // splitContainer1
            // 
            splitContainer1.Dock = DockStyle.Fill;
            splitContainer1.Location = new Point(0, 123);
            splitContainer1.Margin = new Padding(8, 7, 8, 7);
            splitContainer1.Name = "splitContainer1";
            splitContainer1.Orientation = Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            splitContainer1.Panel1.Controls.Add(groupBox1);
            // 
            // splitContainer1.Panel2
            // 
            splitContainer1.Panel2.Controls.Add(groupBox2);
            splitContainer1.Size = new Size(3000, 1614);
            splitContainer1.SplitterDistance = 1199;
            splitContainer1.SplitterWidth = 10;
            splitContainer1.TabIndex = 3;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(stationsDataGridView);
            groupBox1.Dock = DockStyle.Fill;
            groupBox1.Location = new Point(0, 0);
            groupBox1.Margin = new Padding(8, 7, 8, 7);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new Padding(8, 7, 8, 7);
            groupBox1.Size = new Size(3000, 1199);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "Stations";
            // 
            // stationsDataGridView
            // 
            stationsDataGridView.AllowUserToAddRows = false;
            stationsDataGridView.AllowUserToDeleteRows = false;
            stationsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            stationsDataGridView.Dock = DockStyle.Fill;
            stationsDataGridView.Location = new Point(8, 55);
            stationsDataGridView.Margin = new Padding(8, 7, 8, 7);
            stationsDataGridView.Name = "stationsDataGridView";
            stationsDataGridView.ReadOnly = true;
            stationsDataGridView.RowHeadersWidth = 51;
            stationsDataGridView.RowTemplate.Height = 29;
            stationsDataGridView.Size = new Size(2984, 1137);
            stationsDataGridView.TabIndex = 0;
            stationsDataGridView.CellDoubleClick += stationsDataGridView_CellDoubleClick;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(infoLabel);
            groupBox2.Dock = DockStyle.Fill;
            groupBox2.Location = new Point(0, 0);
            groupBox2.Margin = new Padding(8, 7, 8, 7);
            groupBox2.Name = "groupBox2";
            groupBox2.Padding = new Padding(8, 7, 8, 7);
            groupBox2.Size = new Size(3000, 405);
            groupBox2.TabIndex = 0;
            groupBox2.TabStop = false;
            groupBox2.Text = "Information";
            // 
            // infoLabel
            // 
            infoLabel.Dock = DockStyle.Fill;
            infoLabel.Location = new Point(8, 55);
            infoLabel.Margin = new Padding(8, 0, 8, 0);
            infoLabel.Name = "infoLabel";
            infoLabel.Size = new Size(2984, 343);
            infoLabel.TabIndex = 0;
            infoLabel.Text = "Welcome to IPIS Windows Application\r\n\r\nThis application manages railway station information, train schedules, and display boards.\r\n\r\nDouble-click on a station to edit its details.";
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(20F, 48F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(3000, 1800);
            Controls.Add(splitContainer1);
            Controls.Add(statusStrip1);
            Controls.Add(toolStrip1);
            Controls.Add(menuStrip1);
            MainMenuStrip = menuStrip1;
            Margin = new Padding(8, 7, 8, 7);
            Name = "MainForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "IPIS - Integrated Passenger Information System";
            FormClosing += MainForm_FormClosing;
            menuStrip1.ResumeLayout(false);
            menuStrip1.PerformLayout();
            toolStrip1.ResumeLayout(false);
            toolStrip1.PerformLayout();
            statusStrip1.ResumeLayout(false);
            statusStrip1.PerformLayout();
            splitContainer1.Panel1.ResumeLayout(false);
            splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer1).EndInit();
            splitContainer1.ResumeLayout(false);
            groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)stationsDataGridView).EndInit();
            groupBox2.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip1;
        private ToolStripMenuItem fileToolStripMenuItem;
        private ToolStripMenuItem exitToolStripMenuItem;
        private ToolStripMenuItem stationsToolStripMenuItem;
        private ToolStripMenuItem addStationToolStripMenuItem;
        private ToolStripMenuItem manageStationsToolStripMenuItem;
        private ToolStripMenuItem trainsToolStripMenuItem;
        private ToolStripMenuItem schedulesToolStripMenuItem;
        private ToolStripMenuItem displayBoardsToolStripMenuItem;
        private ToolStripMenuItem helpToolStripMenuItem;
        private ToolStripMenuItem aboutToolStripMenuItem;
        private ToolStrip toolStrip1;
        private ToolStripButton addStationButton;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripButton refreshButton;
        private StatusStrip statusStrip1;
        private ToolStripStatusLabel statusLabel;
        private SplitContainer splitContainer1;
        private GroupBox groupBox1;
        private DataGridView stationsDataGridView;
        private GroupBox groupBox2;
        private Label infoLabel;
    }
}
