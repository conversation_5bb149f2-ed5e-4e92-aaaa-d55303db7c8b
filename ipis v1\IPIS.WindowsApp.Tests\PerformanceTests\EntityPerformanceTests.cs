using NUnit.Framework;
using FluentAssertions;
using System.Diagnostics;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Tests.Helpers;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Tests.PerformanceTests
{
    /// <summary>
    /// Performance tests for entity operations
    /// Ensures the system can handle railway operational loads
    /// </summary>
    [TestFixture]
    [Category("Performance")]
    public class EntityPerformanceTests
    {
        private const int LARGE_DATASET_SIZE = 10000;
        private const int MEDIUM_DATASET_SIZE = 1000;
        private const int SMALL_DATASET_SIZE = 100;

        #region Station Performance Tests

        [Test]
        [TestCase(SMALL_DATASET_SIZE, 100)]
        [TestCase(MEDIUM_DATASET_SIZE, 500)]
        [TestCase(LARGE_DATASET_SIZE, 2000)]
        public void Station_CreateManyInstances_PerformsWithinTimeLimit(int instanceCount, int maxMilliseconds)
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();
            var stations = new List<Station>();

            // Act
            for (int i = 0; i < instanceCount; i++)
            {
                var station = TestDataHelper.CreateValidStation();
                station.Id = i + 1;
                stations.Add(station);
            }

            stopwatch.Stop();

            // Assert
            stations.Should().HaveCount(instanceCount);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxMilliseconds);
            
            // Verify data integrity
            stations.All(s => s.IsValid()).Should().BeTrue();
            stations.Select(s => s.Code).Should().OnlyHaveUniqueItems();
        }

        [Test]
        public void Station_ComputedProperties_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var stations = TestDataHelper.CreateStationsWithRelatedEntities(100, 5, 3);
            var stopwatch = Stopwatch.StartNew();

            // Act
            var results = stations.Select(s => new
            {
                s.DisplayName,
                s.PlatformCount,
                s.ActivePlatformCount,
                s.DisplayBoardCount,
                s.OnlineDisplayBoardCount,
                s.StatusDescription
            }).ToList();

            stopwatch.Stop();

            // Assert
            results.Should().HaveCount(100);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(100); // Should be very fast
        }

        #endregion

        #region Train Performance Tests

        [Test]
        [TestCase(SMALL_DATASET_SIZE, 100)]
        [TestCase(MEDIUM_DATASET_SIZE, 500)]
        [TestCase(LARGE_DATASET_SIZE, 2000)]
        public void Train_CreateManyInstances_PerformsWithinTimeLimit(int instanceCount, int maxMilliseconds)
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();
            var trains = new List<Train>();

            // Act
            for (int i = 0; i < instanceCount; i++)
            {
                var train = TestDataHelper.CreateValidTrain();
                train.Id = i + 1;
                train.TrainNumber = $"T{i:D6}";
                trains.Add(train);
            }

            stopwatch.Stop();

            // Assert
            trains.Should().HaveCount(instanceCount);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxMilliseconds);
            
            // Verify data integrity
            trains.All(t => t.IsValid()).Should().BeTrue();
            trains.Select(t => t.TrainNumber).Should().OnlyHaveUniqueItems();
        }

        [Test]
        public void Train_OperatesOnDayCheck_PerformsEfficientlyOnLargeDataset()
        {
            // Arrange
            var trains = new List<Train>();
            for (int i = 0; i < MEDIUM_DATASET_SIZE; i++)
            {
                var train = TestDataHelper.CreateValidTrain();
                train.OperatingDays = i % 3 == 0 ? "Daily" : "Mon,Wed,Fri";
                trains.Add(train);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var mondayTrains = trains.Where(t => t.OperatesOnDay(DayOfWeek.Monday)).ToList();
            var tuesdayTrains = trains.Where(t => t.OperatesOnDay(DayOfWeek.Tuesday)).ToList();

            stopwatch.Stop();

            // Assert
            mondayTrains.Should().NotBeEmpty();
            tuesdayTrains.Should().NotBeEmpty();
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(50);
        }

        #endregion

        #region Schedule Performance Tests

        [Test]
        [TestCase(SMALL_DATASET_SIZE, 200)]
        [TestCase(MEDIUM_DATASET_SIZE, 1000)]
        [TestCase(LARGE_DATASET_SIZE, 5000)]
        public void Schedule_CreateManyInstances_PerformsWithinTimeLimit(int instanceCount, int maxMilliseconds)
        {
            // Arrange
            var stopwatch = Stopwatch.StartNew();
            var schedules = new List<Schedule>();

            // Act
            for (int i = 0; i < instanceCount; i++)
            {
                var schedule = TestDataHelper.CreateValidSchedule();
                schedule.Id = i + 1;
                schedules.Add(schedule);
            }

            stopwatch.Stop();

            // Assert
            schedules.Should().HaveCount(instanceCount);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(maxMilliseconds);
            
            // Verify data integrity
            schedules.All(s => s.IsValid()).Should().BeTrue();
        }

        [Test]
        public void Schedule_StatusUpdates_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var schedules = new List<Schedule>();
            for (int i = 0; i < MEDIUM_DATASET_SIZE; i++)
            {
                var schedule = TestDataHelper.CreateValidSchedule();
                schedule.ScheduledArrival = DateTime.Now.AddMinutes(i - 500); // Mix of past and future
                schedule.Status = ScheduleStatus.Scheduled;
                schedules.Add(schedule);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            foreach (var schedule in schedules)
            {
                schedule.UpdateStatus();
                schedule.CalculateDelay();
            }

            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(500);
            
            // Verify some schedules were updated
            schedules.Count(s => s.Status == ScheduleStatus.Delayed).Should().BeGreaterThan(0);
        }

        [Test]
        public void Schedule_ComputedProperties_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var schedules = new List<Schedule>();
            for (int i = 0; i < LARGE_DATASET_SIZE; i++)
            {
                var schedule = TestDataHelper.CreateValidSchedule();
                schedule.ScheduledArrival = DateTime.Today.AddHours(i % 24);
                schedule.DelayMinutes = i % 120;
                schedules.Add(schedule);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var results = schedules.Select(s => new
            {
                s.IsToday,
                s.IsDelayed,
                s.ExpectedArrival,
                s.ExpectedDeparture,
                s.ShouldDisplay,
                DelayDescription = s.GetDelayDescription()
            }).ToList();

            stopwatch.Stop();

            // Assert
            results.Should().HaveCount(LARGE_DATASET_SIZE);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(200);
        }

        #endregion

        #region DisplayBoard Performance Tests

        [Test]
        public void DisplayBoard_HeartbeatUpdates_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var displayBoards = new List<DisplayBoard>();
            for (int i = 0; i < MEDIUM_DATASET_SIZE; i++)
            {
                var board = TestDataHelper.CreateValidDisplayBoard();
                board.IsOnline = false;
                displayBoards.Add(board);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            foreach (var board in displayBoards)
            {
                board.UpdateHeartbeat();
            }

            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(100);
            displayBoards.All(b => b.IsOnline).Should().BeTrue();
            displayBoards.All(b => b.IsHealthy).Should().BeTrue();
        }

        [Test]
        public void DisplayBoard_LanguageSupport_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var displayBoards = new List<DisplayBoard>();
            for (int i = 0; i < MEDIUM_DATASET_SIZE; i++)
            {
                var board = TestDataHelper.CreateValidDisplayBoard();
                board.DisplayLanguages = "en,hi,regional,fr,de";
                displayBoards.Add(board);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var englishBoards = displayBoards.Where(b => b.SupportsLanguage("en")).ToList();
            var hindiBoards = displayBoards.Where(b => b.SupportsLanguage("hi")).ToList();
            var frenchBoards = displayBoards.Where(b => b.SupportsLanguage("fr")).ToList();

            stopwatch.Stop();

            // Assert
            englishBoards.Should().HaveCount(MEDIUM_DATASET_SIZE);
            hindiBoards.Should().HaveCount(MEDIUM_DATASET_SIZE);
            frenchBoards.Should().HaveCount(MEDIUM_DATASET_SIZE);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(100);
        }

        #endregion

        #region Message Performance Tests

        [Test]
        public void Message_ValidityChecks_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var messages = new List<Message>();
            var now = DateTime.UtcNow;
            
            for (int i = 0; i < LARGE_DATASET_SIZE; i++)
            {
                var message = TestDataHelper.CreateValidMessage();
                message.ValidFrom = now.AddMinutes(i - 5000); // Mix of past and future
                message.ValidTo = now.AddMinutes(i);
                message.IsActive = i % 2 == 0;
                messages.Add(message);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var validMessages = messages.Where(m => m.IsCurrentlyValid).ToList();
            var activeMessages = messages.Where(m => m.ShouldDisplay).ToList();
            var expiredMessages = messages.Where(m => m.IsExpired).ToList();

            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(200);
            validMessages.Should().NotBeEmpty();
            expiredMessages.Should().NotBeEmpty();
        }

        #endregion

        #region User Performance Tests

        [Test]
        public void User_AuthenticationOperations_PerformEfficientlyOnLargeDataset()
        {
            // Arrange
            var users = new List<User>();
            for (int i = 0; i < MEDIUM_DATASET_SIZE; i++)
            {
                var user = TestDataHelper.CreateValidUser();
                user.Username = $"user{i:D6}";
                user.FailedLoginAttempts = i % 6; // Some will be locked
                users.Add(user);
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var activeUsers = users.Where(u => u.IsActive).ToList();
            var lockedUsers = users.Where(u => u.IsCurrentlyLocked).ToList();
            var validSessions = users.Where(u => u.HasValidSession).ToList();

            // Simulate login operations
            foreach (var user in users.Take(100))
            {
                user.RecordLogin("192.168.1.100", Guid.NewGuid().ToString(), TimeSpan.FromHours(8));
            }

            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(300);
            activeUsers.Should().NotBeEmpty();
            lockedUsers.Should().NotBeEmpty();
        }

        #endregion

        #region Complex Scenario Performance Tests

        [Test]
        public void ComplexScenario_RealTimeStationOperations_PerformWithinTimeLimit()
        {
            // Arrange - Simulate a busy railway station
            var station = TestDataHelper.CreateValidStation();
            var platforms = new List<Platform>();
            var trains = new List<Train>();
            var schedules = new List<Schedule>();
            var displayBoards = new List<DisplayBoard>();
            var messages = new List<Message>();

            // Create 10 platforms
            for (int i = 0; i < 10; i++)
            {
                platforms.Add(TestDataHelper.CreateValidPlatform(station.Id));
            }

            // Create 50 trains
            for (int i = 0; i < 50; i++)
            {
                trains.Add(TestDataHelper.CreateValidTrain());
            }

            // Create 200 schedules (busy day)
            for (int i = 0; i < 200; i++)
            {
                var schedule = TestDataHelper.CreateValidSchedule(
                    trains[i % trains.Count].Id, 
                    station.Id, 
                    platforms[i % platforms.Count].Id);
                schedule.ScheduledArrival = DateTime.Today.AddMinutes(i * 5); // Every 5 minutes
                schedules.Add(schedule);
            }

            // Create 20 display boards
            for (int i = 0; i < 20; i++)
            {
                displayBoards.Add(TestDataHelper.CreateValidDisplayBoard(station.Id));
            }

            // Create 100 messages
            for (int i = 0; i < 100; i++)
            {
                messages.Add(TestDataHelper.CreateValidMessage(
                    displayBoards[i % displayBoards.Count].Id, 
                    station.Id));
            }

            var stopwatch = Stopwatch.StartNew();

            // Act - Simulate real-time operations
            
            // 1. Update all schedule statuses
            foreach (var schedule in schedules)
            {
                schedule.UpdateStatus();
                schedule.CalculateDelay();
            }

            // 2. Update display board heartbeats
            foreach (var board in displayBoards)
            {
                board.UpdateHeartbeat();
            }

            // 3. Check message validity
            var activeMessages = messages.Where(m => m.ShouldDisplay).ToList();

            // 4. Get today's schedules
            var todaysSchedules = schedules.Where(s => s.IsToday).ToList();

            // 5. Get delayed trains
            var delayedSchedules = schedules.Where(s => s.IsDelayed).ToList();

            // 6. Calculate station summary
            var stationSummary = new
            {
                TotalPlatforms = platforms.Count,
                ActivePlatforms = platforms.Count(p => p.IsActive),
                TotalDisplayBoards = displayBoards.Count,
                OnlineDisplayBoards = displayBoards.Count(db => db.IsOnline),
                TodaysSchedules = todaysSchedules.Count,
                DelayedSchedules = delayedSchedules.Count,
                ActiveMessages = activeMessages.Count
            };

            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should complete within 1 second
            
            // Verify results
            stationSummary.TotalPlatforms.Should().Be(10);
            stationSummary.TotalDisplayBoards.Should().Be(20);
            stationSummary.OnlineDisplayBoards.Should().Be(20); // All should be online after heartbeat
            todaysSchedules.Should().NotBeEmpty();
            activeMessages.Should().NotBeEmpty();
        }

        [Test]
        public void MemoryUsage_LargeDatasetOperations_StaysWithinLimits()
        {
            // Arrange
            var initialMemory = GC.GetTotalMemory(true);
            var entities = new List<object>();

            // Act - Create large dataset
            for (int i = 0; i < LARGE_DATASET_SIZE; i++)
            {
                entities.Add(TestDataHelper.CreateValidStation());
                entities.Add(TestDataHelper.CreateValidTrain());
                entities.Add(TestDataHelper.CreateValidSchedule());
                
                if (i % 1000 == 0)
                {
                    // Force garbage collection periodically
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }

            var finalMemory = GC.GetTotalMemory(true);
            var memoryUsed = finalMemory - initialMemory;

            // Assert
            entities.Should().HaveCount(LARGE_DATASET_SIZE * 3);
            memoryUsed.Should().BeLessThan(500 * 1024 * 1024); // Less than 500MB
        }

        #endregion

        #region Stress Tests

        [Test]
        [Explicit("Long running stress test")]
        public void StressTest_ConcurrentOperations_HandlesLoad()
        {
            // Arrange
            const int threadCount = 10;
            const int operationsPerThread = 1000;
            var tasks = new List<Task>();
            var results = new ConcurrentBag<bool>();

            // Act
            for (int i = 0; i < threadCount; i++)
            {
                var task = Task.Run(() =>
                {
                    try
                    {
                        for (int j = 0; j < operationsPerThread; j++)
                        {
                            var station = TestDataHelper.CreateValidStation();
                            var train = TestDataHelper.CreateValidTrain();
                            var schedule = TestDataHelper.CreateValidSchedule();
                            
                            // Perform operations
                            station.UpdateTimestamp();
                            train.UpdateTimestamp();
                            schedule.UpdateStatus();
                            schedule.CalculateDelay();
                        }
                        results.Add(true);
                    }
                    catch
                    {
                        results.Add(false);
                    }
                });
                tasks.Add(task);
            }

            var stopwatch = Stopwatch.StartNew();
            Task.WaitAll(tasks.ToArray());
            stopwatch.Stop();

            // Assert
            results.Should().HaveCount(threadCount);
            results.All(r => r).Should().BeTrue(); // All threads should succeed
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // Should complete within 30 seconds
        }

        #endregion
    }
}
