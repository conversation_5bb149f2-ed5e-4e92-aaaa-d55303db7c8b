using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Configuration
{
    /// <summary>
    /// Configuration class for Railway API integration
    /// Contains all settings required for external Railway API communication
    /// </summary>
    public class RailwayApiConfiguration
    {
        /// <summary>
        /// Configuration section name in appsettings.json
        /// </summary>
        public const string SectionName = "RailwayApi";

        /// <summary>
        /// Base URL of the Railway API
        /// </summary>
        [Required]
        [Url]
        public string BaseUrl { get; set; } = "https://api.railway.gov.in";

        /// <summary>
        /// API key for authentication
        /// </summary>
        [Required]
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// API version to use
        /// </summary>
        [Required]
        public string ApiVersion { get; set; } = "v1";

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        [Range(5, 300)]
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Number of retry attempts for failed requests
        /// </summary>
        [Range(0, 10)]
        public int RetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts in seconds
        /// </summary>
        [Range(1, 60)]
        public int RetryDelaySeconds { get; set; } = 2;

        /// <summary>
        /// Synchronization interval in minutes
        /// </summary>
        [Range(1, 1440)]
        public int SyncIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// Enable real-time synchronization
        /// </summary>
        public bool EnableRealTimeSync { get; set; } = true;

        /// <summary>
        /// Enable offline mode support
        /// </summary>
        public bool EnableOfflineMode { get; set; } = true;

        /// <summary>
        /// Cache expiration time in hours
        /// </summary>
        [Range(1, 168)]
        public int CacheExpirationHours { get; set; } = 24;

        /// <summary>
        /// Maximum number of concurrent API requests
        /// </summary>
        [Range(1, 50)]
        public int MaxConcurrentRequests { get; set; } = 10;

        /// <summary>
        /// Enable circuit breaker pattern
        /// </summary>
        public bool EnableCircuitBreaker { get; set; } = true;

        /// <summary>
        /// Circuit breaker failure threshold
        /// </summary>
        [Range(1, 20)]
        public int CircuitBreakerFailureThreshold { get; set; } = 5;

        /// <summary>
        /// Circuit breaker timeout in minutes
        /// </summary>
        [Range(1, 60)]
        public int CircuitBreakerTimeoutMinutes { get; set; } = 5;

        /// <summary>
        /// Enable request/response logging
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// Log sensitive data (API keys, etc.)
        /// </summary>
        public bool LogSensitiveData { get; set; } = false;

        /// <summary>
        /// Enable performance monitoring
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// User agent string for API requests
        /// </summary>
        public string UserAgent { get; set; } = "IPIS-WindowsApp/1.0";

        /// <summary>
        /// Additional HTTP headers to include in requests
        /// </summary>
        public Dictionary<string, string> AdditionalHeaders { get; set; } = new();

        /// <summary>
        /// API endpoints configuration
        /// </summary>
        public ApiEndpoints Endpoints { get; set; } = new();

        /// <summary>
        /// Authentication configuration
        /// </summary>
        public AuthenticationConfig Authentication { get; set; } = new();

        /// <summary>
        /// Rate limiting configuration
        /// </summary>
        public RateLimitConfig RateLimit { get; set; } = new();

        /// <summary>
        /// Validates the configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(BaseUrl))
                result.Errors.Add("BaseUrl is required");

            if (string.IsNullOrWhiteSpace(ApiKey))
                result.Errors.Add("ApiKey is required");

            if (string.IsNullOrWhiteSpace(ApiVersion))
                result.Errors.Add("ApiVersion is required");

            if (!Uri.TryCreate(BaseUrl, UriKind.Absolute, out _))
                result.Errors.Add("BaseUrl must be a valid URL");

            if (TimeoutSeconds < 5 || TimeoutSeconds > 300)
                result.Errors.Add("TimeoutSeconds must be between 5 and 300");

            if (RetryAttempts < 0 || RetryAttempts > 10)
                result.Errors.Add("RetryAttempts must be between 0 and 10");

            if (SyncIntervalMinutes < 1 || SyncIntervalMinutes > 1440)
                result.Errors.Add("SyncIntervalMinutes must be between 1 and 1440");

            // Validate endpoints
            var endpointValidation = Endpoints.Validate();
            result.Errors.AddRange(endpointValidation.Errors);
            result.Warnings.AddRange(endpointValidation.Warnings);

            // Validate authentication
            var authValidation = Authentication.Validate();
            result.Errors.AddRange(authValidation.Errors);
            result.Warnings.AddRange(authValidation.Warnings);

            return result;
        }

        /// <summary>
        /// Gets the complete API URL for an endpoint
        /// </summary>
        /// <param name="endpoint">Endpoint path</param>
        /// <returns>Complete URL</returns>
        public string GetApiUrl(string endpoint)
        {
            var baseUri = new Uri(BaseUrl);
            var versionedPath = $"api/{ApiVersion}/{endpoint.TrimStart('/')}";
            return new Uri(baseUri, versionedPath).ToString();
        }
    }

    /// <summary>
    /// API endpoints configuration
    /// </summary>
    public class ApiEndpoints
    {
        /// <summary>
        /// Health check endpoint
        /// </summary>
        public string Health { get; set; } = "health";

        /// <summary>
        /// Train schedules endpoint
        /// </summary>
        public string Schedules { get; set; } = "schedules";

        /// <summary>
        /// Train status endpoint
        /// </summary>
        public string TrainStatus { get; set; } = "trains/status";

        /// <summary>
        /// Delay information endpoint
        /// </summary>
        public string Delays { get; set; } = "delays";

        /// <summary>
        /// Platform assignments endpoint
        /// </summary>
        public string PlatformAssignments { get; set; } = "platforms/assignments";

        /// <summary>
        /// Live train positions endpoint
        /// </summary>
        public string LivePositions { get; set; } = "trains/live";

        /// <summary>
        /// Batch operations endpoint
        /// </summary>
        public string Batch { get; set; } = "batch";

        /// <summary>
        /// Validates the endpoints configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Health))
                result.Errors.Add("Health endpoint is required");

            if (string.IsNullOrWhiteSpace(Schedules))
                result.Errors.Add("Schedules endpoint is required");

            if (string.IsNullOrWhiteSpace(TrainStatus))
                result.Errors.Add("TrainStatus endpoint is required");

            return result;
        }
    }

    /// <summary>
    /// Authentication configuration
    /// </summary>
    public class AuthenticationConfig
    {
        /// <summary>
        /// Authentication type (ApiKey, Bearer, Basic)
        /// </summary>
        public string Type { get; set; } = "ApiKey";

        /// <summary>
        /// Header name for API key authentication
        /// </summary>
        public string ApiKeyHeader { get; set; } = "X-API-Key";

        /// <summary>
        /// Bearer token for Bearer authentication
        /// </summary>
        public string? BearerToken { get; set; }

        /// <summary>
        /// Username for Basic authentication
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// Password for Basic authentication
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// Token refresh endpoint
        /// </summary>
        public string? TokenRefreshEndpoint { get; set; }

        /// <summary>
        /// Token expiration time in minutes
        /// </summary>
        public int TokenExpirationMinutes { get; set; } = 60;

        /// <summary>
        /// Validates the authentication configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Type))
                result.Errors.Add("Authentication Type is required");

            switch (Type.ToLowerInvariant())
            {
                case "apikey":
                    if (string.IsNullOrWhiteSpace(ApiKeyHeader))
                        result.Errors.Add("ApiKeyHeader is required for ApiKey authentication");
                    break;

                case "bearer":
                    if (string.IsNullOrWhiteSpace(BearerToken))
                        result.Warnings.Add("BearerToken should be provided for Bearer authentication");
                    break;

                case "basic":
                    if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
                        result.Errors.Add("Username and Password are required for Basic authentication");
                    break;

                default:
                    result.Errors.Add($"Unsupported authentication type: {Type}");
                    break;
            }

            return result;
        }
    }

    /// <summary>
    /// Rate limiting configuration
    /// </summary>
    public class RateLimitConfig
    {
        /// <summary>
        /// Enable rate limiting
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Maximum requests per minute
        /// </summary>
        [Range(1, 10000)]
        public int RequestsPerMinute { get; set; } = 100;

        /// <summary>
        /// Maximum requests per hour
        /// </summary>
        [Range(1, 100000)]
        public int RequestsPerHour { get; set; } = 1000;

        /// <summary>
        /// Burst limit for short-term spikes
        /// </summary>
        [Range(1, 1000)]
        public int BurstLimit { get; set; } = 20;

        /// <summary>
        /// Delay between requests in milliseconds
        /// </summary>
        [Range(0, 10000)]
        public int DelayBetweenRequestsMs { get; set; } = 100;
    }

    /// <summary>
    /// Validation result for configuration
    /// </summary>
    public class ValidationResult
    {
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public bool IsValid => Errors.Count == 0;
    }
}
