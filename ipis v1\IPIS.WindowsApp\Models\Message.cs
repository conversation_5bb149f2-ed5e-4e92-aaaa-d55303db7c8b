using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IPIS.WindowsApp.Models
{
    /// <summary>
    /// Represents the type of message
    /// </summary>
    public enum MessageType
    {
        Train,
        Announcement,
        Emergency,
        Information
    }

    /// <summary>
    /// Represents a message displayed on display boards
    /// </summary>
    [Table("Messages")]
    public class Message
    {
        /// <summary>
        /// Gets or sets the unique identifier for the message
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the display board ID this message belongs to
        /// </summary>
        [Required]
        public int DisplayBoardId { get; set; }

        /// <summary>
        /// Gets or sets the type of message
        /// </summary>
        public MessageType MessageType { get; set; }

        /// <summary>
        /// Gets or sets the content of the message
        /// </summary>
        [Required]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the language of the message (e.g., "en", "es", "fr")
        /// </summary>
        [StringLength(10)]
        public string Language { get; set; } = "en";

        /// <summary>
        /// Gets or sets the priority of the message (1 = highest, 10 = lowest)
        /// </summary>
        public int Priority { get; set; } = 1;

        /// <summary>
        /// Gets or sets whether the message is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Gets or sets when the message becomes valid
        /// </summary>
        public DateTime ValidFrom { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gets or sets when the message expires (null for no expiration)
        /// </summary>
        public DateTime? ValidTo { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the message was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for the display board this message belongs to
        /// </summary>
        [ForeignKey("DisplayBoardId")]
        public virtual DisplayBoard DisplayBoard { get; set; } = null!;
    }
}
