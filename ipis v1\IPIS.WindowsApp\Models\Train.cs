using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IPIS.WindowsApp.Models
{
    /// <summary>
    /// Represents a train in the IPIS system
    /// </summary>
    [Table("Trains")]
    public class Train
    {
        /// <summary>
        /// Gets or sets the unique identifier for the train
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the train number (e.g., "12345", "EXP001")
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the train name (e.g., "Express", "Local")
        /// </summary>
        [StringLength(100)]
        public string? TrainName { get; set; }

        /// <summary>
        /// Gets or sets the type of train (e.g., "Express", "Local", "Freight")
        /// </summary>
        [StringLength(50)]
        public string? TrainType { get; set; }

        /// <summary>
        /// Gets or sets whether the train is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Gets or sets the date and time when the train was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for schedules associated with this train
        /// </summary>
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }
}
