using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a message entity for display boards and announcements
    /// Based on legacy system analysis - enhanced for multi-language support
    /// </summary>
    [Table("Messages")]
    public class Message
    {
        /// <summary>
        /// Primary key for the message
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the display board (optional, for board-specific messages)
        /// </summary>
        [ForeignKey("DisplayBoard")]
        public int? DisplayBoardId { get; set; }

        /// <summary>
        /// Foreign key to the station (optional, for station-wide messages)
        /// </summary>
        [ForeignKey("Station")]
        public int? StationId { get; set; }

        /// <summary>
        /// Foreign key to the platform (optional, for platform-specific messages)
        /// </summary>
        [ForeignKey("Platform")]
        public int? PlatformId { get; set; }

        /// <summary>
        /// Foreign key to the schedule (optional, for train-specific messages)
        /// </summary>
        [ForeignKey("Schedule")]
        public int? ScheduleId { get; set; }

        /// <summary>
        /// Type of message (Train, Announcement, Emergency, etc.)
        /// </summary>
        [Required]
        public MessageType MessageType { get; set; }

        /// <summary>
        /// Plain text content of the message
        /// </summary>
        [Required(ErrorMessage = "Message content is required")]
        [Column(TypeName = "TEXT")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Rich text/HTML content (optional)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? ContentHtml { get; set; }

        /// <summary>
        /// Language of the message
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Language { get; set; } = "en";

        /// <summary>
        /// Priority level (1=Highest, 10=Lowest)
        /// </summary>
        [Required]
        [Range(1, 10, ErrorMessage = "Priority must be between 1 and 10")]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Indicates if the message is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the message should scroll on display
        /// </summary>
        public bool IsScrolling { get; set; } = false;

        /// <summary>
        /// Scrolling speed (characters per second)
        /// </summary>
        [Range(1, 50, ErrorMessage = "Scroll speed must be between 1 and 50")]
        public int? ScrollSpeed { get; set; }

        /// <summary>
        /// Message validity start time
        /// </summary>
        [Required]
        public DateTime ValidFrom { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Message validity end time (optional)
        /// </summary>
        public DateTime? ValidTo { get; set; }

        /// <summary>
        /// Number of times to repeat the message (0 = infinite)
        /// </summary>
        [Range(0, 1000, ErrorMessage = "Repeat count must be between 0 and 1000")]
        public int RepeatCount { get; set; } = 1;

        /// <summary>
        /// Current repeat counter
        /// </summary>
        public int CurrentRepeatCount { get; set; } = 0;

        /// <summary>
        /// Indicates if audio announcement is enabled
        /// </summary>
        public bool AudioEnabled { get; set; } = false;

        /// <summary>
        /// Audio file path for voice announcement
        /// </summary>
        [StringLength(500)]
        public string? AudioFilePath { get; set; }

        /// <summary>
        /// Display duration in seconds (0 = until manually removed)
        /// </summary>
        [Range(0, 86400, ErrorMessage = "Display duration must be between 0 and 86400 seconds")]
        public int DisplayDuration { get; set; } = 0;

        /// <summary>
        /// Font size for display (overrides board default)
        /// </summary>
        [Range(8, 72, ErrorMessage = "Font size must be between 8 and 72")]
        public int? FontSize { get; set; }

        /// <summary>
        /// Font color (hex format)
        /// </summary>
        [StringLength(7)]
        [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "Font color must be in hex format (#RRGGBB)")]
        public string? FontColor { get; set; }

        /// <summary>
        /// Background color (hex format)
        /// </summary>
        [StringLength(7)]
        [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "Background color must be in hex format (#RRGGBB)")]
        public string? BackgroundColor { get; set; }

        /// <summary>
        /// Text alignment (Left, Center, Right)
        /// </summary>
        [StringLength(10)]
        public string? TextAlignment { get; set; } = "Left";

        /// <summary>
        /// Indicates if the message has been published to display boards
        /// </summary>
        public bool IsPublished { get; set; } = false;

        /// <summary>
        /// Timestamp when the message was published
        /// </summary>
        public DateTime? PublishedAt { get; set; }

        /// <summary>
        /// Timestamp when the message was last displayed
        /// </summary>
        public DateTime? LastDisplayed { get; set; }

        /// <summary>
        /// Number of times the message has been displayed
        /// </summary>
        public int DisplayCount { get; set; } = 0;

        /// <summary>
        /// Approval status for the message
        /// </summary>
        [StringLength(20)]
        public string? ApprovalStatus { get; set; } = "Pending";

        /// <summary>
        /// User who approved the message
        /// </summary>
        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// Timestamp when the message was approved
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Additional metadata in JSON format
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Metadata { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// The display board this message is associated with (optional)
        /// </summary>
        public virtual DisplayBoard? DisplayBoard { get; set; }

        /// <summary>
        /// The station this message is associated with (optional)
        /// </summary>
        public virtual Station? Station { get; set; }

        /// <summary>
        /// The platform this message is associated with (optional)
        /// </summary>
        public virtual Platform? Platform { get; set; }

        /// <summary>
        /// The schedule this message is associated with (optional)
        /// </summary>
        public virtual Schedule? Schedule { get; set; }

        // Computed Properties

        /// <summary>
        /// Indicates if the message is currently valid (within validity period)
        /// </summary>
        [NotMapped]
        public bool IsCurrentlyValid
        {
            get
            {
                var now = DateTime.UtcNow;
                return now >= ValidFrom && (ValidTo == null || now <= ValidTo);
            }
        }

        /// <summary>
        /// Indicates if the message has expired
        /// </summary>
        [NotMapped]
        public bool IsExpired => ValidTo.HasValue && DateTime.UtcNow > ValidTo.Value;

        /// <summary>
        /// Gets the remaining validity time
        /// </summary>
        [NotMapped]
        public TimeSpan? RemainingValidTime => ValidTo.HasValue 
            ? ValidTo.Value - DateTime.UtcNow 
            : null;

        /// <summary>
        /// Indicates if the message should be displayed based on all criteria
        /// </summary>
        [NotMapped]
        public bool ShouldDisplay => IsActive && 
                                    IsCurrentlyValid && 
                                    (RepeatCount == 0 || CurrentRepeatCount < RepeatCount);

        /// <summary>
        /// Gets the priority description
        /// </summary>
        [NotMapped]
        public string PriorityDescription => Priority switch
        {
            1 => "Critical",
            2 => "High",
            3 => "Medium-High",
            4 => "Medium",
            5 => "Normal",
            6 => "Medium-Low",
            7 => "Low",
            8 => "Very Low",
            9 => "Minimal",
            10 => "Background",
            _ => "Unknown"
        };

        /// <summary>
        /// Gets the content preview (first 50 characters)
        /// </summary>
        [NotMapped]
        public string ContentPreview => Content.Length > 50 
            ? Content.Substring(0, 50) + "..." 
            : Content;

        // Methods

        /// <summary>
        /// Publishes the message to display boards
        /// </summary>
        public void Publish()
        {
            IsPublished = true;
            PublishedAt = DateTime.UtcNow;
            UpdateTimestamp();
        }

        /// <summary>
        /// Records a display event
        /// </summary>
        public void RecordDisplay()
        {
            LastDisplayed = DateTime.UtcNow;
            DisplayCount++;
            CurrentRepeatCount++;
            UpdateTimestamp();
        }

        /// <summary>
        /// Approves the message
        /// </summary>
        /// <param name="approvedBy">User who approved the message</param>
        public void Approve(string approvedBy)
        {
            ApprovalStatus = "Approved";
            ApprovedBy = approvedBy;
            ApprovedAt = DateTime.UtcNow;
            UpdateTimestamp();
        }

        /// <summary>
        /// Rejects the message
        /// </summary>
        /// <param name="rejectedBy">User who rejected the message</param>
        public void Reject(string rejectedBy)
        {
            ApprovalStatus = "Rejected";
            ApprovedBy = rejectedBy;
            ApprovedAt = DateTime.UtcNow;
            IsActive = false;
            UpdateTimestamp();
        }

        /// <summary>
        /// Deactivates the message
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            UpdateTimestamp();
        }

        /// <summary>
        /// Extends the validity period
        /// </summary>
        /// <param name="newValidTo">New validity end time</param>
        public void ExtendValidity(DateTime newValidTo)
        {
            ValidTo = newValidTo;
            UpdateTimestamp();
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the message has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Content) && 
                   !string.IsNullOrWhiteSpace(Language) && 
                   Priority >= 1 && Priority <= 10;
        }

        /// <summary>
        /// Gets a summary of the message for display purposes
        /// </summary>
        /// <returns>Message summary string</returns>
        public string GetSummary()
        {
            return $"Message: {ContentPreview}, Type: {MessageType}, " +
                   $"Priority: {PriorityDescription}, Language: {Language}";
        }

        /// <summary>
        /// Returns a string representation of the message
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return ContentPreview;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current message
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Message other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the message
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
