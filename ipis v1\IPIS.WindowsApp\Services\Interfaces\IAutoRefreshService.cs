using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Service for managing automatic refresh of railway data
    /// Handles configurable auto-refresh for stations and trains with customizable intervals
    /// </summary>
    public interface IAutoRefreshService
    {
        /// <summary>
        /// Occurs when auto-refresh data is updated
        /// </summary>
        event EventHandler<AutoRefreshDataUpdatedEventArgs>? DataUpdated;

        /// <summary>
        /// Occurs when auto-refresh encounters an error
        /// </summary>
        event EventHandler<AutoRefreshErrorEventArgs>? RefreshError;

        /// <summary>
        /// Occurs when auto-refresh status changes
        /// </summary>
        event EventHandler<AutoRefreshStatusChangedEventArgs>? StatusChanged;

        /// <summary>
        /// Starts the auto-refresh service
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops the auto-refresh service
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Enables automatic refresh for a station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="intervalMinutes">Refresh interval in minutes (default: 15)</param>
        /// <param name="startHoursBefore">Hours before train arrival to start monitoring (default: 2)</param>
        /// <param name="stopHoursAfter">Hours after train departure to stop monitoring (default: 1)</param>
        /// <returns>Success status</returns>
        Task<bool> EnableStationAutoRefreshAsync(string stationCode, int intervalMinutes = 15, int startHoursBefore = 2, int stopHoursAfter = 1);

        /// <summary>
        /// Enables automatic refresh for a train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="intervalMinutes">Refresh interval in minutes (default: 15)</param>
        /// <param name="startHoursBefore">Hours before train arrival to start monitoring (default: 2)</param>
        /// <param name="stopHoursAfter">Hours after train departure to stop monitoring (default: 1)</param>
        /// <returns>Success status</returns>
        Task<bool> EnableTrainAutoRefreshAsync(string trainNumber, int intervalMinutes = 15, int startHoursBefore = 2, int stopHoursAfter = 1);

        /// <summary>
        /// Disables automatic refresh for a station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <returns>Success status</returns>
        Task<bool> DisableStationAutoRefreshAsync(string stationCode);

        /// <summary>
        /// Disables automatic refresh for a train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <returns>Success status</returns>
        Task<bool> DisableTrainAutoRefreshAsync(string trainNumber);

        /// <summary>
        /// Gets current auto-refresh configuration for a station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <returns>Auto-refresh configuration</returns>
        Task<AutoRefreshConfigDto?> GetStationAutoRefreshConfigAsync(string stationCode);

        /// <summary>
        /// Gets current auto-refresh configuration for a train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <returns>Auto-refresh configuration</returns>
        Task<AutoRefreshConfigDto?> GetTrainAutoRefreshConfigAsync(string trainNumber);

        /// <summary>
        /// Gets all active auto-refresh configurations
        /// </summary>
        /// <returns>List of active configurations</returns>
        Task<List<AutoRefreshConfigDto>> GetActiveConfigurationsAsync();

        /// <summary>
        /// Updates auto-refresh configuration
        /// </summary>
        /// <param name="config">Updated configuration</param>
        /// <returns>Success status</returns>
        Task<bool> UpdateConfigurationAsync(AutoRefreshConfigDto config);

        /// <summary>
        /// Manually triggers refresh for a station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RefreshStationDataAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Manually triggers refresh for a train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RefreshTrainDataAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets refresh statistics
        /// </summary>
        /// <returns>Refresh statistics</returns>
        Task<AutoRefreshStatsDto> GetRefreshStatsAsync();

        /// <summary>
        /// Clears all auto-refresh configurations
        /// </summary>
        /// <returns>Success status</returns>
        Task<bool> ClearAllConfigurationsAsync();

        /// <summary>
        /// Gets the current service status
        /// </summary>
        /// <returns>Service status</returns>
        AutoRefreshServiceStatus GetServiceStatus();

        /// <summary>
        /// Pauses auto-refresh temporarily
        /// </summary>
        /// <returns>Success status</returns>
        Task<bool> PauseAsync();

        /// <summary>
        /// Resumes auto-refresh
        /// </summary>
        /// <returns>Success status</returns>
        Task<bool> ResumeAsync();
    }

    /// <summary>
    /// Event arguments for auto-refresh data updated event
    /// </summary>
    public class AutoRefreshDataUpdatedEventArgs : EventArgs
    {
        public string EntityType { get; set; } = string.Empty; // "Station" or "Train"
        public string EntityId { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty; // "Arrivals", "Departures", "Status", etc.
        public int RecordCount { get; set; }
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public object? Data { get; set; }
    }

    /// <summary>
    /// Event arguments for auto-refresh error event
    /// </summary>
    public class AutoRefreshErrorEventArgs : EventArgs
    {
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public string Operation { get; set; } = string.Empty;
        public Exception Exception { get; set; } = null!;
        public DateTime ErrorAt { get; set; } = DateTime.UtcNow;
        public int RetryAttempt { get; set; }
    }

    /// <summary>
    /// Event arguments for auto-refresh status changed event
    /// </summary>
    public class AutoRefreshStatusChangedEventArgs : EventArgs
    {
        public AutoRefreshServiceStatus OldStatus { get; set; }
        public AutoRefreshServiceStatus NewStatus { get; set; }
        public DateTime StatusChangedAt { get; set; } = DateTime.UtcNow;
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Auto-refresh service status enumeration
    /// </summary>
    public enum AutoRefreshServiceStatus
    {
        Stopped,
        Starting,
        Running,
        Paused,
        Stopping,
        Error
    }
}
