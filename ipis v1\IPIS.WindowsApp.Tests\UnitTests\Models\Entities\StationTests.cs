using NUnit.Framework;
using FluentAssertions;
using AutoFixture;
using AutoFixture.NUnit3;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Tests.UnitTests.Models.Entities
{
    /// <summary>
    /// Comprehensive test suite for Station entity
    /// Tests every line of code and business logic
    /// </summary>
    [TestFixture]
    public class StationTests
    {
        private IFixture _fixture;

        [SetUp]
        public void SetUp()
        {
            _fixture = new Fixture();
            _fixture.Customize<Station>(c => c
                .With(s => s.Code, "TEST")
                .With(s => s.Name, "Test Station")
                .With(s => s.IsActive, true)
                .With(s => s.StationType, StationType.Regular)
                .With(s => s.TimeZone, "UTC"));
        }

        #region Constructor and Property Tests

        [Test]
        public void Station_DefaultConstructor_SetsDefaultValues()
        {
            // Act
            var station = new Station();

            // Assert
            station.Id.Should().Be(0);
            station.Code.Should().Be(string.Empty);
            station.Name.Should().Be(string.Empty);
            station.IsActive.Should().BeTrue();
            station.StationType.Should().Be(StationType.Regular);
            station.TimeZone.Should().Be("UTC");
            station.HasPassengerFacilities.Should().BeTrue();
            station.HasFreightFacilities.Should().BeFalse();
            station.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            station.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            station.Platforms.Should().NotBeNull().And.BeEmpty();
            station.Schedules.Should().NotBeNull().And.BeEmpty();
            station.DisplayBoards.Should().NotBeNull().And.BeEmpty();
            station.Messages.Should().NotBeNull().And.BeEmpty();
            station.VoiceFiles.Should().NotBeNull().And.BeEmpty();
            station.Advertisements.Should().NotBeNull().And.BeEmpty();
        }

        [Test]
        [AutoData]
        public void Station_SetProperties_SetsCorrectly(string code, string name, string location)
        {
            // Arrange
            var station = new Station();
            var stationType = StationType.Junction;
            var timeZone = "Asia/Kolkata";

            // Act
            station.Code = code;
            station.Name = name;
            station.Location = location;
            station.StationType = stationType;
            station.TimeZone = timeZone;
            station.IsActive = false;
            station.HasPassengerFacilities = false;
            station.HasFreightFacilities = true;

            // Assert
            station.Code.Should().Be(code);
            station.Name.Should().Be(name);
            station.Location.Should().Be(location);
            station.StationType.Should().Be(stationType);
            station.TimeZone.Should().Be(timeZone);
            station.IsActive.Should().BeFalse();
            station.HasPassengerFacilities.Should().BeFalse();
            station.HasFreightFacilities.Should().BeTrue();
        }

        #endregion

        #region Validation Tests

        [Test]
        public void Station_ValidData_PassesValidation()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeTrue();
            validationResults.Should().BeEmpty();
        }

        [Test]
        [TestCase("", "Station code is required")]
        [TestCase(null, "Station code is required")]
        public void Station_InvalidCode_FailsValidation(string code, string expectedError)
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = code;
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        public void Station_CodeTooLong_FailsValidation()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = new string('A', 11); // 11 characters, max is 10
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("cannot exceed 10 characters"));
        }

        [Test]
        [TestCase("test123", "Station code must contain only uppercase letters and numbers")]
        [TestCase("TEST-123", "Station code must contain only uppercase letters and numbers")]
        [TestCase("TEST 123", "Station code must contain only uppercase letters and numbers")]
        public void Station_InvalidCodeFormat_FailsValidation(string code, string expectedError)
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = code;
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        [TestCase("", "Station name is required")]
        [TestCase(null, "Station name is required")]
        public void Station_InvalidName_FailsValidation(string name, string expectedError)
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Name = name;
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        public void Station_NameTooLong_FailsValidation()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Name = new string('A', 101); // 101 characters, max is 100
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("cannot exceed 100 characters"));
        }

        [Test]
        public void Station_InvalidEmail_FailsValidation()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.ContactEmail = "invalid-email";
            var validationContext = new ValidationContext(station);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(station, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == "Invalid email format");
        }

        #endregion

        #region Computed Properties Tests

        [Test]
        public void PlatformCount_WithPlatforms_ReturnsCorrectCount()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var platforms = _fixture.CreateMany<Platform>(3).ToList();
            station.Platforms = platforms;

            // Act & Assert
            station.PlatformCount.Should().Be(3);
        }

        [Test]
        public void PlatformCount_WithNullPlatforms_ReturnsZero()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Platforms = null!;

            // Act & Assert
            station.PlatformCount.Should().Be(0);
        }

        [Test]
        public void ActivePlatformCount_WithMixedPlatforms_ReturnsCorrectCount()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var platforms = new List<Platform>
            {
                _fixture.Build<Platform>().With(p => p.IsActive, true).Create(),
                _fixture.Build<Platform>().With(p => p.IsActive, false).Create(),
                _fixture.Build<Platform>().With(p => p.IsActive, true).Create()
            };
            station.Platforms = platforms;

            // Act & Assert
            station.ActivePlatformCount.Should().Be(2);
        }

        [Test]
        public void DisplayBoardCount_WithDisplayBoards_ReturnsCorrectCount()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var displayBoards = _fixture.CreateMany<DisplayBoard>(2).ToList();
            station.DisplayBoards = displayBoards;

            // Act & Assert
            station.DisplayBoardCount.Should().Be(2);
        }

        [Test]
        public void OnlineDisplayBoardCount_WithMixedDisplayBoards_ReturnsCorrectCount()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var displayBoards = new List<DisplayBoard>
            {
                _fixture.Build<DisplayBoard>().With(db => db.IsOnline, true).Create(),
                _fixture.Build<DisplayBoard>().With(db => db.IsOnline, false).Create(),
                _fixture.Build<DisplayBoard>().With(db => db.IsOnline, true).Create()
            };
            station.DisplayBoards = displayBoards;

            // Act & Assert
            station.OnlineDisplayBoardCount.Should().Be(2);
        }

        [Test]
        public void DisplayName_WithCodeAndName_ReturnsFormattedString()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = "NYC";
            station.Name = "New York Central";

            // Act & Assert
            station.DisplayName.Should().Be("NYC - New York Central");
        }

        [Test]
        public void StatusDescription_WhenActive_ReturnsActive()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.IsActive = true;

            // Act & Assert
            station.StatusDescription.Should().Be("Active");
        }

        [Test]
        public void StatusDescription_WhenInactive_ReturnsInactive()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.IsActive = false;

            // Act & Assert
            station.StatusDescription.Should().Be("Inactive");
        }

        #endregion

        #region Method Tests

        [Test]
        public void UpdateTimestamp_WhenCalled_UpdatesTimestamp()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var originalTimestamp = station.UpdatedAt;
            Thread.Sleep(10); // Ensure time difference

            // Act
            station.UpdateTimestamp();

            // Assert
            station.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void UpdateTimestamp_WithUser_UpdatesTimestampAndUser()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var originalTimestamp = station.UpdatedAt;
            var updatedBy = "testuser";
            Thread.Sleep(10); // Ensure time difference

            // Act
            station.UpdateTimestamp(updatedBy);

            // Assert
            station.UpdatedAt.Should().BeAfter(originalTimestamp);
            station.UpdatedBy.Should().Be(updatedBy);
        }

        [Test]
        public void IsValid_WithValidData_ReturnsTrue()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = "TEST";
            station.Name = "Test Station";

            // Act & Assert
            station.IsValid().Should().BeTrue();
        }

        [Test]
        [TestCase("", "Test Station", false)]
        [TestCase(null, "Test Station", false)]
        [TestCase("TEST", "", false)]
        [TestCase("TEST", null, false)]
        [TestCase("VERYLONGCODE", "Test Station", false)] // Code too long
        [TestCase("TEST", "This is a very long station name that exceeds the maximum allowed length of 100 characters and should fail validation", false)]
        public void IsValid_WithInvalidData_ReturnsFalse(string code, string name, bool expected)
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = code;
            station.Name = name;

            // Act & Assert
            station.IsValid().Should().Be(expected);
        }

        [Test]
        public void GetSummary_WithValidData_ReturnsFormattedSummary()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = "NYC";
            station.Name = "New York Central";
            station.StationType = StationType.Terminal;
            station.IsActive = true;
            station.Platforms = _fixture.CreateMany<Platform>(3).ToList();

            // Act
            var summary = station.GetSummary();

            // Assert
            summary.Should().Contain("NYC - New York Central");
            summary.Should().Contain("Terminal");
            summary.Should().Contain("Platforms: 3");
            summary.Should().Contain("Active");
        }

        [Test]
        public void ToString_ReturnsDisplayName()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Code = "TEST";
            station.Name = "Test Station";

            // Act
            var result = station.ToString();

            // Assert
            result.Should().Be("TEST - Test Station");
        }

        #endregion

        #region Equality Tests

        [Test]
        public void Equals_WithSameIdAndCode_ReturnsTrue()
        {
            // Arrange
            var station1 = _fixture.Create<Station>();
            station1.Id = 1;
            station1.Code = "TEST";

            var station2 = _fixture.Create<Station>();
            station2.Id = 1;
            station2.Code = "TEST";

            // Act & Assert
            station1.Equals(station2).Should().BeTrue();
        }

        [Test]
        public void Equals_WithDifferentId_ReturnsFalse()
        {
            // Arrange
            var station1 = _fixture.Create<Station>();
            station1.Id = 1;
            station1.Code = "TEST";

            var station2 = _fixture.Create<Station>();
            station2.Id = 2;
            station2.Code = "TEST";

            // Act & Assert
            station1.Equals(station2).Should().BeFalse();
        }

        [Test]
        public void Equals_WithDifferentCode_ReturnsFalse()
        {
            // Arrange
            var station1 = _fixture.Create<Station>();
            station1.Id = 1;
            station1.Code = "TEST1";

            var station2 = _fixture.Create<Station>();
            station2.Id = 1;
            station2.Code = "TEST2";

            // Act & Assert
            station1.Equals(station2).Should().BeFalse();
        }

        [Test]
        public void Equals_WithNull_ReturnsFalse()
        {
            // Arrange
            var station = _fixture.Create<Station>();

            // Act & Assert
            station.Equals(null).Should().BeFalse();
        }

        [Test]
        public void Equals_WithDifferentType_ReturnsFalse()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            var otherObject = "not a station";

            // Act & Assert
            station.Equals(otherObject).Should().BeFalse();
        }

        [Test]
        public void GetHashCode_WithSameIdAndCode_ReturnsSameHashCode()
        {
            // Arrange
            var station1 = _fixture.Create<Station>();
            station1.Id = 1;
            station1.Code = "TEST";

            var station2 = _fixture.Create<Station>();
            station2.Id = 1;
            station2.Code = "TEST";

            // Act & Assert
            station1.GetHashCode().Should().Be(station2.GetHashCode());
        }

        [Test]
        public void GetHashCode_WithDifferentData_ReturnsDifferentHashCode()
        {
            // Arrange
            var station1 = _fixture.Create<Station>();
            station1.Id = 1;
            station1.Code = "TEST1";

            var station2 = _fixture.Create<Station>();
            station2.Id = 2;
            station2.Code = "TEST2";

            // Act & Assert
            station1.GetHashCode().Should().NotBe(station2.GetHashCode());
        }

        #endregion

        #region Edge Cases and Boundary Tests

        [Test]
        public void Station_WithMaxLengthValues_ShouldBeValid()
        {
            // Arrange
            var station = new Station
            {
                Code = new string('A', 10), // Max length
                Name = new string('B', 100), // Max length
                Location = new string('C', 200), // Max length
                TimeZone = new string('D', 50), // Max length
                ContactPhone = new string('1', 20), // Max length
                ContactEmail = "test@" + new string('e', 90) + ".com", // Max length
                StationMaster = new string('F', 100), // Max length
                OperatingHours = new string('G', 50), // Max length
                Remarks = new string('H', 500) // Max length
            };

            // Act & Assert
            station.IsValid().Should().BeTrue();
        }

        [Test]
        public void Station_WithMinimalValidData_ShouldBeValid()
        {
            // Arrange
            var station = new Station
            {
                Code = "A",
                Name = "B"
            };

            // Act & Assert
            station.IsValid().Should().BeTrue();
        }

        [Test]
        public void Station_WithNullCollections_ShouldHandleGracefully()
        {
            // Arrange
            var station = _fixture.Create<Station>();
            station.Platforms = null!;
            station.Schedules = null!;
            station.DisplayBoards = null!;
            station.Messages = null!;
            station.VoiceFiles = null!;
            station.Advertisements = null!;

            // Act & Assert
            station.PlatformCount.Should().Be(0);
            station.ActivePlatformCount.Should().Be(0);
            station.DisplayBoardCount.Should().Be(0);
            station.OnlineDisplayBoardCount.Should().Be(0);
        }

        #endregion

        #region Performance Tests

        [Test]
        public void Station_CreateManyInstances_ShouldPerformWell()
        {
            // Arrange
            const int instanceCount = 10000;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var stations = new List<Station>();
            for (int i = 0; i < instanceCount; i++)
            {
                stations.Add(new Station
                {
                    Code = $"ST{i:D6}",
                    Name = $"Station {i}",
                    IsActive = true
                });
            }

            stopwatch.Stop();

            // Assert
            stations.Should().HaveCount(instanceCount);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should complete within 1 second
        }

        #endregion
    }
}
