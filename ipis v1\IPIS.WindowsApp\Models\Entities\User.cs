using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a user entity for authentication and authorization
    /// Based on legacy system analysis - enhanced with comprehensive security features
    /// </summary>
    [Table("Users")]
    public class User
    {
        /// <summary>
        /// Primary key for the user
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Unique username for login
        /// </summary>
        [Required(ErrorMessage = "Username is required")]
        [StringLength(50, ErrorMessage = "Username cannot exceed 50 characters")]
        [RegularExpression(@"^[a-zA-Z0-9_]+$", ErrorMessage = "Username can only contain letters, numbers, and underscores")]
        [Column("Username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Email address (unique)
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [Column("Email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Hashed password (using BCrypt)
        /// </summary>
        [Required(ErrorMessage = "Password hash is required")]
        [StringLength(255)]
        [Column("PasswordHash")]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// Password salt for additional security
        /// </summary>
        [StringLength(255)]
        public string? PasswordSalt { get; set; }

        /// <summary>
        /// First name of the user
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
        [Column("FirstName")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the user
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
        [Column("LastName")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// User role in the system
        /// </summary>
        [Required]
        public UserRole Role { get; set; } = UserRole.Viewer;

        /// <summary>
        /// Station access permissions (JSON array of station IDs)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? StationAccess { get; set; }

        /// <summary>
        /// Phone number
        /// </summary>
        [StringLength(20)]
        [Phone(ErrorMessage = "Invalid phone number format")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Employee ID or badge number
        /// </summary>
        [StringLength(20)]
        public string? EmployeeId { get; set; }

        /// <summary>
        /// Department or division
        /// </summary>
        [StringLength(50)]
        public string? Department { get; set; }

        /// <summary>
        /// Job title or position
        /// </summary>
        [StringLength(100)]
        public string? JobTitle { get; set; }

        /// <summary>
        /// Indicates if the user account is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the user account is locked
        /// </summary>
        public bool IsLocked { get; set; } = false;

        /// <summary>
        /// Indicates if email is verified
        /// </summary>
        public bool IsEmailVerified { get; set; } = false;

        /// <summary>
        /// Indicates if two-factor authentication is enabled
        /// </summary>
        public bool IsTwoFactorEnabled { get; set; } = false;

        /// <summary>
        /// Last login timestamp
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Last login IP address
        /// </summary>
        [StringLength(45)] // IPv6 support
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// Current login session ID
        /// </summary>
        [StringLength(255)]
        public string? CurrentSessionId { get; set; }

        /// <summary>
        /// Session expiration time
        /// </summary>
        public DateTime? SessionExpiresAt { get; set; }

        /// <summary>
        /// Number of failed login attempts
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// Account locked until this timestamp
        /// </summary>
        public DateTime? AccountLockedUntil { get; set; }

        /// <summary>
        /// Password last changed timestamp
        /// </summary>
        public DateTime? PasswordChangedAt { get; set; }

        /// <summary>
        /// Indicates if password must be changed on next login
        /// </summary>
        public bool MustChangePassword { get; set; } = false;

        /// <summary>
        /// Password reset token
        /// </summary>
        [StringLength(255)]
        public string? PasswordResetToken { get; set; }

        /// <summary>
        /// Password reset token expiration
        /// </summary>
        public DateTime? PasswordResetTokenExpiry { get; set; }

        /// <summary>
        /// Email verification token
        /// </summary>
        [StringLength(255)]
        public string? EmailVerificationToken { get; set; }

        /// <summary>
        /// Two-factor authentication secret key
        /// </summary>
        [StringLength(255)]
        public string? TwoFactorSecret { get; set; }

        /// <summary>
        /// Backup codes for two-factor authentication
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? TwoFactorBackupCodes { get; set; }

        /// <summary>
        /// User preferences in JSON format
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Preferences { get; set; }

        /// <summary>
        /// Profile picture file path
        /// </summary>
        [StringLength(500)]
        public string? ProfilePicture { get; set; }

        /// <summary>
        /// User's preferred language
        /// </summary>
        [StringLength(10)]
        public string PreferredLanguage { get; set; } = "en";

        /// <summary>
        /// User's timezone
        /// </summary>
        [StringLength(50)]
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// Additional notes about the user
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// Collection of audit logs for this user
        /// </summary>
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();

        // Computed Properties

        /// <summary>
        /// Gets the full name of the user
        /// </summary>
        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Gets the display name (full name with username)
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{FullName} ({Username})";

        /// <summary>
        /// Indicates if the account is currently locked
        /// </summary>
        [NotMapped]
        public bool IsCurrentlyLocked => IsLocked || 
                                        (AccountLockedUntil.HasValue && DateTime.UtcNow < AccountLockedUntil.Value);

        /// <summary>
        /// Indicates if the current session is valid
        /// </summary>
        [NotMapped]
        public bool HasValidSession => !string.IsNullOrWhiteSpace(CurrentSessionId) && 
                                      SessionExpiresAt.HasValue && 
                                      DateTime.UtcNow < SessionExpiresAt.Value;

        /// <summary>
        /// Gets the time since last login
        /// </summary>
        [NotMapped]
        public TimeSpan? TimeSinceLastLogin => LastLoginAt.HasValue 
            ? DateTime.UtcNow - LastLoginAt.Value 
            : null;

        /// <summary>
        /// Indicates if password needs to be changed (expired or forced)
        /// </summary>
        [NotMapped]
        public bool NeedsPasswordChange => MustChangePassword || 
                                          (PasswordChangedAt.HasValue && 
                                           DateTime.UtcNow - PasswordChangedAt.Value > TimeSpan.FromDays(90));

        /// <summary>
        /// Gets the accessible station IDs as a list
        /// </summary>
        [NotMapped]
        public List<int> AccessibleStationIds
        {
            get
            {
                if (string.IsNullOrWhiteSpace(StationAccess))
                    return new List<int>();

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<int>>(StationAccess) ?? new List<int>();
                }
                catch
                {
                    return new List<int>();
                }
            }
        }

        // Methods

        /// <summary>
        /// Records a successful login
        /// </summary>
        /// <param name="ipAddress">IP address of the login</param>
        /// <param name="sessionId">Session ID</param>
        /// <param name="sessionDuration">Session duration</param>
        public void RecordLogin(string ipAddress, string sessionId, TimeSpan sessionDuration)
        {
            LastLoginAt = DateTime.UtcNow;
            LastLoginIp = ipAddress;
            CurrentSessionId = sessionId;
            SessionExpiresAt = DateTime.UtcNow.Add(sessionDuration);
            FailedLoginAttempts = 0;
            IsLocked = false;
            AccountLockedUntil = null;
            UpdateTimestamp();
        }

        /// <summary>
        /// Records a failed login attempt
        /// </summary>
        /// <param name="maxAttempts">Maximum allowed attempts before locking</param>
        /// <param name="lockoutDuration">Duration to lock the account</param>
        public void RecordFailedLogin(int maxAttempts = 5, TimeSpan? lockoutDuration = null)
        {
            FailedLoginAttempts++;
            
            if (FailedLoginAttempts >= maxAttempts)
            {
                IsLocked = true;
                AccountLockedUntil = DateTime.UtcNow.Add(lockoutDuration ?? TimeSpan.FromMinutes(30));
            }
            
            UpdateTimestamp();
        }

        /// <summary>
        /// Logs out the user by clearing session information
        /// </summary>
        public void Logout()
        {
            CurrentSessionId = null;
            SessionExpiresAt = null;
            UpdateTimestamp();
        }

        /// <summary>
        /// Unlocks the user account
        /// </summary>
        public void Unlock()
        {
            IsLocked = false;
            AccountLockedUntil = null;
            FailedLoginAttempts = 0;
            UpdateTimestamp();
        }

        /// <summary>
        /// Changes the user's password
        /// </summary>
        /// <param name="newPasswordHash">New password hash</param>
        public void ChangePassword(string newPasswordHash)
        {
            PasswordHash = newPasswordHash;
            PasswordChangedAt = DateTime.UtcNow;
            MustChangePassword = false;
            PasswordResetToken = null;
            PasswordResetTokenExpiry = null;
            UpdateTimestamp();
        }

        /// <summary>
        /// Sets a password reset token
        /// </summary>
        /// <param name="token">Reset token</param>
        /// <param name="expiry">Token expiry time</param>
        public void SetPasswordResetToken(string token, DateTime expiry)
        {
            PasswordResetToken = token;
            PasswordResetTokenExpiry = expiry;
            UpdateTimestamp();
        }

        /// <summary>
        /// Verifies the email address
        /// </summary>
        public void VerifyEmail()
        {
            IsEmailVerified = true;
            EmailVerificationToken = null;
            UpdateTimestamp();
        }

        /// <summary>
        /// Checks if the user has access to a specific station
        /// </summary>
        /// <param name="stationId">Station ID to check</param>
        /// <returns>True if has access, false otherwise</returns>
        public bool HasStationAccess(int stationId)
        {
            // Admin and Station Master have access to all stations
            if (Role == UserRole.Admin || Role == UserRole.StationMaster)
                return true;

            return AccessibleStationIds.Contains(stationId);
        }

        /// <summary>
        /// Adds access to a station
        /// </summary>
        /// <param name="stationId">Station ID to add</param>
        public void AddStationAccess(int stationId)
        {
            var stationIds = AccessibleStationIds;
            if (!stationIds.Contains(stationId))
            {
                stationIds.Add(stationId);
                StationAccess = System.Text.Json.JsonSerializer.Serialize(stationIds);
                UpdateTimestamp();
            }
        }

        /// <summary>
        /// Removes access to a station
        /// </summary>
        /// <param name="stationId">Station ID to remove</param>
        public void RemoveStationAccess(int stationId)
        {
            var stationIds = AccessibleStationIds;
            if (stationIds.Remove(stationId))
            {
                StationAccess = System.Text.Json.JsonSerializer.Serialize(stationIds);
                UpdateTimestamp();
            }
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the user has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Username) && 
                   !string.IsNullOrWhiteSpace(Email) && 
                   !string.IsNullOrWhiteSpace(PasswordHash) && 
                   !string.IsNullOrWhiteSpace(FirstName) && 
                   !string.IsNullOrWhiteSpace(LastName);
        }

        /// <summary>
        /// Gets a summary of the user for display purposes
        /// </summary>
        /// <returns>User summary string</returns>
        public string GetSummary()
        {
            return $"User: {DisplayName}, Role: {Role}, " +
                   $"Status: {(IsActive ? "Active" : "Inactive")}, " +
                   $"Last Login: {LastLoginAt?.ToString("yyyy-MM-dd HH:mm") ?? "Never"}";
        }

        /// <summary>
        /// Returns a string representation of the user
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayName;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current user
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is User other)
            {
                return Id == other.Id && 
                       Username.Equals(other.Username, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the user
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Id, Username.ToUpperInvariant());
        }
    }
}
