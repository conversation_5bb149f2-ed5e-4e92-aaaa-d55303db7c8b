using IPIS.WindowsApp.Models.DTOs.Railway;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Timers;

namespace IPIS.WindowsApp.Services.Implementations
{
    /// <summary>
    /// Implementation of auto-refresh service for railway data
    /// Manages configurable automatic refresh for stations and trains
    /// </summary>
    public class AutoRefreshService : IAutoRefreshService, IDisposable
    {
        private readonly ILogger<AutoRefreshService> _logger;
        private readonly IRailwayApiService _railwayApiService;
        private readonly ConcurrentDictionary<string, AutoRefreshConfigDto> _activeConfigurations;
        private readonly ConcurrentDictionary<string, System.Timers.Timer> _refreshTimers;
        private readonly ConcurrentDictionary<string, DateTime> _lastRefreshTimes;
        private readonly ConcurrentDictionary<string, int> _failureCounts;
        private readonly object _statusLock = new();
        private AutoRefreshServiceStatus _status = AutoRefreshServiceStatus.Stopped;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _disposed = false;

        public AutoRefreshService(
            ILogger<AutoRefreshService> logger,
            IRailwayApiService railwayApiService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _railwayApiService = railwayApiService ?? throw new ArgumentNullException(nameof(railwayApiService));

            _activeConfigurations = new ConcurrentDictionary<string, AutoRefreshConfigDto>();
            _refreshTimers = new ConcurrentDictionary<string, System.Timers.Timer>();
            _lastRefreshTimes = new ConcurrentDictionary<string, DateTime>();
            _failureCounts = new ConcurrentDictionary<string, int>();

            _logger.LogInformation("Auto-refresh service initialized");
        }

        #region Events

        public event EventHandler<AutoRefreshDataUpdatedEventArgs>? DataUpdated;
        public event EventHandler<AutoRefreshErrorEventArgs>? RefreshError;
        public event EventHandler<AutoRefreshStatusChangedEventArgs>? StatusChanged;

        #endregion

        #region Service Control

        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                lock (_statusLock)
                {
                    if (_status == AutoRefreshServiceStatus.Running)
                    {
                        _logger.LogWarning("Auto-refresh service is already running");
                        return;
                    }

                    ChangeStatus(AutoRefreshServiceStatus.Starting, "Service starting");
                }

                _cancellationTokenSource = new CancellationTokenSource();

                // Load existing configurations from database
                await LoadConfigurationsFromDatabaseAsync();

                // Start refresh timers for active configurations
                await StartRefreshTimersAsync();

                lock (_statusLock)
                {
                    ChangeStatus(AutoRefreshServiceStatus.Running, "Service started successfully");
                }

                _logger.LogInformation("Auto-refresh service started with {Count} active configurations",
                    _activeConfigurations.Count);
            }
            catch (Exception ex)
            {
                lock (_statusLock)
                {
                    ChangeStatus(AutoRefreshServiceStatus.Error, $"Failed to start service: {ex.Message}");
                }
                _logger.LogError(ex, "Error starting auto-refresh service");
                throw;
            }
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                lock (_statusLock)
                {
                    if (_status == AutoRefreshServiceStatus.Stopped)
                    {
                        _logger.LogWarning("Auto-refresh service is already stopped");
                        return;
                    }

                    ChangeStatus(AutoRefreshServiceStatus.Stopping, "Service stopping");
                }

                // Cancel all operations
                _cancellationTokenSource?.Cancel();

                // Stop all timers
                await StopAllTimersAsync();

                // Clear collections
                _activeConfigurations.Clear();
                _lastRefreshTimes.Clear();
                _failureCounts.Clear();

                lock (_statusLock)
                {
                    ChangeStatus(AutoRefreshServiceStatus.Stopped, "Service stopped successfully");
                }

                _logger.LogInformation("Auto-refresh service stopped");
            }
            catch (Exception ex)
            {
                lock (_statusLock)
                {
                    ChangeStatus(AutoRefreshServiceStatus.Error, $"Failed to stop service: {ex.Message}");
                }
                _logger.LogError(ex, "Error stopping auto-refresh service");
                throw;
            }
        }

        public async Task<bool> PauseAsync()
        {
            try
            {
                lock (_statusLock)
                {
                    if (_status != AutoRefreshServiceStatus.Running)
                    {
                        _logger.LogWarning("Cannot pause auto-refresh service - not running");
                        return false;
                    }

                    ChangeStatus(AutoRefreshServiceStatus.Paused, "Service paused");
                }

                // Pause all timers
                foreach (var timer in _refreshTimers.Values)
                {
                    timer.Stop();
                }

                _logger.LogInformation("Auto-refresh service paused");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pausing auto-refresh service");
                return false;
            }
        }

        public async Task<bool> ResumeAsync()
        {
            try
            {
                lock (_statusLock)
                {
                    if (_status != AutoRefreshServiceStatus.Paused)
                    {
                        _logger.LogWarning("Cannot resume auto-refresh service - not paused");
                        return false;
                    }

                    ChangeStatus(AutoRefreshServiceStatus.Running, "Service resumed");
                }

                // Resume all timers
                foreach (var timer in _refreshTimers.Values)
                {
                    timer.Start();
                }

                _logger.LogInformation("Auto-refresh service resumed");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resuming auto-refresh service");
                return false;
            }
        }

        public AutoRefreshServiceStatus GetServiceStatus()
        {
            lock (_statusLock)
            {
                return _status;
            }
        }

        #endregion

        #region Configuration Management

        public async Task<bool> EnableStationAutoRefreshAsync(string stationCode, int intervalMinutes = 15, int startHoursBefore = 2, int stopHoursAfter = 1)
        {
            try
            {
                var config = new AutoRefreshConfigDto
                {
                    EntityType = "Station",
                    EntityId = stationCode,
                    IsEnabled = true,
                    IntervalMinutes = intervalMinutes,
                    StartHoursBefore = startHoursBefore,
                    StopHoursAfter = stopHoursAfter,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                return await SaveConfigurationAsync(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enabling auto-refresh for station {StationCode}", stationCode);
                return false;
            }
        }

        public async Task<bool> EnableTrainAutoRefreshAsync(string trainNumber, int intervalMinutes = 15, int startHoursBefore = 2, int stopHoursAfter = 1)
        {
            try
            {
                var config = new AutoRefreshConfigDto
                {
                    EntityType = "Train",
                    EntityId = trainNumber,
                    IsEnabled = true,
                    IntervalMinutes = intervalMinutes,
                    StartHoursBefore = startHoursBefore,
                    StopHoursAfter = stopHoursAfter,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                return await SaveConfigurationAsync(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enabling auto-refresh for train {TrainNumber}", trainNumber);
                return false;
            }
        }

        public async Task<bool> DisableStationAutoRefreshAsync(string stationCode)
        {
            try
            {
                var key = $"Station_{stationCode}";

                // Stop and remove timer
                if (_refreshTimers.TryRemove(key, out var timer))
                {
                    timer.Stop();
                    timer.Dispose();
                }

                // Remove from active configurations
                _activeConfigurations.TryRemove(key, out _);
                _lastRefreshTimes.TryRemove(key, out _);
                _failureCounts.TryRemove(key, out _);

                // Remove from database (placeholder for future implementation)
                // Note: You would need to create AutoRefreshConfig entity and DbSet
                // This is a placeholder for the database operation

                _logger.LogInformation("Disabled auto-refresh for station {StationCode}", stationCode);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disabling auto-refresh for station {StationCode}", stationCode);
                return false;
            }
        }

        public async Task<bool> DisableTrainAutoRefreshAsync(string trainNumber)
        {
            try
            {
                var key = $"Train_{trainNumber}";

                // Stop and remove timer
                if (_refreshTimers.TryRemove(key, out var timer))
                {
                    timer.Stop();
                    timer.Dispose();
                }

                // Remove from active configurations
                _activeConfigurations.TryRemove(key, out _);
                _lastRefreshTimes.TryRemove(key, out _);
                _failureCounts.TryRemove(key, out _);

                // Remove from database (placeholder for future implementation)
                // Note: You would need to create AutoRefreshConfig entity and DbSet
                // This is a placeholder for the database operation

                _logger.LogInformation("Disabled auto-refresh for train {TrainNumber}", trainNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disabling auto-refresh for train {TrainNumber}", trainNumber);
                return false;
            }
        }

        #endregion

        #region Private Methods

        private void ChangeStatus(AutoRefreshServiceStatus newStatus, string? reason = null)
        {
            var oldStatus = _status;
            _status = newStatus;

            StatusChanged?.Invoke(this, new AutoRefreshStatusChangedEventArgs
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Reason = reason
            });

            _logger.LogInformation("Auto-refresh service status changed from {OldStatus} to {NewStatus}: {Reason}",
                oldStatus, newStatus, reason ?? "No reason provided");
        }

        private async Task LoadConfigurationsFromDatabaseAsync()
        {
            try
            {
                // This is a placeholder - you would load from your database
                // For now, we'll create some sample configurations
                _logger.LogInformation("Loading auto-refresh configurations from database");

                // In a real implementation, you would:
                // using var context = await _dbContextFactory.CreateDbContextAsync();
                // var configs = await context.AutoRefreshConfigs.Where(c => c.IsEnabled).ToListAsync();
                // foreach (var config in configs) { ... }

                _logger.LogInformation("Loaded {Count} auto-refresh configurations", _activeConfigurations.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading auto-refresh configurations from database");
                throw;
            }
        }

        private async Task StartRefreshTimersAsync()
        {
            foreach (var config in _activeConfigurations.Values)
            {
                if (config.IsActive)
                {
                    await CreateRefreshTimerAsync(config);
                }
            }
        }

        private async Task StopAllTimersAsync()
        {
            foreach (var timer in _refreshTimers.Values)
            {
                timer.Stop();
                timer.Dispose();
            }
            _refreshTimers.Clear();
        }

        private async Task CreateRefreshTimerAsync(AutoRefreshConfigDto config)
        {
            try
            {
                var key = $"{config.EntityType}_{config.EntityId}";
                var timer = new System.Timers.Timer(config.IntervalMinutes * 60 * 1000); // Convert to milliseconds

                timer.Elapsed += async (sender, e) => await OnTimerElapsedAsync(config);
                timer.AutoReset = true;
                timer.Start();

                _refreshTimers[key] = timer;

                _logger.LogDebug("Created refresh timer for {EntityType} {EntityId} with interval {Interval} minutes",
                    config.EntityType, config.EntityId, config.IntervalMinutes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating refresh timer for {EntityType} {EntityId}",
                    config.EntityType, config.EntityId);
            }
        }

        private async Task OnTimerElapsedAsync(AutoRefreshConfigDto config)
        {
            if (_status != AutoRefreshServiceStatus.Running)
                return;

            try
            {
                var key = $"{config.EntityType}_{config.EntityId}";

                // Check if we should refresh based on train schedules
                if (config.RefreshOnlyDuringSchedules && !await ShouldRefreshNowAsync(config))
                {
                    _logger.LogDebug("Skipping refresh for {EntityType} {EntityId} - outside schedule window",
                        config.EntityType, config.EntityId);
                    return;
                }

                _logger.LogDebug("Starting scheduled refresh for {EntityType} {EntityId}",
                    config.EntityType, config.EntityId);

                bool success = false;
                if (config.EntityType == "Station")
                {
                    success = await RefreshStationDataAsync(config.EntityId);
                }
                else if (config.EntityType == "Train")
                {
                    success = await RefreshTrainDataAsync(config.EntityId);
                }

                if (success)
                {
                    _lastRefreshTimes[key] = DateTime.UtcNow;
                    _failureCounts[key] = 0;
                    config.LastSuccessfulRefresh = DateTime.UtcNow;
                    config.CurrentFailures = 0;
                }
                else
                {
                    var failureCount = _failureCounts.AddOrUpdate(key, 1, (k, v) => v + 1);
                    config.CurrentFailures = failureCount;
                    config.LastFailedRefresh = DateTime.UtcNow;

                    if (failureCount >= config.MaxFailures)
                    {
                        _logger.LogWarning("Disabling auto-refresh for {EntityType} {EntityId} due to {Count} consecutive failures",
                            config.EntityType, config.EntityId, failureCount);

                        // Disable the configuration
                        config.IsEnabled = false;
                        await UpdateConfigurationAsync(config);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during scheduled refresh for {EntityType} {EntityId}",
                    config.EntityType, config.EntityId);

                OnRefreshError(config.EntityType, config.EntityId, "ScheduledRefresh", ex);
            }
        }

        private async Task<bool> ShouldRefreshNowAsync(AutoRefreshConfigDto config)
        {
            try
            {
                var now = DateTime.Now;

                // Check if current time is within active hours
                if (config.StartTime.HasValue && config.EndTime.HasValue)
                {
                    var currentTime = now.TimeOfDay;
                    if (config.StartTime.Value <= config.EndTime.Value)
                    {
                        // Same day range
                        if (currentTime < config.StartTime.Value || currentTime > config.EndTime.Value)
                            return false;
                    }
                    else
                    {
                        // Overnight range
                        if (currentTime < config.StartTime.Value && currentTime > config.EndTime.Value)
                            return false;
                    }
                }

                // For trains, check if there are upcoming arrivals/departures within the monitoring window
                if (config.EntityType == "Train")
                {
                    // This would require checking train schedules
                    // For now, we'll assume it should refresh during active hours
                    return true;
                }

                // For stations, check if there are trains arriving/departing within the monitoring window
                if (config.EntityType == "Station")
                {
                    var startTime = now.AddHours(-config.StartHoursBefore);
                    var endTime = now.AddHours(config.StopHoursAfter);

                    // This would require checking if there are any trains scheduled within this window
                    // For now, we'll assume it should refresh during active hours
                    return true;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if should refresh for {EntityType} {EntityId}",
                    config.EntityType, config.EntityId);
                return false;
            }
        }

        private async Task<bool> SaveConfigurationAsync(AutoRefreshConfigDto config)
        {
            try
            {
                var key = $"{config.EntityType}_{config.EntityId}";

                // Save to database (placeholder for future implementation)
                // Note: You would need to create AutoRefreshConfig entity and DbSet
                // This is a placeholder for the database operation

                // Add to active configurations
                _activeConfigurations[key] = config;

                // Create timer if service is running
                if (_status == AutoRefreshServiceStatus.Running)
                {
                    await CreateRefreshTimerAsync(config);
                }

                _logger.LogInformation("Saved auto-refresh configuration for {EntityType} {EntityId}",
                    config.EntityType, config.EntityId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving auto-refresh configuration for {EntityType} {EntityId}",
                    config.EntityType, config.EntityId);
                return false;
            }
        }

        private void OnDataUpdated(string entityType, string entityId, string dataType, int recordCount, object? data)
        {
            DataUpdated?.Invoke(this, new AutoRefreshDataUpdatedEventArgs
            {
                EntityType = entityType,
                EntityId = entityId,
                DataType = dataType,
                RecordCount = recordCount,
                Data = data
            });
        }

        private void OnRefreshError(string entityType, string entityId, string operation, Exception exception, int retryAttempt = 0)
        {
            RefreshError?.Invoke(this, new AutoRefreshErrorEventArgs
            {
                EntityType = entityType,
                EntityId = entityId,
                Operation = operation,
                Exception = exception,
                RetryAttempt = retryAttempt
            });
        }

        public async Task<AutoRefreshConfigDto?> GetStationAutoRefreshConfigAsync(string stationCode)
        {
            var key = $"Station_{stationCode}";
            return _activeConfigurations.TryGetValue(key, out var config) ? config : null;
        }

        public async Task<AutoRefreshConfigDto?> GetTrainAutoRefreshConfigAsync(string trainNumber)
        {
            var key = $"Train_{trainNumber}";
            return _activeConfigurations.TryGetValue(key, out var config) ? config : null;
        }

        public async Task<List<AutoRefreshConfigDto>> GetActiveConfigurationsAsync()
        {
            return _activeConfigurations.Values.Where(c => c.IsActive).ToList();
        }

        public async Task<bool> UpdateConfigurationAsync(AutoRefreshConfigDto config)
        {
            return await SaveConfigurationAsync(config);
        }

        public async Task<bool> RefreshStationDataAsync(string stationCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Manually refreshing data for station {StationCode}", stationCode);

                var now = DateTime.Now;
                var fromTime = now.AddHours(-2);
                var toTime = now.AddHours(12);

                // Get arriving trains
                var arrivals = await _railwayApiService.GetArrivingTrainsAsync(stationCode, fromTime, toTime, cancellationToken);
                OnDataUpdated("Station", stationCode, "Arrivals", arrivals.Count, arrivals);

                // Get departing trains
                var departures = await _railwayApiService.GetDepartingTrainsAsync(stationCode, fromTime, toTime, cancellationToken);
                OnDataUpdated("Station", stationCode, "Departures", departures.Count, departures);

                _logger.LogInformation("Successfully refreshed data for station {StationCode}: {ArrivalCount} arrivals, {DepartureCount} departures",
                    stationCode, arrivals.Count, departures.Count);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing data for station {StationCode}", stationCode);
                OnRefreshError("Station", stationCode, "ManualRefresh", ex);
                return false;
            }
        }

        public async Task<bool> RefreshTrainDataAsync(string trainNumber, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Manually refreshing data for train {TrainNumber}", trainNumber);

                // Get train status
                var status = await _railwayApiService.GetLiveTrainStatusAsync(trainNumber, cancellationToken);
                if (status != null)
                {
                    OnDataUpdated("Train", trainNumber, "Status", 1, status);
                }

                // Get train details
                var details = await _railwayApiService.GetTrainDetailsAsync(trainNumber, cancellationToken);
                if (details != null)
                {
                    OnDataUpdated("Train", trainNumber, "Details", 1, details);
                }

                _logger.LogInformation("Successfully refreshed data for train {TrainNumber}", trainNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing data for train {TrainNumber}", trainNumber);
                OnRefreshError("Train", trainNumber, "ManualRefresh", ex);
                return false;
            }
        }

        public async Task<AutoRefreshStatsDto> GetRefreshStatsAsync()
        {
            var stats = new AutoRefreshStatsDto
            {
                TotalConfigurations = _activeConfigurations.Count,
                ActiveConfigurations = _activeConfigurations.Values.Count(c => c.IsActive),
                StationConfigurations = _activeConfigurations.Values.Count(c => c.EntityType == "Station"),
                TrainConfigurations = _activeConfigurations.Values.Count(c => c.EntityType == "Train"),
                TotalFailures = _failureCounts.Values.Sum(),
                LastRefreshTime = _lastRefreshTimes.Values.Any() ? _lastRefreshTimes.Values.Max() : null,
                ServiceStatus = _status.ToString(),
                ServiceUptime = _status == AutoRefreshServiceStatus.Running ? DateTime.UtcNow - (_lastRefreshTimes.Values.Any() ? _lastRefreshTimes.Values.Min() : DateTime.UtcNow) : TimeSpan.Zero
            };

            return stats;
        }

        public async Task<bool> ClearAllConfigurationsAsync()
        {
            try
            {
                // Stop all timers
                await StopAllTimersAsync();

                // Clear all collections
                _activeConfigurations.Clear();
                _lastRefreshTimes.Clear();
                _failureCounts.Clear();

                // Clear database (placeholder for future implementation)
                // Note: You would need to clear AutoRefreshConfig table
                // This is a placeholder for the database operation

                _logger.LogInformation("Cleared all auto-refresh configurations");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all auto-refresh configurations");
                return false;
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();

                foreach (var timer in _refreshTimers.Values)
                {
                    timer?.Dispose();
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
