using System.ComponentModel.DataAnnotations;
using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Configuration
{
    /// <summary>
    /// Configuration class for data synchronization settings
    /// Controls how data is synchronized between local system and Railway API
    /// </summary>
    public class SyncConfiguration
    {
        /// <summary>
        /// Configuration section name in appsettings.json
        /// </summary>
        public const string SectionName = "Synchronization";

        /// <summary>
        /// Full synchronization interval in hours
        /// </summary>
        [Range(1, 168)]
        public int FullSyncIntervalHours { get; set; } = 6;

        /// <summary>
        /// Incremental synchronization interval in minutes
        /// </summary>
        [Range(1, 1440)]
        public int IncrementalSyncIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// Maximum number of sync retry attempts
        /// </summary>
        [Range(0, 10)]
        public int MaxSyncRetries { get; set; } = 3;

        /// <summary>
        /// Enable automatic conflict resolution
        /// </summary>
        public bool EnableConflictResolution { get; set; } = true;

        /// <summary>
        /// Default conflict resolution strategy
        /// </summary>
        public ConflictResolutionStrategy DefaultStrategy { get; set; } = ConflictResolutionStrategy.ApiPriority;

        /// <summary>
        /// Number of records to process in each sync batch
        /// </summary>
        [Range(10, 10000)]
        public int SyncBatchSize { get; set; } = 100;

        /// <summary>
        /// Enable detailed synchronization logging
        /// </summary>
        public bool EnableSyncLogging { get; set; } = true;

        /// <summary>
        /// Enable parallel processing during sync
        /// </summary>
        public bool EnableParallelProcessing { get; set; } = true;

        /// <summary>
        /// Maximum degree of parallelism
        /// </summary>
        [Range(1, 20)]
        public int MaxDegreeOfParallelism { get; set; } = 4;

        /// <summary>
        /// Sync timeout in minutes
        /// </summary>
        [Range(1, 120)]
        public int SyncTimeoutMinutes { get; set; } = 30;

        /// <summary>
        /// Enable sync progress notifications
        /// </summary>
        public bool EnableProgressNotifications { get; set; } = true;

        /// <summary>
        /// Progress notification interval in percentage
        /// </summary>
        [Range(1, 50)]
        public int ProgressNotificationInterval { get; set; } = 10;

        /// <summary>
        /// Data retention settings
        /// </summary>
        public DataRetentionConfig DataRetention { get; set; } = new();

        /// <summary>
        /// Conflict resolution settings
        /// </summary>
        public ConflictResolutionConfig ConflictResolution { get; set; } = new();

        /// <summary>
        /// Performance optimization settings
        /// </summary>
        public PerformanceConfig Performance { get; set; } = new();

        /// <summary>
        /// Sync scheduling settings
        /// </summary>
        public SchedulingConfig Scheduling { get; set; } = new();

        /// <summary>
        /// Validates the synchronization configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (FullSyncIntervalHours < 1 || FullSyncIntervalHours > 168)
                result.Errors.Add("FullSyncIntervalHours must be between 1 and 168");

            if (IncrementalSyncIntervalMinutes < 1 || IncrementalSyncIntervalMinutes > 1440)
                result.Errors.Add("IncrementalSyncIntervalMinutes must be between 1 and 1440");

            if (SyncBatchSize < 10 || SyncBatchSize > 10000)
                result.Errors.Add("SyncBatchSize must be between 10 and 10000");

            if (MaxDegreeOfParallelism < 1 || MaxDegreeOfParallelism > 20)
                result.Errors.Add("MaxDegreeOfParallelism must be between 1 and 20");

            // Validate sub-configurations
            var dataRetentionValidation = DataRetention.Validate();
            result.Errors.AddRange(dataRetentionValidation.Errors);
            result.Warnings.AddRange(dataRetentionValidation.Warnings);

            var conflictResolutionValidation = ConflictResolution.Validate();
            result.Errors.AddRange(conflictResolutionValidation.Errors);
            result.Warnings.AddRange(conflictResolutionValidation.Warnings);

            var performanceValidation = Performance.Validate();
            result.Errors.AddRange(performanceValidation.Errors);
            result.Warnings.AddRange(performanceValidation.Warnings);

            var schedulingValidation = Scheduling.Validate();
            result.Errors.AddRange(schedulingValidation.Errors);
            result.Warnings.AddRange(schedulingValidation.Warnings);

            return result;
        }
    }

    /// <summary>
    /// Data retention configuration
    /// </summary>
    public class DataRetentionConfig
    {
        /// <summary>
        /// Retain schedule data for specified days
        /// </summary>
        [Range(1, 365)]
        public int ScheduleRetentionDays { get; set; } = 30;

        /// <summary>
        /// Retain train status data for specified days
        /// </summary>
        [Range(1, 90)]
        public int TrainStatusRetentionDays { get; set; } = 7;

        /// <summary>
        /// Retain delay information for specified days
        /// </summary>
        [Range(1, 180)]
        public int DelayInfoRetentionDays { get; set; } = 14;

        /// <summary>
        /// Retain sync history for specified days
        /// </summary>
        [Range(1, 365)]
        public int SyncHistoryRetentionDays { get; set; } = 90;

        /// <summary>
        /// Enable automatic cleanup of old data
        /// </summary>
        public bool EnableAutoCleanup { get; set; } = true;

        /// <summary>
        /// Cleanup interval in hours
        /// </summary>
        [Range(1, 168)]
        public int CleanupIntervalHours { get; set; } = 24;

        /// <summary>
        /// Validates the data retention configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (ScheduleRetentionDays < 1 || ScheduleRetentionDays > 365)
                result.Errors.Add("ScheduleRetentionDays must be between 1 and 365");

            if (TrainStatusRetentionDays < 1 || TrainStatusRetentionDays > 90)
                result.Errors.Add("TrainStatusRetentionDays must be between 1 and 90");

            if (DelayInfoRetentionDays < 1 || DelayInfoRetentionDays > 180)
                result.Errors.Add("DelayInfoRetentionDays must be between 1 and 180");

            return result;
        }
    }

    /// <summary>
    /// Conflict resolution configuration
    /// </summary>
    public class ConflictResolutionConfig
    {
        /// <summary>
        /// Enable automatic conflict resolution
        /// </summary>
        public bool EnableAutoResolution { get; set; } = true;

        /// <summary>
        /// Maximum time to spend on conflict resolution in minutes
        /// </summary>
        [Range(1, 60)]
        public int MaxResolutionTimeMinutes { get; set; } = 10;

        /// <summary>
        /// Require manual approval for critical conflicts
        /// </summary>
        public bool RequireManualApprovalForCritical { get; set; } = true;

        /// <summary>
        /// Strategy mappings for different conflict types
        /// </summary>
        public Dictionary<string, ConflictResolutionStrategy> StrategyMappings { get; set; } = new()
        {
            { "ScheduleConflict", ConflictResolutionStrategy.ApiPriority },
            { "DelayConflict", ConflictResolutionStrategy.MostRecent },
            { "PlatformConflict", ConflictResolutionStrategy.Manual },
            { "StatusConflict", ConflictResolutionStrategy.ApiPriority }
        };

        /// <summary>
        /// Enable conflict resolution notifications
        /// </summary>
        public bool EnableNotifications { get; set; } = true;

        /// <summary>
        /// Validates the conflict resolution configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (MaxResolutionTimeMinutes < 1 || MaxResolutionTimeMinutes > 60)
                result.Errors.Add("MaxResolutionTimeMinutes must be between 1 and 60");

            return result;
        }
    }

    /// <summary>
    /// Performance optimization configuration
    /// </summary>
    public class PerformanceConfig
    {
        /// <summary>
        /// Enable database connection pooling
        /// </summary>
        public bool EnableConnectionPooling { get; set; } = true;

        /// <summary>
        /// Maximum database connections
        /// </summary>
        [Range(1, 100)]
        public int MaxDatabaseConnections { get; set; } = 10;

        /// <summary>
        /// Enable query result caching
        /// </summary>
        public bool EnableQueryCaching { get; set; } = true;

        /// <summary>
        /// Query cache expiration in minutes
        /// </summary>
        [Range(1, 1440)]
        public int QueryCacheExpirationMinutes { get; set; } = 30;

        /// <summary>
        /// Enable bulk operations for better performance
        /// </summary>
        public bool EnableBulkOperations { get; set; } = true;

        /// <summary>
        /// Bulk operation batch size
        /// </summary>
        [Range(10, 10000)]
        public int BulkOperationBatchSize { get; set; } = 1000;

        /// <summary>
        /// Enable memory optimization
        /// </summary>
        public bool EnableMemoryOptimization { get; set; } = true;

        /// <summary>
        /// Maximum memory usage in MB
        /// </summary>
        [Range(100, 10000)]
        public int MaxMemoryUsageMB { get; set; } = 1000;

        /// <summary>
        /// Validates the performance configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (MaxDatabaseConnections < 1 || MaxDatabaseConnections > 100)
                result.Errors.Add("MaxDatabaseConnections must be between 1 and 100");

            if (BulkOperationBatchSize < 10 || BulkOperationBatchSize > 10000)
                result.Errors.Add("BulkOperationBatchSize must be between 10 and 10000");

            return result;
        }
    }

    /// <summary>
    /// Sync scheduling configuration
    /// </summary>
    public class SchedulingConfig
    {
        /// <summary>
        /// Enable scheduled synchronization
        /// </summary>
        public bool EnableScheduledSync { get; set; } = true;

        /// <summary>
        /// Sync schedule in cron format
        /// </summary>
        public string CronSchedule { get; set; } = "0 */5 * * * *"; // Every 5 minutes

        /// <summary>
        /// Time zone for scheduling
        /// </summary>
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// Enable sync during maintenance windows only
        /// </summary>
        public bool SyncDuringMaintenanceOnly { get; set; } = false;

        /// <summary>
        /// Maintenance window start time
        /// </summary>
        public TimeSpan MaintenanceWindowStart { get; set; } = new(2, 0, 0); // 2:00 AM

        /// <summary>
        /// Maintenance window end time
        /// </summary>
        public TimeSpan MaintenanceWindowEnd { get; set; } = new(4, 0, 0); // 4:00 AM

        /// <summary>
        /// Days of week when sync is allowed
        /// </summary>
        public List<DayOfWeek> AllowedSyncDays { get; set; } = new()
        {
            DayOfWeek.Monday,
            DayOfWeek.Tuesday,
            DayOfWeek.Wednesday,
            DayOfWeek.Thursday,
            DayOfWeek.Friday,
            DayOfWeek.Saturday,
            DayOfWeek.Sunday
        };

        /// <summary>
        /// Enable sync pause during peak hours
        /// </summary>
        public bool PauseDuringPeakHours { get; set; } = false;

        /// <summary>
        /// Peak hours start time
        /// </summary>
        public TimeSpan PeakHoursStart { get; set; } = new(7, 0, 0); // 7:00 AM

        /// <summary>
        /// Peak hours end time
        /// </summary>
        public TimeSpan PeakHoursEnd { get; set; } = new(19, 0, 0); // 7:00 PM

        /// <summary>
        /// Validates the scheduling configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(CronSchedule))
                result.Errors.Add("CronSchedule is required");

            if (string.IsNullOrWhiteSpace(TimeZone))
                result.Errors.Add("TimeZone is required");

            if (MaintenanceWindowStart >= MaintenanceWindowEnd)
                result.Warnings.Add("MaintenanceWindowStart should be before MaintenanceWindowEnd");

            if (PeakHoursStart >= PeakHoursEnd)
                result.Warnings.Add("PeakHoursStart should be before PeakHoursEnd");

            if (AllowedSyncDays.Count == 0)
                result.Warnings.Add("At least one day should be allowed for synchronization");

            return result;
        }
    }


}
