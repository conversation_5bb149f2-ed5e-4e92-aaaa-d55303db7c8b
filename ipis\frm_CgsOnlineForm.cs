// Decompiled with JetBrains decompiler
// Type: ipis.frm_CgsOnlineForm
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frm_CgsOnlineForm : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("t26")]
  private TextBox _t26;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("t25")]
  private TextBox _t25;
  [AccessedThroughProperty("t24")]
  private TextBox _t24;
  [AccessedThroughProperty("t23")]
  private TextBox _t23;
  [AccessedThroughProperty("t22")]
  private TextBox _t22;
  [AccessedThroughProperty("t21")]
  private TextBox _t21;
  [AccessedThroughProperty("t20")]
  private TextBox _t20;
  [AccessedThroughProperty("t19")]
  private TextBox _t19;
  [AccessedThroughProperty("t18")]
  private TextBox _t18;
  [AccessedThroughProperty("t17")]
  private TextBox _t17;
  [AccessedThroughProperty("t16")]
  private TextBox _t16;
  [AccessedThroughProperty("t15")]
  private TextBox _t15;
  [AccessedThroughProperty("t14")]
  private TextBox _t14;
  [AccessedThroughProperty("t13")]
  private TextBox _t13;
  [AccessedThroughProperty("t12")]
  private TextBox _t12;
  [AccessedThroughProperty("t11")]
  private TextBox _t11;
  [AccessedThroughProperty("t10")]
  private TextBox _t10;
  [AccessedThroughProperty("t9")]
  private TextBox _t9;
  [AccessedThroughProperty("t8")]
  private TextBox _t8;
  [AccessedThroughProperty("t7")]
  private TextBox _t7;
  [AccessedThroughProperty("t6")]
  private TextBox _t6;
  [AccessedThroughProperty("t5")]
  private TextBox _t5;
  [AccessedThroughProperty("t4")]
  private TextBox _t4;
  [AccessedThroughProperty("t3")]
  private TextBox _t3;
  [AccessedThroughProperty("t2")]
  private TextBox _t2;
  [AccessedThroughProperty("t1")]
  private TextBox _t1;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("btnLeft")]
  private Button _btnLeft;
  [AccessedThroughProperty("btnRight")]
  private Button _btnRight;
  [AccessedThroughProperty("btnReverse")]
  private Button _btnReverse;

  [DebuggerNonUserCode]
  static frm_CgsOnlineForm()
  {
  }

  [DebuggerNonUserCode]
  public frm_CgsOnlineForm()
  {
    this.Load += new EventHandler(this.frm_CgsOnlineForm_Load);
    frm_CgsOnlineForm.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frm_CgsOnlineForm.__ENCList)
    {
      if (frm_CgsOnlineForm.__ENCList.Count == frm_CgsOnlineForm.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frm_CgsOnlineForm.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frm_CgsOnlineForm.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frm_CgsOnlineForm.__ENCList[index1] = frm_CgsOnlineForm.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frm_CgsOnlineForm.__ENCList.RemoveRange(index1, checked (frm_CgsOnlineForm.__ENCList.Count - index1));
        frm_CgsOnlineForm.__ENCList.Capacity = frm_CgsOnlineForm.__ENCList.Count;
      }
      frm_CgsOnlineForm.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnOk = new Button();
    this.Panel1 = new Panel();
    this.t26 = new TextBox();
    this.Label2 = new Label();
    this.t25 = new TextBox();
    this.t24 = new TextBox();
    this.t23 = new TextBox();
    this.t22 = new TextBox();
    this.t21 = new TextBox();
    this.t20 = new TextBox();
    this.t19 = new TextBox();
    this.t18 = new TextBox();
    this.t17 = new TextBox();
    this.t16 = new TextBox();
    this.t15 = new TextBox();
    this.t14 = new TextBox();
    this.t13 = new TextBox();
    this.t12 = new TextBox();
    this.t11 = new TextBox();
    this.t10 = new TextBox();
    this.t9 = new TextBox();
    this.t8 = new TextBox();
    this.t7 = new TextBox();
    this.t6 = new TextBox();
    this.t5 = new TextBox();
    this.t4 = new TextBox();
    this.t3 = new TextBox();
    this.t2 = new TextBox();
    this.t1 = new TextBox();
    this.Label1 = new Label();
    this.btnLeft = new Button();
    this.btnRight = new Button();
    this.btnReverse = new Button();
    this.Panel1.SuspendLayout();
    this.SuspendLayout();
    this.btnOk.BackColor = SystemColors.ButtonFace;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    Point point1 = new Point(520, (int) sbyte.MaxValue);
    Point point2 = point1;
    btnOk1.Location = point2;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnOk2.Size = size2;
    this.btnOk.TabIndex = 28;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.Panel1.BackColor = SystemColors.ButtonHighlight;
    this.Panel1.BorderStyle = BorderStyle.Fixed3D;
    this.Panel1.Controls.Add((Control) this.t26);
    this.Panel1.Controls.Add((Control) this.Label2);
    this.Panel1.Controls.Add((Control) this.t25);
    this.Panel1.Controls.Add((Control) this.t24);
    this.Panel1.Controls.Add((Control) this.t23);
    this.Panel1.Controls.Add((Control) this.t22);
    this.Panel1.Controls.Add((Control) this.t21);
    this.Panel1.Controls.Add((Control) this.t20);
    this.Panel1.Controls.Add((Control) this.t19);
    this.Panel1.Controls.Add((Control) this.t18);
    this.Panel1.Controls.Add((Control) this.t17);
    this.Panel1.Controls.Add((Control) this.t16);
    this.Panel1.Controls.Add((Control) this.t15);
    this.Panel1.Controls.Add((Control) this.t14);
    this.Panel1.Controls.Add((Control) this.t13);
    this.Panel1.Controls.Add((Control) this.t12);
    this.Panel1.Controls.Add((Control) this.t11);
    this.Panel1.Controls.Add((Control) this.t10);
    this.Panel1.Controls.Add((Control) this.t9);
    this.Panel1.Controls.Add((Control) this.t8);
    this.Panel1.Controls.Add((Control) this.t7);
    this.Panel1.Controls.Add((Control) this.t6);
    this.Panel1.Controls.Add((Control) this.t5);
    this.Panel1.Controls.Add((Control) this.t4);
    this.Panel1.Controls.Add((Control) this.t3);
    this.Panel1.Controls.Add((Control) this.t2);
    this.Panel1.Controls.Add((Control) this.t1);
    this.Panel1.Controls.Add((Control) this.Label1);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(12, 12);
    Point point3 = point1;
    panel1_1.Location = point3;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(615, 94);
    Size size3 = size1;
    panel1_2.Size = size3;
    this.Panel1.TabIndex = 8;
    this.t26.BackColor = SystemColors.ButtonFace;
    TextBox t26_1 = this.t26;
    point1 = new Point(531, 46);
    Point point4 = point1;
    t26_1.Location = point4;
    this.t26.MaxLength = 3;
    this.t26.Name = "t26";
    TextBox t26_2 = this.t26;
    size1 = new Size(35, 20);
    Size size4 = size1;
    t26_2.Size = size4;
    this.t26.TabIndex = 27;
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(572, 49);
    Point point5 = point1;
    label2_1.Location = point5;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(39, 13);
    Size size5 = size1;
    label2_2.Size = size5;
    this.Label2.TabIndex = 26;
    this.Label2.Text = "Down";
    this.t25.BackColor = SystemColors.ButtonFace;
    TextBox t25_1 = this.t25;
    point1 = new Point(490, 46);
    Point point6 = point1;
    t25_1.Location = point6;
    this.t25.MaxLength = 3;
    this.t25.Name = "t25";
    TextBox t25_2 = this.t25;
    size1 = new Size(35, 20);
    Size size6 = size1;
    t25_2.Size = size6;
    this.t25.TabIndex = 25;
    this.t24.BackColor = SystemColors.ButtonFace;
    TextBox t24_1 = this.t24;
    point1 = new Point(449, 46);
    Point point7 = point1;
    t24_1.Location = point7;
    this.t24.MaxLength = 3;
    this.t24.Name = "t24";
    TextBox t24_2 = this.t24;
    size1 = new Size(35, 20);
    Size size7 = size1;
    t24_2.Size = size7;
    this.t24.TabIndex = 24;
    this.t23.BackColor = SystemColors.ButtonFace;
    TextBox t23_1 = this.t23;
    point1 = new Point(408, 46);
    Point point8 = point1;
    t23_1.Location = point8;
    this.t23.MaxLength = 3;
    this.t23.Name = "t23";
    TextBox t23_2 = this.t23;
    size1 = new Size(35, 20);
    Size size8 = size1;
    t23_2.Size = size8;
    this.t23.TabIndex = 23;
    this.t22.BackColor = SystemColors.ButtonFace;
    TextBox t22_1 = this.t22;
    point1 = new Point(365, 46);
    Point point9 = point1;
    t22_1.Location = point9;
    this.t22.MaxLength = 3;
    this.t22.Name = "t22";
    TextBox t22_2 = this.t22;
    size1 = new Size(35, 20);
    Size size9 = size1;
    t22_2.Size = size9;
    this.t22.TabIndex = 22;
    this.t21.BackColor = SystemColors.ButtonFace;
    TextBox t21_1 = this.t21;
    point1 = new Point(324, 46);
    Point point10 = point1;
    t21_1.Location = point10;
    this.t21.MaxLength = 3;
    this.t21.Name = "t21";
    TextBox t21_2 = this.t21;
    size1 = new Size(35, 20);
    Size size10 = size1;
    t21_2.Size = size10;
    this.t21.TabIndex = 21;
    this.t20.BackColor = SystemColors.ButtonFace;
    TextBox t20_1 = this.t20;
    point1 = new Point(283, 46);
    Point point11 = point1;
    t20_1.Location = point11;
    this.t20.MaxLength = 3;
    this.t20.Name = "t20";
    TextBox t20_2 = this.t20;
    size1 = new Size(35, 20);
    Size size11 = size1;
    t20_2.Size = size11;
    this.t20.TabIndex = 20;
    this.t19.BackColor = SystemColors.ButtonFace;
    TextBox t19_1 = this.t19;
    point1 = new Point(242, 46);
    Point point12 = point1;
    t19_1.Location = point12;
    this.t19.MaxLength = 3;
    this.t19.Name = "t19";
    TextBox t19_2 = this.t19;
    size1 = new Size(35, 20);
    Size size12 = size1;
    t19_2.Size = size12;
    this.t19.TabIndex = 19;
    this.t18.BackColor = SystemColors.ButtonFace;
    TextBox t18_1 = this.t18;
    point1 = new Point(201, 46);
    Point point13 = point1;
    t18_1.Location = point13;
    this.t18.MaxLength = 3;
    this.t18.Name = "t18";
    TextBox t18_2 = this.t18;
    size1 = new Size(35, 20);
    Size size13 = size1;
    t18_2.Size = size13;
    this.t18.TabIndex = 18;
    this.t17.BackColor = SystemColors.ButtonFace;
    TextBox t17_1 = this.t17;
    point1 = new Point(160 /*0xA0*/, 46);
    Point point14 = point1;
    t17_1.Location = point14;
    this.t17.MaxLength = 3;
    this.t17.Name = "t17";
    TextBox t17_2 = this.t17;
    size1 = new Size(35, 20);
    Size size14 = size1;
    t17_2.Size = size14;
    this.t17.TabIndex = 17;
    this.t16.BackColor = SystemColors.ButtonFace;
    TextBox t16_1 = this.t16;
    point1 = new Point(118, 46);
    Point point15 = point1;
    t16_1.Location = point15;
    this.t16.MaxLength = 3;
    this.t16.Name = "t16";
    TextBox t16_2 = this.t16;
    size1 = new Size(35, 20);
    Size size15 = size1;
    t16_2.Size = size15;
    this.t16.TabIndex = 16 /*0x10*/;
    this.t15.BackColor = SystemColors.ButtonFace;
    TextBox t15_1 = this.t15;
    point1 = new Point(77, 46);
    Point point16 = point1;
    t15_1.Location = point16;
    this.t15.MaxLength = 3;
    this.t15.Name = "t15";
    TextBox t15_2 = this.t15;
    size1 = new Size(35, 20);
    Size size16 = size1;
    t15_2.Size = size16;
    this.t15.TabIndex = 15;
    this.t14.BackColor = SystemColors.ButtonFace;
    TextBox t14_1 = this.t14;
    point1 = new Point(36, 46);
    Point point17 = point1;
    t14_1.Location = point17;
    this.t14.MaxLength = 3;
    this.t14.Name = "t14";
    TextBox t14_2 = this.t14;
    size1 = new Size(35, 20);
    Size size17 = size1;
    t14_2.Size = size17;
    this.t14.TabIndex = 14;
    this.t13.BackColor = SystemColors.ButtonFace;
    TextBox t13_1 = this.t13;
    point1 = new Point(531, 17);
    Point point18 = point1;
    t13_1.Location = point18;
    this.t13.MaxLength = 3;
    this.t13.Name = "t13";
    TextBox t13_2 = this.t13;
    size1 = new Size(35, 20);
    Size size18 = size1;
    t13_2.Size = size18;
    this.t13.TabIndex = 13;
    this.t12.BackColor = SystemColors.ButtonFace;
    TextBox t12_1 = this.t12;
    point1 = new Point(490, 17);
    Point point19 = point1;
    t12_1.Location = point19;
    this.t12.MaxLength = 3;
    this.t12.Name = "t12";
    TextBox t12_2 = this.t12;
    size1 = new Size(35, 20);
    Size size19 = size1;
    t12_2.Size = size19;
    this.t12.TabIndex = 12;
    this.t11.BackColor = SystemColors.ButtonFace;
    TextBox t11_1 = this.t11;
    point1 = new Point(449, 17);
    Point point20 = point1;
    t11_1.Location = point20;
    this.t11.MaxLength = 3;
    this.t11.Name = "t11";
    TextBox t11_2 = this.t11;
    size1 = new Size(35, 20);
    Size size20 = size1;
    t11_2.Size = size20;
    this.t11.TabIndex = 11;
    this.t10.BackColor = SystemColors.ButtonFace;
    TextBox t10_1 = this.t10;
    point1 = new Point(408, 17);
    Point point21 = point1;
    t10_1.Location = point21;
    this.t10.MaxLength = 3;
    this.t10.Name = "t10";
    TextBox t10_2 = this.t10;
    size1 = new Size(35, 20);
    Size size21 = size1;
    t10_2.Size = size21;
    this.t10.TabIndex = 10;
    this.t9.BackColor = SystemColors.ButtonFace;
    TextBox t9_1 = this.t9;
    point1 = new Point(365, 17);
    Point point22 = point1;
    t9_1.Location = point22;
    this.t9.MaxLength = 3;
    this.t9.Name = "t9";
    TextBox t9_2 = this.t9;
    size1 = new Size(35, 20);
    Size size22 = size1;
    t9_2.Size = size22;
    this.t9.TabIndex = 9;
    this.t8.BackColor = SystemColors.ButtonFace;
    TextBox t8_1 = this.t8;
    point1 = new Point(324, 17);
    Point point23 = point1;
    t8_1.Location = point23;
    this.t8.MaxLength = 3;
    this.t8.Name = "t8";
    TextBox t8_2 = this.t8;
    size1 = new Size(35, 20);
    Size size23 = size1;
    t8_2.Size = size23;
    this.t8.TabIndex = 8;
    this.t7.BackColor = SystemColors.ButtonFace;
    TextBox t7_1 = this.t7;
    point1 = new Point(283, 17);
    Point point24 = point1;
    t7_1.Location = point24;
    this.t7.MaxLength = 3;
    this.t7.Name = "t7";
    TextBox t7_2 = this.t7;
    size1 = new Size(35, 20);
    Size size24 = size1;
    t7_2.Size = size24;
    this.t7.TabIndex = 7;
    this.t6.BackColor = SystemColors.ButtonFace;
    TextBox t6_1 = this.t6;
    point1 = new Point(242, 17);
    Point point25 = point1;
    t6_1.Location = point25;
    this.t6.MaxLength = 3;
    this.t6.Name = "t6";
    TextBox t6_2 = this.t6;
    size1 = new Size(35, 20);
    Size size25 = size1;
    t6_2.Size = size25;
    this.t6.TabIndex = 6;
    this.t5.BackColor = SystemColors.ButtonFace;
    TextBox t5_1 = this.t5;
    point1 = new Point(201, 17);
    Point point26 = point1;
    t5_1.Location = point26;
    this.t5.MaxLength = 3;
    this.t5.Name = "t5";
    TextBox t5_2 = this.t5;
    size1 = new Size(35, 20);
    Size size26 = size1;
    t5_2.Size = size26;
    this.t5.TabIndex = 5;
    this.t4.BackColor = SystemColors.ButtonFace;
    TextBox t4_1 = this.t4;
    point1 = new Point(160 /*0xA0*/, 17);
    Point point27 = point1;
    t4_1.Location = point27;
    this.t4.MaxLength = 3;
    this.t4.Name = "t4";
    TextBox t4_2 = this.t4;
    size1 = new Size(35, 20);
    Size size27 = size1;
    t4_2.Size = size27;
    this.t4.TabIndex = 4;
    this.t3.BackColor = SystemColors.ButtonFace;
    TextBox t3_1 = this.t3;
    point1 = new Point(118, 17);
    Point point28 = point1;
    t3_1.Location = point28;
    this.t3.MaxLength = 3;
    this.t3.Name = "t3";
    TextBox t3_2 = this.t3;
    size1 = new Size(35, 20);
    Size size28 = size1;
    t3_2.Size = size28;
    this.t3.TabIndex = 3;
    this.t2.BackColor = SystemColors.ButtonFace;
    TextBox t2_1 = this.t2;
    point1 = new Point(77, 17);
    Point point29 = point1;
    t2_1.Location = point29;
    this.t2.MaxLength = 3;
    this.t2.Name = "t2";
    TextBox t2_2 = this.t2;
    size1 = new Size(35, 20);
    Size size29 = size1;
    t2_2.Size = size29;
    this.t2.TabIndex = 2;
    this.t1.BackColor = SystemColors.ButtonFace;
    TextBox t1_1 = this.t1;
    point1 = new Point(36, 17);
    Point point30 = point1;
    t1_1.Location = point30;
    this.t1.MaxLength = 3;
    this.t1.Name = "t1";
    TextBox t1_2 = this.t1;
    size1 = new Size(35, 20);
    Size size30 = size1;
    t1_2.Size = size30;
    this.t1.TabIndex = 1;
    this.Label1.AutoSize = true;
    this.Label1.BackColor = SystemColors.ButtonHighlight;
    this.Label1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(3, 17);
    Point point31 = point1;
    label1_1.Location = point31;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(24, 13);
    Size size31 = size1;
    label1_2.Size = size31;
    this.Label1.TabIndex = 0;
    this.Label1.Text = "UP";
    this.btnLeft.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnLeft1 = this.btnLeft;
    point1 = new Point(122, 130);
    Point point32 = point1;
    btnLeft1.Location = point32;
    this.btnLeft.Name = "btnLeft";
    Button btnLeft2 = this.btnLeft;
    size1 = new Size(75, 23);
    Size size32 = size1;
    btnLeft2.Size = size32;
    this.btnLeft.TabIndex = 29;
    this.btnLeft.Text = "<<";
    this.btnLeft.UseVisualStyleBackColor = true;
    this.btnRight.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnRight1 = this.btnRight;
    point1 = new Point(256 /*0x0100*/, 129);
    Point point33 = point1;
    btnRight1.Location = point33;
    this.btnRight.Name = "btnRight";
    Button btnRight2 = this.btnRight;
    size1 = new Size(75, 23);
    Size size33 = size1;
    btnRight2.Size = size33;
    this.btnRight.TabIndex = 30;
    this.btnRight.Text = ">>";
    this.btnRight.UseVisualStyleBackColor = true;
    this.btnReverse.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnReverse1 = this.btnReverse;
    point1 = new Point(382, 129);
    Point point34 = point1;
    btnReverse1.Location = point34;
    this.btnReverse.Name = "btnReverse";
    Button btnReverse2 = this.btnReverse;
    size1 = new Size(75, 23);
    Size size34 = size1;
    btnReverse2.Size = size34;
    this.btnReverse.TabIndex = 31 /*0x1F*/;
    this.btnReverse.Text = "Reverse";
    this.btnReverse.UseVisualStyleBackColor = true;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(647, 189);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnReverse);
    this.Controls.Add((Control) this.btnRight);
    this.Controls.Add((Control) this.btnLeft);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.Panel1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frm_CgsOnlineForm";
    this.Text = "CGS";
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    this.ResumeLayout(false);
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual TextBox t26
  {
    [DebuggerNonUserCode] get { return this._t26; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t26 = value; }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual TextBox t25
  {
    [DebuggerNonUserCode] get { return this._t25; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t25 = value; }
  }

  internal virtual TextBox t24
  {
    [DebuggerNonUserCode] get { return this._t24; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t24 = value; }
  }

  internal virtual TextBox t23
  {
    [DebuggerNonUserCode] get { return this._t23; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t23 = value; }
  }

  internal virtual TextBox t22
  {
    [DebuggerNonUserCode] get { return this._t22; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t22 = value; }
  }

  internal virtual TextBox t21
  {
    [DebuggerNonUserCode] get { return this._t21; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t21 = value; }
  }

  internal virtual TextBox t20
  {
    [DebuggerNonUserCode] get { return this._t20; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t20 = value; }
  }

  internal virtual TextBox t19
  {
    [DebuggerNonUserCode] get { return this._t19; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t19 = value; }
  }

  internal virtual TextBox t18
  {
    [DebuggerNonUserCode] get { return this._t18; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t18 = value; }
  }

  internal virtual TextBox t17
  {
    [DebuggerNonUserCode] get { return this._t17; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t17 = value; }
  }

  internal virtual TextBox t16
  {
    [DebuggerNonUserCode] get { return this._t16; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t16 = value; }
  }

  internal virtual TextBox t15
  {
    [DebuggerNonUserCode] get { return this._t15; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t15 = value; }
  }

  internal virtual TextBox t14
  {
    [DebuggerNonUserCode] get { return this._t14; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t14 = value; }
  }

  internal virtual TextBox t13
  {
    [DebuggerNonUserCode] get { return this._t13; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t13 = value; }
  }

  internal virtual TextBox t12
  {
    [DebuggerNonUserCode] get { return this._t12; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t12 = value; }
  }

  internal virtual TextBox t11
  {
    [DebuggerNonUserCode] get { return this._t11; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t11 = value; }
  }

  internal virtual TextBox t10
  {
    [DebuggerNonUserCode] get { return this._t10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t10 = value; }
  }

  internal virtual TextBox t9
  {
    [DebuggerNonUserCode] get { return this._t9; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t9 = value; }
  }

  internal virtual TextBox t8
  {
    [DebuggerNonUserCode] get { return this._t8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t8 = value; }
  }

  internal virtual TextBox t7
  {
    [DebuggerNonUserCode] get { return this._t7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t7 = value; }
  }

  internal virtual TextBox t6
  {
    [DebuggerNonUserCode] get { return this._t6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t6 = value; }
  }

  internal virtual TextBox t5
  {
    [DebuggerNonUserCode] get { return this._t5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t5 = value; }
  }

  internal virtual TextBox t4
  {
    [DebuggerNonUserCode] get { return this._t4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t4 = value; }
  }

  internal virtual TextBox t3
  {
    [DebuggerNonUserCode] get { return this._t3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t3 = value; }
  }

  internal virtual TextBox t2
  {
    [DebuggerNonUserCode] get { return this._t2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t2 = value; }
  }

  internal virtual TextBox t1
  {
    [DebuggerNonUserCode] get { return this._t1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t1 = value; }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Button btnLeft
  {
    [DebuggerNonUserCode] get { return this._btnLeft; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnLeft_Click);
      if (this._btnLeft != null)
        this._btnLeft.Click -= eventHandler;
      this._btnLeft = value;
      if (this._btnLeft == null)
        return;
      this._btnLeft.Click += eventHandler;
    }
  }

  internal virtual Button btnRight
  {
    [DebuggerNonUserCode] get { return this._btnRight; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnRight_Click);
      if (this._btnRight != null)
        this._btnRight.Click -= eventHandler;
      this._btnRight = value;
      if (this._btnRight == null)
        return;
      this._btnRight.Click += eventHandler;
    }
  }

  internal virtual Button btnReverse
  {
    [DebuggerNonUserCode] get { return this._btnReverse; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnReverse_Click);
      if (this._btnReverse != null)
        this._btnReverse.Click -= eventHandler;
      this._btnReverse = value;
      if (this._btnReverse == null)
        return;
      this._btnReverse.Click += eventHandler;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    try
    {
      int num = 0;
      string[] strArray = new string[27]
      {
        this.t1.Text,
        this.t2.Text,
        this.t3.Text,
        this.t4.Text,
        this.t5.Text,
        this.t6.Text,
        this.t7.Text,
        this.t8.Text,
        this.t9.Text,
        this.t10.Text,
        this.t11.Text,
        this.t12.Text,
        this.t13.Text,
        this.t14.Text,
        this.t15.Text,
        this.t16.Text,
        this.t17.Text,
        this.t18.Text,
        this.t19.Text,
        this.t20.Text,
        this.t21.Text,
        this.t22.Text,
        this.t23.Text,
        this.t24.Text,
        this.t25.Text,
        this.t26.Text,
        null
      };
      int cgsSenddata = frmMainFormIPIS.cgs_senddata;
      int index1 = 0;
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index1] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index1], "NULL", false) != 0 ? this.t1.Text : DBNull.Value.ToString();
      int index2 = checked (index1 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index2] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index2], "NULL", false) != 0 ? this.t2.Text : DBNull.Value.ToString();
      int index3 = checked (index2 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index3] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index3], "NULL", false) != 0 ? this.t3.Text : DBNull.Value.ToString();
      int index4 = checked (index3 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index4] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index4], "NULL", false) != 0 ? this.t4.Text : DBNull.Value.ToString();
      int index5 = checked (index4 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index5] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index5], "NULL", false) != 0 ? this.t5.Text : DBNull.Value.ToString();
      int index6 = checked (index5 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index6] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index6], "NULL", false) != 0 ? this.t6.Text : DBNull.Value.ToString();
      int index7 = checked (index6 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index7] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index7], "NULL", false) != 0 ? this.t7.Text : DBNull.Value.ToString();
      int index8 = checked (index7 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index8] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index8], "NULL", false) != 0 ? this.t8.Text : DBNull.Value.ToString();
      int index9 = checked (index8 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index9] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index9], "NULL", false) != 0 ? this.t9.Text : DBNull.Value.ToString();
      int index10 = checked (index9 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index10] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index10], "NULL", false) != 0 ? this.t10.Text : DBNull.Value.ToString();
      int index11 = checked (index10 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index11] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index11], "NULL", false) != 0 ? this.t11.Text : DBNull.Value.ToString();
      int index12 = checked (index11 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index12] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index12], "NULL", false) != 0 ? this.t12.Text : DBNull.Value.ToString();
      int index13 = checked (index12 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index13] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index13], "NULL", false) != 0 ? this.t13.Text : DBNull.Value.ToString();
      int index14 = checked (index13 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index14] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index14], "NULL", false) != 0 ? this.t14.Text : DBNull.Value.ToString();
      int index15 = checked (index14 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index15] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index15], "NULL", false) != 0 ? this.t15.Text : DBNull.Value.ToString();
      int index16 = checked (index15 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index16] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index16], "NULL", false) != 0 ? this.t16.Text : DBNull.Value.ToString();
      int index17 = checked (index16 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index17] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index17], "NULL", false) != 0 ? this.t17.Text : DBNull.Value.ToString();
      int index18 = checked (index17 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index18] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index18], "NULL", false) != 0 ? this.t18.Text : DBNull.Value.ToString();
      int index19 = checked (index18 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index19] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index19], "NULL", false) != 0 ? this.t19.Text : DBNull.Value.ToString();
      int index20 = checked (index19 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index20] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index20], "NULL", false) != 0 ? this.t20.Text : DBNull.Value.ToString();
      int index21 = checked (index20 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index21] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index21], "NULL", false) != 0 ? this.t21.Text : DBNull.Value.ToString();
      int index22 = checked (index21 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index22] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index22], "NULL", false) != 0 ? this.t22.Text : DBNull.Value.ToString();
      int index23 = checked (index22 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index23] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index23], "NULL", false) != 0 ? this.t23.Text : DBNull.Value.ToString();
      int index24 = checked (index23 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index24] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index24], "NULL", false) != 0 ? this.t24.Text : DBNull.Value.ToString();
      int index25 = checked (index24 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index25] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index25], "NULL", false) != 0 ? this.t25.Text : DBNull.Value.ToString();
      int index26 = checked (index25 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index26] = Operators.CompareString(frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_values[index26], "NULL", false) != 0 ? this.t26.Text : DBNull.Value.ToString();
      num = checked (index26 + 1);
      frmMainFormIPIS.online_train_data[cgsSenddata].cgs_array_modified = true;
      frmMainFormIPIS.cgs_online_train_chk[cgsSenddata] = true;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.Close();
  }

  private void btnLeft_Click(object sender, EventArgs e)
  {
    this.t1.Text = this.t2.Text;
    this.t2.Text = this.t3.Text;
    this.t3.Text = this.t4.Text;
    this.t4.Text = this.t5.Text;
    this.t5.Text = this.t6.Text;
    this.t6.Text = this.t7.Text;
    this.t7.Text = this.t8.Text;
    this.t8.Text = this.t9.Text;
    this.t9.Text = this.t10.Text;
    this.t10.Text = this.t11.Text;
    this.t11.Text = this.t12.Text;
    this.t12.Text = this.t13.Text;
    this.t13.Text = this.t14.Text;
    this.t14.Text = this.t15.Text;
    this.t15.Text = this.t16.Text;
    this.t16.Text = this.t17.Text;
    this.t17.Text = this.t18.Text;
    this.t18.Text = this.t19.Text;
    this.t19.Text = this.t20.Text;
    this.t20.Text = this.t21.Text;
    this.t21.Text = this.t22.Text;
    this.t22.Text = this.t23.Text;
    this.t23.Text = this.t24.Text;
    this.t24.Text = this.t25.Text;
    this.t25.Text = this.t26.Text;
    this.t26.Text = string.Empty;
  }

  private void btnRight_Click(object sender, EventArgs e)
  {
    this.t26.Text = this.t25.Text;
    this.t25.Text = this.t24.Text;
    this.t24.Text = this.t23.Text;
    this.t23.Text = this.t22.Text;
    this.t22.Text = this.t21.Text;
    this.t21.Text = this.t20.Text;
    this.t20.Text = this.t19.Text;
    this.t19.Text = this.t18.Text;
    this.t18.Text = this.t17.Text;
    this.t17.Text = this.t16.Text;
    this.t16.Text = this.t15.Text;
    this.t15.Text = this.t14.Text;
    this.t14.Text = this.t13.Text;
    this.t13.Text = this.t12.Text;
    this.t12.Text = this.t11.Text;
    this.t11.Text = this.t10.Text;
    this.t10.Text = this.t9.Text;
    this.t9.Text = this.t8.Text;
    this.t8.Text = this.t7.Text;
    this.t7.Text = this.t6.Text;
    this.t6.Text = this.t5.Text;
    this.t5.Text = this.t4.Text;
    this.t4.Text = this.t3.Text;
    this.t3.Text = this.t2.Text;
    this.t2.Text = this.t1.Text;
    this.t1.Text = string.Empty;
  }

  private void btnReverse_Click(object sender, EventArgs e)
  {
    string[] strArray = new string[27]
    {
      this.t1.Text,
      this.t2.Text,
      this.t3.Text,
      this.t4.Text,
      this.t5.Text,
      this.t6.Text,
      this.t7.Text,
      this.t8.Text,
      this.t9.Text,
      this.t10.Text,
      this.t11.Text,
      this.t12.Text,
      this.t13.Text,
      this.t14.Text,
      this.t15.Text,
      this.t16.Text,
      this.t17.Text,
      this.t18.Text,
      this.t19.Text,
      this.t20.Text,
      this.t21.Text,
      this.t22.Text,
      this.t23.Text,
      this.t24.Text,
      this.t25.Text,
      this.t26.Text,
      null
    };
    this.t26.Text = strArray[0];
    this.t25.Text = strArray[1];
    this.t24.Text = strArray[2];
    this.t23.Text = strArray[3];
    this.t22.Text = strArray[4];
    this.t21.Text = strArray[5];
    this.t20.Text = strArray[6];
    this.t19.Text = strArray[7];
    this.t18.Text = strArray[8];
    this.t17.Text = strArray[9];
    this.t16.Text = strArray[10];
    this.t15.Text = strArray[11];
    this.t14.Text = strArray[12];
    this.t13.Text = strArray[13];
    this.t12.Text = strArray[14];
    this.t11.Text = strArray[15];
    this.t10.Text = strArray[16 /*0x10*/];
    this.t9.Text = strArray[17];
    this.t8.Text = strArray[18];
    this.t7.Text = strArray[19];
    this.t6.Text = strArray[20];
    this.t5.Text = strArray[21];
    this.t4.Text = strArray[22];
    this.t3.Text = strArray[23];
    this.t2.Text = strArray[24];
    this.t1.Text = strArray[25];
  }

  private void frm_CgsOnlineForm_Load(object sender, EventArgs e)
  {
    this.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].train_no;
    if (Operators.CompareString(frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].AD, frmMainFormIPIS.pre_cgs_inf[frmMainFormIPIS.cgs_senddata], false) == 0 | (Operators.CompareString(frmMainFormIPIS.pre_cgs_inf[frmMainFormIPIS.cgs_senddata], "", false) == 0 | Operators.CompareString(frmMainFormIPIS.pre_cgs_inf[frmMainFormIPIS.cgs_senddata], (string) null, false) == 0) & Operators.CompareString(frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].AD, "A", false) == 0)
    {
      this.t1.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[0];
      this.t2.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[1];
      this.t3.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[2];
      this.t4.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[3];
      this.t5.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[4];
      this.t6.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[5];
      this.t7.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[6];
      this.t8.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[7];
      this.t9.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[8];
      this.t10.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[9];
      this.t11.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[10];
      this.t12.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[11];
      this.t13.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[12];
      this.t14.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[13];
      this.t15.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[14];
      this.t16.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[15];
      this.t17.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[16 /*0x10*/];
      this.t18.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[17];
      this.t19.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[18];
      this.t20.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[19];
      this.t21.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[20];
      this.t22.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[21];
      this.t23.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[22];
      this.t24.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[23];
      this.t25.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[24];
      this.t26.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[25];
    }
    else
    {
      this.t26.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[0];
      this.t25.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[1];
      this.t24.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[2];
      this.t23.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[3];
      this.t22.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[4];
      this.t21.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[5];
      this.t20.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[6];
      this.t19.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[7];
      this.t18.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[8];
      this.t17.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[9];
      this.t16.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[10];
      this.t15.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[11];
      this.t14.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[12];
      this.t13.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[13];
      this.t12.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[14];
      this.t11.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[15];
      this.t10.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[16 /*0x10*/];
      this.t9.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[17];
      this.t8.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[18];
      this.t7.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[19];
      this.t6.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[20];
      this.t5.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[21];
      this.t4.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[22];
      this.t3.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[23];
      this.t2.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[24];
      this.t1.Text = frmMainFormIPIS.online_train_data[frmMainFormIPIS.cgs_senddata].cgs_array_values[25];
    }
  }
}

}