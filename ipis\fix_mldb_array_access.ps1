# Fix all mldb_dis_brd array access issues

$file = "frmDisplayBoardSettings.cs"
if (Test-Path $file) {
    Write-Host "Fixing mldb_dis_brd array access in: $file"
    $content = Get-Content $file -Raw
    
    # Fix all mldb_dis_brd[x,y] to mldb_dis_brd.mdlb[y]
    $content = $content -replace 'mldb_dis_brd\[0,\s*([^\]]+)\]', 'mldb_dis_brd.mdlb[$1]'
    $content = $content -replace 'mldb_dis_brd\[1,\s*([^\]]+)\]', 'mldb_dis_brd.mdlb[$1]'
    $content = $content -replace 'mldb_dis_brd\[([^,]+),\s*([^\]]+)\]', 'mldb_dis_brd.mdlb[$2]'
    
    Set-Content -Path $file -Value $content -NoNewline
    Write-Host "  - Fixed mldb_dis_brd array access"
}

Write-Host "mldb_dis_brd array access fixes complete!"
