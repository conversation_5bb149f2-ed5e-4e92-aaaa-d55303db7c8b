using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// Data Transfer Object for data conflicts between local and API data
    /// Manages conflict detection and resolution processes
    /// </summary>
    public class DataConflict
    {
        /// <summary>
        /// Unique identifier for the conflict
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Type of entity where conflict occurred
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EntityType { get; set; } = string.Empty;

        /// <summary>
        /// Identifier of the conflicting entity
        /// </summary>
        [Required]
        [StringLength(100)]
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Type of conflict (Value, Status, Timing, etc.)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ConflictType { get; set; } = string.Empty;

        /// <summary>
        /// Field or property where conflict occurred
        /// </summary>
        [StringLength(100)]
        public string ConflictField { get; set; } = string.Empty;

        /// <summary>
        /// Local system value
        /// </summary>
        public object LocalValue { get; set; } = new();

        /// <summary>
        /// Railway API value
        /// </summary>
        public object ApiValue { get; set; } = new();

        /// <summary>
        /// Timestamp when conflict was detected
        /// </summary>
        public DateTime DetectedAt { get; set; }

        /// <summary>
        /// Severity level of the conflict
        /// </summary>
        public ConflictSeverity Severity { get; set; }

        /// <summary>
        /// Description of the conflict
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the conflict has been resolved
        /// </summary>
        public bool IsResolved { get; set; }

        /// <summary>
        /// Resolution strategy used
        /// </summary>
        public ConflictResolutionStrategy? ResolutionStrategy { get; set; }

        /// <summary>
        /// Resolved value
        /// </summary>
        public object? ResolvedValue { get; set; }

        /// <summary>
        /// Timestamp when conflict was resolved
        /// </summary>
        public DateTime? ResolvedAt { get; set; }

        /// <summary>
        /// User who resolved the conflict (for manual resolution)
        /// </summary>
        [StringLength(100)]
        public string? ResolvedBy { get; set; }

        /// <summary>
        /// Resolution notes or comments
        /// </summary>
        [StringLength(500)]
        public string? ResolutionNotes { get; set; }

        /// <summary>
        /// Indicates if manual intervention is required
        /// </summary>
        public bool RequiresManualIntervention { get; set; }

        /// <summary>
        /// Impact assessment of the conflict
        /// </summary>
        [StringLength(200)]
        public string ImpactAssessment { get; set; } = string.Empty;

        /// <summary>
        /// Recommended action for resolution
        /// </summary>
        [StringLength(200)]
        public string RecommendedAction { get; set; } = string.Empty;

        /// <summary>
        /// Additional metadata about the conflict
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Computed property: Time since conflict was detected
        /// </summary>
        public TimeSpan TimeSinceDetected => DateTime.UtcNow - DetectedAt;

        /// <summary>
        /// Computed property: Resolution duration
        /// </summary>
        public TimeSpan? ResolutionDuration => IsResolved && ResolvedAt.HasValue 
            ? ResolvedAt.Value - DetectedAt 
            : null;

        /// <summary>
        /// Computed property: Severity color for UI display
        /// </summary>
        public string SeverityColor => Severity switch
        {
            ConflictSeverity.Low => "#28a745",      // Green
            ConflictSeverity.Medium => "#ffc107",   // Yellow
            ConflictSeverity.High => "#fd7e14",     // Orange
            ConflictSeverity.Critical => "#dc3545", // Red
            _ => "#6c757d"                          // Gray
        };

        /// <summary>
        /// Validates the conflict data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(EntityType) &&
                   !string.IsNullOrWhiteSpace(EntityId) &&
                   !string.IsNullOrWhiteSpace(ConflictType) &&
                   DetectedAt != default;
        }

        /// <summary>
        /// Gets a summary of the conflict
        /// </summary>
        /// <returns>Conflict summary string</returns>
        public string GetSummary()
        {
            var summary = $"{ConflictType} conflict in {EntityType} ({EntityId})";
            
            if (!string.IsNullOrWhiteSpace(ConflictField))
                summary += $" - {ConflictField}";
            
            summary += $" [{Severity}]";
            
            if (IsResolved)
                summary += " [RESOLVED]";
            else if (RequiresManualIntervention)
                summary += " [MANUAL REQUIRED]";
            
            return summary;
        }

        /// <summary>
        /// Returns a string representation of the conflict
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }

    /// <summary>
    /// Data Transfer Object for conflict resolution results
    /// Contains information about how a conflict was resolved
    /// </summary>
    public class ConflictResolutionResult
    {
        /// <summary>
        /// Indicates if the conflict was successfully resolved
        /// </summary>
        public bool IsResolved { get; set; }

        /// <summary>
        /// Strategy used for resolution
        /// </summary>
        public ConflictResolutionStrategy StrategyUsed { get; set; }

        /// <summary>
        /// Final resolved value
        /// </summary>
        public object ResolvedValue { get; set; } = new();

        /// <summary>
        /// Reason for the resolution choice
        /// </summary>
        [StringLength(500)]
        public string ResolutionReason { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when resolution was completed
        /// </summary>
        public DateTime ResolvedAt { get; set; }

        /// <summary>
        /// User who performed the resolution (for manual resolution)
        /// </summary>
        [StringLength(100)]
        public string? ResolvedBy { get; set; }

        /// <summary>
        /// Confidence level in the resolution (0-100)
        /// </summary>
        [Range(0, 100)]
        public int ConfidenceLevel { get; set; }

        /// <summary>
        /// Additional notes about the resolution
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Indicates if the resolution should be applied to similar conflicts
        /// </summary>
        public bool ApplyToSimilarConflicts { get; set; }

        /// <summary>
        /// Validation errors if resolution failed
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Computed property: Indicates if resolution is highly confident
        /// </summary>
        public bool IsHighConfidence => ConfidenceLevel >= 80;

        /// <summary>
        /// Gets a summary of the resolution result
        /// </summary>
        /// <returns>Resolution summary string</returns>
        public string GetSummary()
        {
            var summary = IsResolved ? "Resolved" : "Failed to resolve";
            summary += $" using {StrategyUsed} strategy";
            
            if (ConfidenceLevel > 0)
                summary += $" (Confidence: {ConfidenceLevel}%)";
            
            if (!string.IsNullOrWhiteSpace(ResolvedBy))
                summary += $" by {ResolvedBy}";
            
            return summary;
        }

        /// <summary>
        /// Returns a string representation of the resolution result
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }

    /// <summary>
    /// Data Transfer Object for conflict resolution configuration
    /// Defines how conflicts should be resolved
    /// </summary>
    public class ConflictResolution
    {
        /// <summary>
        /// Conflict identifier to resolve
        /// </summary>
        [Required]
        public string ConflictId { get; set; } = string.Empty;

        /// <summary>
        /// Resolution strategy to apply
        /// </summary>
        public ConflictResolutionStrategy Strategy { get; set; }

        /// <summary>
        /// Manual value to use (for manual resolution)
        /// </summary>
        public object? ManualValue { get; set; }

        /// <summary>
        /// User performing the resolution
        /// </summary>
        [StringLength(100)]
        public string? ResolvedBy { get; set; }

        /// <summary>
        /// Notes about the resolution decision
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Indicates if this resolution should be remembered for similar conflicts
        /// </summary>
        public bool RememberForSimilar { get; set; }

        /// <summary>
        /// Validates the resolution configuration
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(ConflictId) &&
                   (Strategy != ConflictResolutionStrategy.Manual || ManualValue != null);
        }
    }

    /// <summary>
    /// Enumeration for conflict severity levels
    /// </summary>
    public enum ConflictSeverity
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// Enumeration for conflict resolution strategies
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        /// <summary>
        /// Use the value from the Railway API
        /// </summary>
        ApiPriority,
        
        /// <summary>
        /// Use the local system value
        /// </summary>
        LocalPriority,
        
        /// <summary>
        /// Use the most recent value based on timestamp
        /// </summary>
        MostRecent,
        
        /// <summary>
        /// Use the oldest value based on timestamp
        /// </summary>
        Oldest,
        
        /// <summary>
        /// Require manual intervention to resolve
        /// </summary>
        Manual,
        
        /// <summary>
        /// Merge values if possible
        /// </summary>
        Merge,
        
        /// <summary>
        /// Ignore the conflict and keep current value
        /// </summary>
        Ignore,
        
        /// <summary>
        /// Use a predefined business rule
        /// </summary>
        BusinessRule
    }

    /// <summary>
    /// Enumeration for conflict types
    /// </summary>
    public enum ConflictType
    {
        ValueMismatch,
        StatusDifference,
        TimingConflict,
        PlatformAssignment,
        DelayInformation,
        TrainStatus,
        ScheduleChange,
        Cancellation,
        DataIntegrity,
        Unknown
    }
}
