<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <ApplicationIcon>Resources\ipis.ico</ApplicationIcon>
    <AssemblyTitle>IPIS Windows Application</AssemblyTitle>
    <AssemblyDescription>Integrated Passenger Information System for Railway Operations</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © Railway IPIS System 2024</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.25">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.25">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />

    <!-- Additional Dependencies for Comprehensive IPIS System -->
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="FluentValidation" Version="11.8.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.8.0" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.IO.Ports" Version="6.0.0" />
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ipis.db">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Icons\" />
    <Folder Include="Resources\Voice\" />
    <Folder Include="Resources\Fonts\" />
    <Folder Include="Logs\" />
    <Folder Include="Backup\" />
    <Folder Include="Import\" />
    <Folder Include="Export\" />
    <Folder Include="Common\Extensions\" />
    <Folder Include="Common\Helpers\" />
    <Folder Include="Common\Constants\" />
    <Folder Include="Common\Exceptions\" />
    <Folder Include="Models\Enums\" />
    <Folder Include="Models\DTOs\" />
    <Folder Include="Data\Configurations\" />
    <Folder Include="Data\Migrations\" />
    <Folder Include="Services\Interfaces\" />
    <Folder Include="Services\Implementations\" />
    <Folder Include="Services\Background\" />
    <Folder Include="Communication\Protocols\" />
    <Folder Include="Communication\Devices\" />
    <Folder Include="Voice\Engine\" />
    <Folder Include="Voice\Files\" />
    <Folder Include="Advertisement\Management\" />
    <Folder Include="Advertisement\Content\" />
  </ItemGroup>

</Project>
