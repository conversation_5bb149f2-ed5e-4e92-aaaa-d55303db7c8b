using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models
{
    /// <summary>
    /// Represents a train schedule entry in the IPIS system
    /// </summary>
    [Table("Schedules")]
    public class Schedule
    {
        /// <summary>
        /// Gets or sets the unique identifier for the schedule
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the train ID for this schedule
        /// </summary>
        [Required]
        public int TrainId { get; set; }

        /// <summary>
        /// Gets or sets the station ID for this schedule
        /// </summary>
        [Required]
        public int StationId { get; set; }

        /// <summary>
        /// Gets or sets the platform ID for this schedule (optional)
        /// </summary>
        public int? PlatformId { get; set; }

        /// <summary>
        /// Gets or sets the scheduled arrival time
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Gets or sets the scheduled departure time
        /// </summary>
        public DateTime? ScheduledDeparture { get; set; }

        /// <summary>
        /// Gets or sets the actual arrival time
        /// </summary>
        public DateTime? ActualArrival { get; set; }

        /// <summary>
        /// Gets or sets the actual departure time
        /// </summary>
        public DateTime? ActualDeparture { get; set; }

        /// <summary>
        /// Gets or sets the current status of the schedule
        /// </summary>
        public ScheduleStatus Status { get; set; } = ScheduleStatus.Scheduled;

        /// <summary>
        /// Gets or sets the delay in minutes (positive for delays, negative for early)
        /// </summary>
        public int DelayMinutes { get; set; } = 0;

        /// <summary>
        /// Gets or sets additional remarks or notes
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the schedule was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gets or sets the date and time when the schedule was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for the train associated with this schedule
        /// </summary>
        [ForeignKey("TrainId")]
        public virtual Train Train { get; set; } = null!;

        /// <summary>
        /// Navigation property for the station associated with this schedule
        /// </summary>
        [ForeignKey("StationId")]
        public virtual Station Station { get; set; } = null!;

        /// <summary>
        /// Navigation property for the platform associated with this schedule
        /// </summary>
        [ForeignKey("PlatformId")]
        public virtual Platform? Platform { get; set; }
    }
}
