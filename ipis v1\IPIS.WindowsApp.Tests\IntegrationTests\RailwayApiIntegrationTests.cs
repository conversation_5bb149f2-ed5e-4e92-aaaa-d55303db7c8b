using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Xunit;
using Xunit.Abstractions;
using IPIS.WindowsApp.Configuration;
using IPIS.WindowsApp.Services.Interfaces;
using IPIS.WindowsApp.Services.Implementations;
using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Tests.IntegrationTests
{
    /// <summary>
    /// Integration tests for Railway API service
    /// Tests the complete Railway API integration functionality
    /// </summary>
    public class RailwayApiIntegrationTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly ServiceProvider _serviceProvider;
        private readonly IRailwayApiService _railwayApiService;
        private readonly IScheduleSyncService _scheduleSyncService;
        private readonly ILogger<RailwayApiIntegrationTests> _logger;

        public RailwayApiIntegrationTests(ITestOutputHelper output)
        {
            _output = output;

            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Test.json", optional: true)
                .Build();

            // Setup dependency injection
            var services = new ServiceCollection();
            
            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            // Add configuration
            services.Configure<RailwayApiConfiguration>(configuration.GetSection("RailwayApi"));
            services.Configure<SyncConfiguration>(configuration.GetSection("Synchronization"));

            // Add HTTP client
            services.AddHttpClient<IRailwayApiService, RailwayApiService>();

            // Add services
            services.AddScoped<IRailwayApiService, RailwayApiService>();
            services.AddScoped<IScheduleSyncService, ScheduleSyncService>();

            _serviceProvider = services.BuildServiceProvider();
            _railwayApiService = _serviceProvider.GetRequiredService<IRailwayApiService>();
            _scheduleSyncService = _serviceProvider.GetRequiredService<IScheduleSyncService>();
            _logger = _serviceProvider.GetRequiredService<ILogger<RailwayApiIntegrationTests>>();

            _output.WriteLine("Railway API Integration Tests initialized");
        }

        #region API Health Tests

        [Fact]
        public async Task GetApiHealthAsync_ShouldReturnHealthStatus()
        {
            // Arrange
            _output.WriteLine("Testing API health check...");

            // Act
            var health = await _railwayApiService.GetApiHealthAsync();

            // Assert
            Assert.NotNull(health);
            Assert.True(health.CheckTime > DateTime.MinValue);
            Assert.True(health.ResponseTimeMs >= 0);
            Assert.NotEmpty(health.Status);

            _output.WriteLine($"API Health: {health.Status}, Response Time: {health.ResponseTimeMs}ms");
            _logger.LogInformation("API health check completed: {Status}", health.Status);
        }

        [Fact]
        public async Task TestConnectionAsync_ShouldReturnConnectionStatus()
        {
            // Arrange
            _output.WriteLine("Testing API connection...");

            // Act
            var isConnected = await _railwayApiService.TestConnectionAsync();

            // Assert
            _output.WriteLine($"API Connection Status: {(isConnected ? "Connected" : "Disconnected")}");
            _logger.LogInformation("API connection test completed: {IsConnected}", isConnected);

            // Note: This test may fail if the actual API is not available
            // In a real scenario, you might want to mock this for unit tests
        }

        #endregion

        #region Schedule Operations Tests

        [Fact]
        public async Task GetTrainSchedulesAsync_WithValidStationCode_ShouldReturnSchedules()
        {
            // Arrange
            var stationCode = "DEL"; // Delhi station code
            var date = DateTime.Today;
            _output.WriteLine($"Testing schedule retrieval for station {stationCode} on {date:yyyy-MM-dd}...");

            // Act
            var schedules = await _railwayApiService.GetTrainSchedulesAsync(stationCode, date);

            // Assert
            Assert.NotNull(schedules);
            _output.WriteLine($"Retrieved {schedules.Count} schedules for station {stationCode}");
            _logger.LogInformation("Retrieved {Count} schedules for station {StationCode}", schedules.Count, stationCode);

            // Validate schedule data structure
            foreach (var schedule in schedules.Take(3)) // Check first 3 schedules
            {
                Assert.NotEmpty(schedule.TrainNumber);
                Assert.NotEmpty(schedule.StationCode);
                Assert.True(schedule.IsValid());
                
                _output.WriteLine($"Schedule: Train {schedule.TrainNumber} - {schedule.GetSummary()}");
            }
        }

        [Fact]
        public async Task GetTrainSchedulesAsync_WithMultipleStations_ShouldReturnSchedulesForAllStations()
        {
            // Arrange
            var stationCodes = new List<string> { "DEL", "BOM", "MAA" };
            var date = DateTime.Today;
            _output.WriteLine($"Testing schedule retrieval for {stationCodes.Count} stations...");

            // Act
            var schedulesByStation = await _railwayApiService.GetTrainSchedulesAsync(stationCodes, date);

            // Assert
            Assert.NotNull(schedulesByStation);
            Assert.Equal(stationCodes.Count, schedulesByStation.Count);

            foreach (var stationCode in stationCodes)
            {
                Assert.True(schedulesByStation.ContainsKey(stationCode));
                var schedules = schedulesByStation[stationCode];
                _output.WriteLine($"Station {stationCode}: {schedules.Count} schedules");
            }

            _logger.LogInformation("Retrieved schedules for {Count} stations", schedulesByStation.Count);
        }

        [Fact]
        public async Task GetTrainScheduleAsync_WithSpecificTrain_ShouldReturnScheduleOrNull()
        {
            // Arrange
            var trainNumber = "12345";
            var stationCode = "DEL";
            var date = DateTime.Today;
            _output.WriteLine($"Testing specific schedule retrieval for train {trainNumber} at station {stationCode}...");

            // Act
            var schedule = await _railwayApiService.GetTrainScheduleAsync(trainNumber, stationCode, date);

            // Assert
            if (schedule != null)
            {
                Assert.Equal(trainNumber, schedule.TrainNumber);
                Assert.Equal(stationCode, schedule.StationCode);
                Assert.True(schedule.IsValid());
                _output.WriteLine($"Found schedule: {schedule.GetSummary()}");
            }
            else
            {
                _output.WriteLine($"No schedule found for train {trainNumber} at station {stationCode}");
            }

            _logger.LogInformation("Schedule lookup for train {TrainNumber} at station {StationCode}: {Found}", 
                trainNumber, stationCode, schedule != null ? "Found" : "Not Found");
        }

        #endregion

        #region Train Status Tests

        [Fact]
        public async Task GetTrainStatusAsync_WithValidTrainNumber_ShouldReturnStatusOrNull()
        {
            // Arrange
            var trainNumber = "12345";
            _output.WriteLine($"Testing train status retrieval for train {trainNumber}...");

            // Act
            var status = await _railwayApiService.GetTrainStatusAsync(trainNumber);

            // Assert
            if (status != null)
            {
                Assert.Equal(trainNumber, status.TrainNumber);
                Assert.True(status.IsValid());
                _output.WriteLine($"Train status: {status.GetSummary()}");
            }
            else
            {
                _output.WriteLine($"No status found for train {trainNumber}");
            }

            _logger.LogInformation("Status lookup for train {TrainNumber}: {Found}", 
                trainNumber, status != null ? "Found" : "Not Found");
        }

        [Fact]
        public async Task GetTrainStatusesAsync_WithMultipleTrains_ShouldReturnStatuses()
        {
            // Arrange
            var trainNumbers = new List<string> { "12345", "12346", "12347" };
            _output.WriteLine($"Testing train status retrieval for {trainNumbers.Count} trains...");

            // Act
            var statuses = await _railwayApiService.GetTrainStatusesAsync(trainNumbers);

            // Assert
            Assert.NotNull(statuses);
            _output.WriteLine($"Retrieved statuses for {statuses.Count} trains");

            foreach (var kvp in statuses)
            {
                Assert.True(trainNumbers.Contains(kvp.Key));
                Assert.True(kvp.Value.IsValid());
                _output.WriteLine($"Train {kvp.Key}: {kvp.Value.CurrentStatus}");
            }

            _logger.LogInformation("Retrieved statuses for {Count} trains", statuses.Count);
        }

        #endregion

        #region Delay Information Tests

        [Fact]
        public async Task GetDelayUpdatesAsync_ShouldReturnDelayInformation()
        {
            // Arrange
            _output.WriteLine("Testing delay updates retrieval...");

            // Act
            var delays = await _railwayApiService.GetDelayUpdatesAsync();

            // Assert
            Assert.NotNull(delays);
            _output.WriteLine($"Retrieved {delays.Count} delay updates");

            foreach (var delay in delays.Take(3)) // Check first 3 delays
            {
                Assert.True(delay.IsValid());
                _output.WriteLine($"Delay: {delay.GetSummary()}");
            }

            _logger.LogInformation("Retrieved {Count} delay updates", delays.Count);
        }

        [Fact]
        public async Task GetStationDelayUpdatesAsync_WithValidStationCode_ShouldReturnStationDelays()
        {
            // Arrange
            var stationCode = "DEL";
            _output.WriteLine($"Testing delay updates retrieval for station {stationCode}...");

            // Act
            var delays = await _railwayApiService.GetStationDelayUpdatesAsync(stationCode);

            // Assert
            Assert.NotNull(delays);
            _output.WriteLine($"Retrieved {delays.Count} delay updates for station {stationCode}");

            foreach (var delay in delays)
            {
                Assert.Equal(stationCode, delay.StationCode);
                Assert.True(delay.IsValid());
            }

            _logger.LogInformation("Retrieved {Count} delay updates for station {StationCode}", delays.Count, stationCode);
        }

        #endregion

        #region Platform Assignment Tests

        [Fact]
        public async Task GetPlatformAssignmentsAsync_WithValidStationCode_ShouldReturnAssignments()
        {
            // Arrange
            var stationCode = "DEL";
            _output.WriteLine($"Testing platform assignments retrieval for station {stationCode}...");

            // Act
            var assignments = await _railwayApiService.GetPlatformAssignmentsAsync(stationCode);

            // Assert
            Assert.NotNull(assignments);
            _output.WriteLine($"Retrieved {assignments.Count} platform assignments for station {stationCode}");

            foreach (var assignment in assignments.Take(3)) // Check first 3 assignments
            {
                Assert.Equal(stationCode, assignment.StationCode);
                Assert.True(assignment.IsValid());
                _output.WriteLine($"Assignment: {assignment.GetSummary()}");
            }

            _logger.LogInformation("Retrieved {Count} platform assignments for station {StationCode}", assignments.Count, stationCode);
        }

        #endregion

        #region Synchronization Tests

        [Fact]
        public async Task SyncScheduleDataAsync_ShouldPerformSynchronization()
        {
            // Arrange
            _output.WriteLine("Testing schedule data synchronization...");

            // Act
            var result = await _railwayApiService.SyncScheduleDataAsync();

            // Assert
            _output.WriteLine($"Schedule synchronization result: {(result ? "Success" : "Failed")}");
            _logger.LogInformation("Schedule synchronization completed: {Result}", result ? "Success" : "Failed");
        }

        [Fact]
        public async Task SyncTrainStatusAsync_ShouldPerformSynchronization()
        {
            // Arrange
            _output.WriteLine("Testing train status synchronization...");

            // Act
            var result = await _railwayApiService.SyncTrainStatusAsync();

            // Assert
            _output.WriteLine($"Train status synchronization result: {(result ? "Success" : "Failed")}");
            _logger.LogInformation("Train status synchronization completed: {Result}", result ? "Success" : "Failed");
        }

        [Fact]
        public async Task PerformBatchUpdateAsync_ShouldExecuteBatchOperations()
        {
            // Arrange
            var stationCodes = new List<string> { "DEL", "BOM" };
            _output.WriteLine($"Testing batch update for {stationCodes.Count} stations...");

            // Act
            var result = await _railwayApiService.PerformBatchUpdateAsync(stationCodes);

            // Assert
            Assert.NotNull(result);
            _output.WriteLine($"Batch update result: Success={result.IsSuccessful}, Records={result.TotalRecordsProcessed}, Duration={result.Duration.TotalMilliseconds}ms");
            
            if (result.Errors.Any())
            {
                _output.WriteLine($"Errors: {string.Join(", ", result.Errors)}");
            }

            _logger.LogInformation("Batch update completed: Success={Success}, Records={Records}", 
                result.IsSuccessful, result.TotalRecordsProcessed);
        }

        #endregion

        #region Schedule Sync Service Tests

        [Fact]
        public async Task ScheduleSyncService_GetSyncStatusAsync_ShouldReturnStatus()
        {
            // Arrange
            _output.WriteLine("Testing schedule sync service status...");

            // Act
            var status = await _scheduleSyncService.GetSyncStatusAsync();

            // Assert
            Assert.NotNull(status);
            Assert.NotEmpty(status.CurrentStatus);
            _output.WriteLine($"Sync service status: {status.CurrentStatus}, Active: {status.IsActive}");
            _logger.LogInformation("Sync service status: {Status}", status.CurrentStatus);
        }

        [Fact]
        public async Task ScheduleSyncService_StartAndStopAsync_ShouldControlService()
        {
            // Arrange
            _output.WriteLine("Testing schedule sync service start/stop...");

            try
            {
                // Act - Start
                await _scheduleSyncService.StartSynchronizationAsync();
                var statusAfterStart = await _scheduleSyncService.GetSyncStatusAsync();
                
                Assert.True(_scheduleSyncService.IsActive);
                _output.WriteLine($"Service started. Status: {statusAfterStart.CurrentStatus}");

                // Act - Stop
                await _scheduleSyncService.StopSynchronizationAsync();
                var statusAfterStop = await _scheduleSyncService.GetSyncStatusAsync();
                
                Assert.False(_scheduleSyncService.IsActive);
                _output.WriteLine($"Service stopped. Status: {statusAfterStop.CurrentStatus}");

                _logger.LogInformation("Sync service start/stop test completed successfully");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Error during start/stop test: {ex.Message}");
                _logger.LogError(ex, "Error during sync service start/stop test");
                throw;
            }
        }

        [Fact]
        public async Task ScheduleSyncService_ForceIncrementalSyncAsync_ShouldPerformSync()
        {
            // Arrange
            _output.WriteLine("Testing forced incremental synchronization...");

            try
            {
                // Act
                var result = await _scheduleSyncService.ForceIncrementalSyncAsync();

                // Assert
                Assert.NotNull(result);
                _output.WriteLine($"Forced sync result: Status={result.CurrentStatus}, Records={result.RecordsProcessed}");
                _logger.LogInformation("Forced incremental sync completed: {Status}", result.CurrentStatus);
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Error during forced sync: {ex.Message}");
                _logger.LogError(ex, "Error during forced incremental sync");
                // Don't throw - this might fail if API is not available
            }
        }

        #endregion

        #region Event Tests

        [Fact]
        public async Task RailwayApiService_Events_ShouldBeRaised()
        {
            // Arrange
            var dataReceivedEventRaised = false;
            var connectionStatusEventRaised = false;

            _railwayApiService.DataReceived += (sender, args) =>
            {
                dataReceivedEventRaised = true;
                _output.WriteLine($"DataReceived event: {args.DataType}, Count: {args.RecordCount}");
            };

            _railwayApiService.ConnectionStatusChanged += (sender, args) =>
            {
                connectionStatusEventRaised = true;
                _output.WriteLine($"ConnectionStatusChanged event: {args.Status}, Connected: {args.IsConnected}");
            };

            _output.WriteLine("Testing Railway API service events...");

            try
            {
                // Act - Perform operations that should trigger events
                await _railwayApiService.TestConnectionAsync();
                await _railwayApiService.GetDelayUpdatesAsync();

                // Give events time to fire
                await Task.Delay(1000);

                // Assert
                _output.WriteLine($"Events raised - DataReceived: {dataReceivedEventRaised}, ConnectionStatus: {connectionStatusEventRaised}");
                _logger.LogInformation("Event test completed - DataReceived: {DataReceived}, ConnectionStatus: {ConnectionStatus}", 
                    dataReceivedEventRaised, connectionStatusEventRaised);
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Error during event test: {ex.Message}");
                _logger.LogError(ex, "Error during event test");
            }
        }

        #endregion

        #region Performance Tests

        [Fact]
        public async Task RailwayApiService_PerformanceTest_ShouldCompleteWithinTimeout()
        {
            // Arrange
            var timeout = TimeSpan.FromSeconds(30);
            _output.WriteLine($"Testing API performance with {timeout.TotalSeconds}s timeout...");

            // Act & Assert
            using var cts = new CancellationTokenSource(timeout);
            
            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                var healthTask = _railwayApiService.GetApiHealthAsync(cts.Token);
                var delaysTask = _railwayApiService.GetDelayUpdatesAsync(cts.Token);
                
                await Task.WhenAll(healthTask, delaysTask);
                
                stopwatch.Stop();
                
                _output.WriteLine($"Performance test completed in {stopwatch.ElapsedMilliseconds}ms");
                _logger.LogInformation("Performance test completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                
                Assert.True(stopwatch.Elapsed < timeout, $"Operations took too long: {stopwatch.Elapsed}");
            }
            catch (OperationCanceledException)
            {
                _output.WriteLine("Performance test timed out");
                _logger.LogWarning("Performance test timed out after {Timeout}s", timeout.TotalSeconds);
                throw new TimeoutException($"API operations did not complete within {timeout.TotalSeconds} seconds");
            }
        }

        #endregion

        #region Cleanup

        public void Dispose()
        {
            _serviceProvider?.Dispose();
            _output.WriteLine("Railway API Integration Tests disposed");
        }

        #endregion
    }
}
