using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a railway train entity
    /// Based on legacy system analysis - enhanced with comprehensive features
    /// </summary>
    [Table("Trains")]
    public class Train
    {
        /// <summary>
        /// Primary key for the train
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Unique train number (e.g., "12345", "EXP123")
        /// Based on legacy system analysis - supports existing train numbering
        /// </summary>
        [Required(ErrorMessage = "Train number is required")]
        [StringLength(20, ErrorMessage = "Train number cannot exceed 20 characters")]
        [Column("TrainNumber")]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Name of the train (e.g., "Rajdhani Express", "Shatabdi Express")
        /// </summary>
        [StringLength(100, ErrorMessage = "Train name cannot exceed 100 characters")]
        [Column("TrainName")]
        public string? TrainName { get; set; }

        /// <summary>
        /// Type of train (Express, Local, Passenger, etc.)
        /// </summary>
        [Required]
        public TrainType TrainType { get; set; } = TrainType.Passenger;

        /// <summary>
        /// Railway operator or zone code
        /// </summary>
        [StringLength(10)]
        public string? OperatorCode { get; set; }

        /// <summary>
        /// Maximum speed of the train in km/h
        /// </summary>
        public int? MaxSpeed { get; set; }

        /// <summary>
        /// Number of coaches in the train
        /// </summary>
        public int? CoachCount { get; set; }

        /// <summary>
        /// Total seating capacity of the train
        /// </summary>
        public int? SeatingCapacity { get; set; }

        /// <summary>
        /// Source station code
        /// </summary>
        [StringLength(10)]
        public string? SourceStation { get; set; }

        /// <summary>
        /// Destination station code
        /// </summary>
        [StringLength(10)]
        public string? DestinationStation { get; set; }

        /// <summary>
        /// Route description or via stations
        /// </summary>
        [StringLength(500)]
        public string? Route { get; set; }

        /// <summary>
        /// Days of operation (e.g., "Daily", "Mon,Wed,Fri")
        /// </summary>
        [StringLength(50)]
        public string? OperatingDays { get; set; }

        /// <summary>
        /// Indicates if the train is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the train has pantry car
        /// </summary>
        public bool HasPantryCar { get; set; } = false;

        /// <summary>
        /// Indicates if the train has AC coaches
        /// </summary>
        public bool HasACCoaches { get; set; } = false;

        /// <summary>
        /// Indicates if the train has sleeper coaches
        /// </summary>
        public bool HasSleeperCoaches { get; set; } = false;

        /// <summary>
        /// Indicates if the train has general coaches
        /// </summary>
        public bool HasGeneralCoaches { get; set; } = true;

        /// <summary>
        /// Indicates if the train has ladies compartment
        /// </summary>
        public bool HasLadiesCompartment { get; set; } = false;

        /// <summary>
        /// Indicates if the train has disabled-friendly facilities
        /// </summary>
        public bool HasDisabledFacilities { get; set; } = false;

        /// <summary>
        /// Train composition details (coach arrangement)
        /// </summary>
        [StringLength(1000)]
        public string? Composition { get; set; }

        /// <summary>
        /// Engine type (Electric, Diesel, Steam)
        /// </summary>
        [StringLength(20)]
        public string? EngineType { get; set; }

        /// <summary>
        /// Gauge type (Broad, Meter, Narrow)
        /// </summary>
        [StringLength(20)]
        public string? GaugeType { get; set; }

        /// <summary>
        /// Distance covered by the train in kilometers
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal? Distance { get; set; }

        /// <summary>
        /// Average journey time in minutes
        /// </summary>
        public int? JourneyTime { get; set; }

        /// <summary>
        /// Frequency of the train (Daily, Weekly, etc.)
        /// </summary>
        [StringLength(20)]
        public string? Frequency { get; set; }

        /// <summary>
        /// Additional remarks or notes about the train
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// Collection of schedules for this train
        /// </summary>
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();

        /// <summary>
        /// Collection of voice files specific to this train
        /// </summary>
        public virtual ICollection<VoiceFile> VoiceFiles { get; set; } = new List<VoiceFile>();

        // Computed Properties

        /// <summary>
        /// Gets a formatted display name for the train
        /// </summary>
        [NotMapped]
        public string DisplayName => string.IsNullOrWhiteSpace(TrainName) 
            ? TrainNumber 
            : $"{TrainNumber} - {TrainName}";

        /// <summary>
        /// Gets the train status description
        /// </summary>
        [NotMapped]
        public string StatusDescription => IsActive ? "Active" : "Inactive";

        /// <summary>
        /// Gets the route description
        /// </summary>
        [NotMapped]
        public string RouteDescription => !string.IsNullOrWhiteSpace(Route) 
            ? Route 
            : $"{SourceStation} to {DestinationStation}";

        /// <summary>
        /// Gets the number of active schedules for this train
        /// </summary>
        [NotMapped]
        public int ActiveScheduleCount => Schedules?.Count(s => s.Status != ScheduleStatus.Cancelled) ?? 0;

        /// <summary>
        /// Gets the facilities available on this train
        /// </summary>
        [NotMapped]
        public List<string> AvailableFacilities
        {
            get
            {
                var facilities = new List<string>();
                if (HasPantryCar) facilities.Add("Pantry Car");
                if (HasACCoaches) facilities.Add("AC Coaches");
                if (HasSleeperCoaches) facilities.Add("Sleeper Coaches");
                if (HasGeneralCoaches) facilities.Add("General Coaches");
                if (HasLadiesCompartment) facilities.Add("Ladies Compartment");
                if (HasDisabledFacilities) facilities.Add("Disabled Facilities");
                return facilities;
            }
        }

        // Methods

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the train has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TrainNumber) && 
                   TrainNumber.Length <= 20;
        }

        /// <summary>
        /// Checks if the train operates on a specific day
        /// </summary>
        /// <param name="dayOfWeek">Day of the week to check</param>
        /// <returns>True if operates on the day, false otherwise</returns>
        public bool OperatesOnDay(DayOfWeek dayOfWeek)
        {
            if (string.IsNullOrWhiteSpace(OperatingDays))
                return true; // Assume daily if not specified

            if (OperatingDays.Contains("Daily", StringComparison.OrdinalIgnoreCase))
                return true;

            var dayName = dayOfWeek.ToString().Substring(0, 3); // Mon, Tue, etc.
            return OperatingDays.Contains(dayName, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets a summary of the train for display purposes
        /// </summary>
        /// <returns>Train summary string</returns>
        public string GetSummary()
        {
            return $"Train: {DisplayName}, Type: {TrainType}, " +
                   $"Route: {RouteDescription}, Status: {StatusDescription}";
        }

        /// <summary>
        /// Gets the estimated journey time as a formatted string
        /// </summary>
        /// <returns>Formatted journey time</returns>
        public string GetFormattedJourneyTime()
        {
            if (JourneyTime == null) return "N/A";
            
            var hours = JourneyTime.Value / 60;
            var minutes = JourneyTime.Value % 60;
            
            if (hours > 0)
                return $"{hours}h {minutes}m";
            else
                return $"{minutes}m";
        }

        /// <summary>
        /// Returns a string representation of the train
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayName;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current train
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Train other)
            {
                return Id == other.Id && 
                       TrainNumber.Equals(other.TrainNumber, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the train
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Id, TrainNumber.ToUpperInvariant());
        }
    }
}
