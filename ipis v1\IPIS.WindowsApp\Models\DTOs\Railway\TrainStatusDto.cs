using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// Data Transfer Object for train status information from Railway API
    /// Provides real-time train location and status data
    /// </summary>
    public class TrainStatusDto
    {
        /// <summary>
        /// Train number
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Current status of the train
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CurrentStatus { get; set; } = string.Empty;

        /// <summary>
        /// Current location of the train
        /// </summary>
        [StringLength(100)]
        public string CurrentLocation { get; set; } = string.Empty;

        /// <summary>
        /// Last location update timestamp
        /// </summary>
        public DateTime LastLocationUpdate { get; set; }

        /// <summary>
        /// Overall delay in minutes
        /// </summary>
        [Range(0, 1440)]
        public int OverallDelayMinutes { get; set; }

        /// <summary>
        /// Station-wise status information
        /// </summary>
        public List<StationStatusDto> StationStatuses { get; set; } = new();

        /// <summary>
        /// Indicates if the train is currently running
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// Data timestamp from Railway API
        /// </summary>
        public DateTime DataTimestamp { get; set; }

        /// <summary>
        /// Train name
        /// </summary>
        [StringLength(100)]
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Source station code
        /// </summary>
        [StringLength(10)]
        public string SourceStation { get; set; } = string.Empty;

        /// <summary>
        /// Destination station code
        /// </summary>
        [StringLength(10)]
        public string DestinationStation { get; set; } = string.Empty;

        /// <summary>
        /// Current speed in km/h
        /// </summary>
        [Range(0, 200)]
        public int? CurrentSpeed { get; set; }

        /// <summary>
        /// Next station code
        /// </summary>
        [StringLength(10)]
        public string NextStation { get; set; } = string.Empty;

        /// <summary>
        /// Expected time to reach next station
        /// </summary>
        public DateTime? ExpectedNextStationTime { get; set; }

        /// <summary>
        /// Distance to next station in kilometers
        /// </summary>
        public decimal? DistanceToNextStation { get; set; }

        /// <summary>
        /// Last reported station
        /// </summary>
        [StringLength(10)]
        public string LastReportedStation { get; set; } = string.Empty;

        /// <summary>
        /// Time when last reported at station
        /// </summary>
        public DateTime? LastReportedTime { get; set; }

        /// <summary>
        /// GPS coordinates if available
        /// </summary>
        public GpsCoordinatesDto? GpsCoordinates { get; set; }

        /// <summary>
        /// Additional status information
        /// </summary>
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();

        /// <summary>
        /// Computed property: Indicates if the train is delayed
        /// </summary>
        public bool IsDelayed => OverallDelayMinutes > 0;

        /// <summary>
        /// Computed property: Gets the delay description
        /// </summary>
        public string DelayDescription
        {
            get
            {
                if (OverallDelayMinutes == 0) return "On Time";
                if (OverallDelayMinutes < 60) return $"{OverallDelayMinutes} min late";
                
                var hours = OverallDelayMinutes / 60;
                var minutes = OverallDelayMinutes % 60;
                
                if (minutes == 0) return $"{hours}h late";
                return $"{hours}h {minutes}m late";
            }
        }

        /// <summary>
        /// Computed property: Gets the route description
        /// </summary>
        public string RouteDescription => $"{SourceStation} to {DestinationStation}";

        /// <summary>
        /// Computed property: Gets the current location description
        /// </summary>
        public string LocationDescription
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(CurrentLocation))
                    return CurrentLocation;
                
                if (!string.IsNullOrWhiteSpace(LastReportedStation))
                    return $"Last reported at {LastReportedStation}";
                
                return "Location unknown";
            }
        }

        /// <summary>
        /// Computed property: Time since last update
        /// </summary>
        public TimeSpan TimeSinceLastUpdate => DateTime.UtcNow - LastLocationUpdate;

        /// <summary>
        /// Validates the DTO data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TrainNumber) &&
                   !string.IsNullOrWhiteSpace(CurrentStatus) &&
                   DataTimestamp != default;
        }

        /// <summary>
        /// Gets the status for a specific station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <returns>Station status or null if not found</returns>
        public StationStatusDto? GetStationStatus(string stationCode)
        {
            return StationStatuses.FirstOrDefault(s => 
                s.StationCode.Equals(stationCode, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets a summary of the train status
        /// </summary>
        /// <returns>Status summary string</returns>
        public string GetSummary()
        {
            var summary = $"Train {TrainNumber}";
            if (!string.IsNullOrWhiteSpace(TrainName))
                summary += $" ({TrainName})";
            
            summary += $": {CurrentStatus}";
            
            if (!string.IsNullOrWhiteSpace(CurrentLocation))
                summary += $" at {CurrentLocation}";
            
            if (IsDelayed)
                summary += $", {DelayDescription}";
            
            return summary;
        }

        /// <summary>
        /// Returns a string representation of the train status
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }

    /// <summary>
    /// Station-specific status information
    /// </summary>
    public class StationStatusDto
    {
        /// <summary>
        /// Station code
        /// </summary>
        [Required]
        [StringLength(10)]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Station name
        /// </summary>
        [StringLength(100)]
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// Scheduled arrival time
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Actual arrival time
        /// </summary>
        public DateTime? ActualArrival { get; set; }

        /// <summary>
        /// Scheduled departure time
        /// </summary>
        public DateTime? ScheduledDeparture { get; set; }

        /// <summary>
        /// Actual departure time
        /// </summary>
        public DateTime? ActualDeparture { get; set; }

        /// <summary>
        /// Platform number
        /// </summary>
        [StringLength(10)]
        public string PlatformNumber { get; set; } = string.Empty;

        /// <summary>
        /// Status at this station
        /// </summary>
        [StringLength(50)]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Delay in minutes at this station
        /// </summary>
        public int DelayMinutes { get; set; }

        /// <summary>
        /// Sequence number in the route
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// Distance from origin
        /// </summary>
        public decimal? DistanceFromOrigin { get; set; }

        /// <summary>
        /// Computed property: Indicates if delayed at this station
        /// </summary>
        public bool IsDelayed => DelayMinutes > 0;

        /// <summary>
        /// Computed property: Indicates if the train has arrived
        /// </summary>
        public bool HasArrived => ActualArrival.HasValue;

        /// <summary>
        /// Computed property: Indicates if the train has departed
        /// </summary>
        public bool HasDeparted => ActualDeparture.HasValue;
    }

    /// <summary>
    /// GPS coordinates information
    /// </summary>
    public class GpsCoordinatesDto
    {
        /// <summary>
        /// Latitude coordinate
        /// </summary>
        [Range(-90, 90)]
        public double Latitude { get; set; }

        /// <summary>
        /// Longitude coordinate
        /// </summary>
        [Range(-180, 180)]
        public double Longitude { get; set; }

        /// <summary>
        /// Accuracy in meters
        /// </summary>
        [Range(0, 10000)]
        public double? Accuracy { get; set; }

        /// <summary>
        /// Timestamp when coordinates were recorded
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Returns a string representation of the coordinates
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{Latitude:F6}, {Longitude:F6}";
        }
    }
}
