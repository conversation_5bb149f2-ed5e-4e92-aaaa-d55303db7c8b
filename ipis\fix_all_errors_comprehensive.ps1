# PowerShell script to fix ALL remaining compilation errors

Write-Host "=== FIXING ALL REMAINING COMPILATION ERRORS ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # 1. Fix all missing ref parameters for method calls
    $content = $content -replace '\.FindWindow\(([^,]+),\s*([^)]+)\)', '.FindWindow(ref $1, ref $2)'
    $content = $content -replace '\.GetWindowRect\(([^,]+),\s*([^)]+)\)', '.GetWindowRect($1, ref $2)'
    $content = $content -replace '\.MessageBox\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.MessageBox($1, ref $2, ref $3, $4)'
    
    # Fix network_db_read method calls
    $content = $content -replace 'network_db_read\.set_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_msg($1, $2, $3, $4, ref $5)'
    $content = $content -replace 'network_db_read\.set_Update_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_Update_msg($1, $2, $3, $4, ref $5)'
    $content = $content -replace 'network_db_read\.delete_msg\(([^,]+),\s*([^)]+)\)', 'network_db_read.delete_msg($1, ref $2)'
    $content = $content -replace 'network_db_read\.set_voice_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_voice_msg($1, $2, $3, $4, $5, ref $6)'
    $content = $content -replace 'network_db_read\.update_voice_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.update_voice_msg($1, $2, $3, $4, $5, ref $6)'
    $content = $content -replace 'network_db_read\.set_user_group\(([^,]+),\s*([^)]+)\)', 'network_db_read.set_user_group(ref $1, $2)'
    $content = $content -replace 'network_db_read\.enc_pwd\(([^,]+),\s*([^)]+)\)', 'network_db_read.enc_pwd($1, ref $2)'
    $content = $content -replace 'network_db_read\.dec_pwd\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.dec_pwd($1, ref $2, $3)'
    $content = $content -replace 'network_db_read\.set_user_pwd\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_user_pwd($1, $2, $3, ref $4)'
    $content = $content -replace 'network_db_read\.set_user_name\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_user_name($1, $2, ref $3)'
    $content = $content -replace 'network_db_read\.delete_pwd\(([^,]+),\s*([^)]+)\)', 'network_db_read.delete_pwd($1, ref $2)'
    $content = $content -replace 'network_db_read\.set_com_baud_values\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_com_baud_values($1, $2, ref $3)'
    
    # Fix agdb_info_data and similar calls
    $content = $content -replace 'network_db_read\.agdb_info_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.agdb_info_data($1, ref $2, ref $3, ref $4)'
    $content = $content -replace 'network_db_read\.cgdb_info_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.cgdb_info_data($1, ref $2, ref $3, ref $4, ref $5, ref $6)'
    $content = $content -replace 'network_db_read\.cgdb_name_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.cgdb_name_data($1, ref $2, $3, ref $4, ref $5, ref $6, ref $7)'
    $content = $content -replace 'network_db_read\.agdb_com_name_data\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.agdb_com_name_data($1, ref $2, ref $3)'
    $content = $content -replace 'network_db_read\.agdb_name_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.agdb_name_data($1, ref $2, $3, ref $4, ref $5, ref $6)'
    $content = $content -replace 'network_db_read\.agdb_com_addr_data\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.agdb_com_addr_data(ref $1, $2, ref $3)'
    $content = $content -replace 'network_db_read\.agdb_addr_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.agdb_addr_data(ref $1, $2, $3, ref $4, ref $5, ref $6)'
    $content = $content -replace 'network_db_read\.cgdb_addr_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.cgdb_addr_data(ref $1, $2, $3, ref $4, ref $5, ref $6, ref $7)'
    
    # Fix more method calls
    $content = $content -replace 'network_db_read\.agdb_get_details_db\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.agdb_get_details_db($1, $2, ref $3, ref $4, ref $5, ref $6, ref $7)'
    $content = $content -replace 'network_db_read\.cgdb_get_details_db\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.cgdb_get_details_db($1, $2, ref $3, ref $4, ref $5, ref $6, ref $7)'
    $content = $content -replace 'network_db_read\.pdch_info_data\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.pdch_info_data($1, ref $2, ref $3, ref $4, ref $5)'
    $content = $content -replace 'network_db_read\.get_Station_code\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.get_Station_code($1, ref $2, ref $3, ref $4)'
    $content = $content -replace 'network_db_read\.get_station_details\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.get_station_details(ref $1, ref $2, ref $3, ref $4, ref $5, ref $6, ref $7, ref $8, ref $9, ref $10, ref $11)'
    $content = $content -replace 'network_db_read\.get_train_status_info\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.get_train_status_info(ref $1, ref $2, ref $3, $4, ref $5)'
    $content = $content -replace 'network_db_read\.get_train_timings\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.get_train_timings(ref $1, ref $2, ref $3, ref $4)'
    $content = $content -replace 'network_db_read\.get_intensity\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.get_intensity(ref $1, ref $2, ref $3, ref $4)'
    $content = $content -replace 'network_db_read\.get_Auto\(([^)]+)\)', 'network_db_read.get_Auto(ref $1)'
    $content = $content -replace 'network_db_read\.get_ComPort\(([^)]+)\)', 'network_db_read.get_ComPort(ref $1)'
    
    # Fix voice method calls
    $content = $content -replace '\.train_name_voice\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.train_name_voice($1, $2, ref $3)'
    $content = $content -replace '\.station_name_voice\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.station_name_voice($1, $2, ref $3)'
    $content = $content -replace '\.lang_status\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.lang_status($1, ref $2, ref $3, ref $4, ref $5)'
    
    # Fix agdb_byte_construct calls
    $content = $content -replace 'agdb_byte_construct\.agdb_default_msg_bytes\(([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.agdb_default_msg_bytes($1, ref $2)'
    
    # Fix API calls
    $content = $content -replace 'pdb_api\.pdb_message\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdb_api.pdb_message($1, ref $2, $3, $4, $5, $6, $7, $8, $9, $10)'
    $content = $content -replace 'mldb_api\.mldb_message\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mldb_api.mldb_message($1, ref $2, $3, $4, $5, $6, $7, $8, $9)'
    
    # Fix Monitor.Enter calls
    $content = $content -replace 'Monitor\.Enter\(([^,]+),\s*([^)]+)\)', 'Monitor.Enter($1, ref $2)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed method call ref parameters"
    }
}

Write-Host "Method call ref parameter fixes complete!"
