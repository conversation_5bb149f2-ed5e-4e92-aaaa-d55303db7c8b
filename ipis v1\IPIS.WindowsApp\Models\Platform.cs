using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IPIS.WindowsApp.Models
{
    /// <summary>
    /// Represents a platform within a railway station
    /// </summary>
    [Table("Platforms")]
    public class Platform
    {
        /// <summary>
        /// Gets or sets the unique identifier for the platform
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the station ID this platform belongs to
        /// </summary>
        [Required]
        public int StationId { get; set; }

        /// <summary>
        /// Gets or sets the platform number (e.g., "1", "2A", "3B")
        /// </summary>
        [Required]
        [StringLength(10)]
        public string PlatformNumber { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the platform name or description
        /// </summary>
        [StringLength(50)]
        public string? PlatformName { get; set; }

        /// <summary>
        /// Gets or sets whether the platform is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Gets or sets the date and time when the platform was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for the station this platform belongs to
        /// </summary>
        [ForeignKey("StationId")]
        public virtual Station Station { get; set; } = null!;

        /// <summary>
        /// Navigation property for schedules associated with this platform
        /// </summary>
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }
}
