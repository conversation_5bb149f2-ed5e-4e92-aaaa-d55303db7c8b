using IPIS.WindowsApp.Models;

namespace IPIS.WindowsApp.Services
{
    /// <summary>
    /// Interface for station-related operations
    /// </summary>
    public interface IStationService
    {
        /// <summary>
        /// Gets all stations asynchronously
        /// </summary>
        /// <returns>A collection of all stations</returns>
        Task<IEnumerable<Station>> GetAllStationsAsync();

        /// <summary>
        /// Gets a station by its ID asynchronously
        /// </summary>
        /// <param name="id">The station ID</param>
        /// <returns>The station if found, null otherwise</returns>
        Task<Station?> GetStationByIdAsync(int id);

        /// <summary>
        /// Gets a station by its code asynchronously
        /// </summary>
        /// <param name="code">The station code</param>
        /// <returns>The station if found, null otherwise</returns>
        Task<Station?> GetStationByCodeAsync(string code);

        /// <summary>
        /// Creates a new station asynchronously
        /// </summary>
        /// <param name="station">The station to create</param>
        /// <returns>The created station</returns>
        Task<Station> CreateStationAsync(Station station);

        /// <summary>
        /// Updates an existing station asynchronously
        /// </summary>
        /// <param name="station">The station to update</param>
        /// <returns>The updated station</returns>
        Task<Station> UpdateStationAsync(Station station);

        /// <summary>
        /// Deletes a station asynchronously
        /// </summary>
        /// <param name="id">The ID of the station to delete</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        Task<bool> DeleteStationAsync(int id);

        /// <summary>
        /// Gets all active stations asynchronously
        /// </summary>
        /// <returns>A collection of active stations</returns>
        Task<IEnumerable<Station>> GetActiveStationsAsync();
    }
}
