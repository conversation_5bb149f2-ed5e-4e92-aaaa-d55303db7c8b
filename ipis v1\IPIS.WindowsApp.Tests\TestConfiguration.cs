using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using IPIS.WindowsApp.Data;
using Serilog;

namespace IPIS.WindowsApp.Tests
{
    /// <summary>
    /// Global test configuration and setup
    /// Configures services, logging, and test environment
    /// </summary>
    [SetUpFixture]
    public class TestConfiguration
    {
        public static IServiceProvider ServiceProvider { get; private set; } = null!;
        public static IConfiguration Configuration { get; private set; } = null!;

        [OneTimeSetUp]
        public void GlobalSetup()
        {
            // Configure Serilog for test logging
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .WriteTo.File("logs/test-log-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // Build configuration
            var configurationBuilder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.Test.json", optional: true)
                .AddEnvironmentVariables();

            Configuration = configurationBuilder.Build();

            // Configure services
            var services = new ServiceCollection();
            ConfigureServices(services);
            ServiceProvider = services.BuildServiceProvider();

            // Log test environment information
            Log.Information("Test environment initialized");
            Log.Information("Configuration loaded from: {ConfigSources}", 
                string.Join(", ", configurationBuilder.Sources.Select(s => s.GetType().Name)));
        }

        [OneTimeTearDown]
        public void GlobalTeardown()
        {
            Log.Information("Test environment cleanup started");
            
            ServiceProvider?.Dispose();
            Log.CloseAndFlush();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Add configuration
            services.AddSingleton(Configuration);

            // Add logging
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // Add Entity Framework with in-memory database for testing
            services.AddDbContext<IPISDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDatabase");
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Add other services as needed for testing
            // services.AddScoped<IStationService, StationService>();
            // services.AddScoped<ITrainService, TrainService>();
            // etc.
        }

        /// <summary>
        /// Creates a new service scope for isolated testing
        /// </summary>
        /// <returns>Service scope</returns>
        public static IServiceScope CreateScope()
        {
            return ServiceProvider.CreateScope();
        }

        /// <summary>
        /// Gets a service from the test service provider
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        public static T GetService<T>() where T : notnull
        {
            return ServiceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Creates a fresh database context for testing
        /// </summary>
        /// <returns>Database context</returns>
        public static IPISDbContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<IPISDbContext>()
                .UseInMemoryDatabase(Guid.NewGuid().ToString())
                .Options;

            return new IPISDbContext(options);
        }
    }

    /// <summary>
    /// Base class for integration tests that need database access
    /// </summary>
    public abstract class DatabaseTestBase
    {
        protected IPISDbContext Context { get; private set; } = null!;
        protected ILogger Logger { get; private set; } = null!;

        [SetUp]
        public virtual void SetUp()
        {
            Context = TestConfiguration.CreateDbContext();
            Context.Database.EnsureCreated();
            
            Logger = TestConfiguration.GetService<ILogger<DatabaseTestBase>>();
        }

        [TearDown]
        public virtual void TearDown()
        {
            Context.Database.EnsureDeleted();
            Context.Dispose();
        }
    }

    /// <summary>
    /// Base class for unit tests
    /// </summary>
    public abstract class UnitTestBase
    {
        protected ILogger Logger { get; private set; } = null!;

        [SetUp]
        public virtual void SetUp()
        {
            Logger = TestConfiguration.GetService<ILogger<UnitTestBase>>();
        }
    }

    /// <summary>
    /// Test categories for organizing test execution
    /// </summary>
    public static class TestCategories
    {
        public const string Unit = "Unit";
        public const string Integration = "Integration";
        public const string Performance = "Performance";
        public const string Database = "Database";
        public const string API = "API";
        public const string Communication = "Communication";
        public const string Voice = "Voice";
        public const string Advertisement = "Advertisement";
        public const string Security = "Security";
        public const string Stress = "Stress";
        public const string LongRunning = "LongRunning";
    }

    /// <summary>
    /// Test data constants
    /// </summary>
    public static class TestConstants
    {
        // Performance thresholds
        public const int FAST_OPERATION_MS = 100;
        public const int MEDIUM_OPERATION_MS = 500;
        public const int SLOW_OPERATION_MS = 2000;

        // Data sizes
        public const int SMALL_DATASET = 100;
        public const int MEDIUM_DATASET = 1000;
        public const int LARGE_DATASET = 10000;

        // Test station codes
        public const string TEST_STATION_CODE = "TEST";
        public const string TEST_STATION_NAME = "Test Station";

        // Test train numbers
        public const string TEST_TRAIN_NUMBER = "12345";
        public const string TEST_TRAIN_NAME = "Test Express";

        // Test IP addresses
        public const string TEST_IP_ADDRESS = "*************";
        public const int TEST_PORT = 8080;

        // Test user credentials
        public const string TEST_USERNAME = "testuser";
        public const string TEST_PASSWORD = "TestPassword123";
        public const string TEST_EMAIL = "<EMAIL>";
    }

    /// <summary>
    /// Custom test attributes for better test organization
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class CriticalTestAttribute : CategoryAttribute
    {
        public CriticalTestAttribute() : base("Critical") { }
    }

    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class RegressionTestAttribute : CategoryAttribute
    {
        public RegressionTestAttribute() : base("Regression") { }
    }

    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class SmokeTestAttribute : CategoryAttribute
    {
        public SmokeTestAttribute() : base("Smoke") { }
    }

    /// <summary>
    /// Test utilities and helper methods
    /// </summary>
    public static class TestUtilities
    {
        /// <summary>
        /// Measures execution time of an action
        /// </summary>
        /// <param name="action">Action to measure</param>
        /// <returns>Execution time in milliseconds</returns>
        public static long MeasureExecutionTime(Action action)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            action();
            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds;
        }

        /// <summary>
        /// Measures execution time of an async action
        /// </summary>
        /// <param name="action">Async action to measure</param>
        /// <returns>Execution time in milliseconds</returns>
        public static async Task<long> MeasureExecutionTimeAsync(Func<Task> action)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            await action();
            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds;
        }

        /// <summary>
        /// Generates a unique test identifier
        /// </summary>
        /// <returns>Unique test ID</returns>
        public static string GenerateTestId()
        {
            return $"TEST_{DateTime.UtcNow:yyyyMMddHHmmss}_{Guid.NewGuid():N}";
        }

        /// <summary>
        /// Creates a temporary file for testing
        /// </summary>
        /// <param name="content">File content</param>
        /// <param name="extension">File extension</param>
        /// <returns>Temporary file path</returns>
        public static string CreateTempFile(string content = "", string extension = ".tmp")
        {
            var tempPath = Path.Combine(Path.GetTempPath(), $"{GenerateTestId()}{extension}");
            File.WriteAllText(tempPath, content);
            return tempPath;
        }

        /// <summary>
        /// Cleans up temporary files created during testing
        /// </summary>
        /// <param name="filePaths">File paths to clean up</param>
        public static void CleanupTempFiles(params string[] filePaths)
        {
            foreach (var filePath in filePaths)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }

        /// <summary>
        /// Waits for a condition to be true with timeout
        /// </summary>
        /// <param name="condition">Condition to check</param>
        /// <param name="timeout">Maximum wait time</param>
        /// <param name="interval">Check interval</param>
        /// <returns>True if condition was met, false if timeout</returns>
        public static async Task<bool> WaitForConditionAsync(
            Func<bool> condition, 
            TimeSpan timeout, 
            TimeSpan? interval = null)
        {
            interval ??= TimeSpan.FromMilliseconds(100);
            var endTime = DateTime.UtcNow.Add(timeout);

            while (DateTime.UtcNow < endTime)
            {
                if (condition())
                    return true;

                await Task.Delay(interval.Value);
            }

            return false;
        }

        /// <summary>
        /// Retries an action with exponential backoff
        /// </summary>
        /// <param name="action">Action to retry</param>
        /// <param name="maxAttempts">Maximum retry attempts</param>
        /// <param name="baseDelay">Base delay between retries</param>
        /// <returns>True if action succeeded, false otherwise</returns>
        public static async Task<bool> RetryAsync(
            Func<Task<bool>> action, 
            int maxAttempts = 3, 
            TimeSpan? baseDelay = null)
        {
            baseDelay ??= TimeSpan.FromMilliseconds(100);

            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                try
                {
                    if (await action())
                        return true;
                }
                catch
                {
                    if (attempt == maxAttempts)
                        throw;
                }

                if (attempt < maxAttempts)
                {
                    var delay = TimeSpan.FromMilliseconds(baseDelay.Value.TotalMilliseconds * Math.Pow(2, attempt - 1));
                    await Task.Delay(delay);
                }
            }

            return false;
        }
    }
}
