# PowerShell script to fix unassigned variable issues systematically

Write-Host "=== FIXING UNASSIGNED VARIABLE ISSUES ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix unassigned variables by initializing them
    $content = $content -replace '\s+byte num1;\s*\n', "`n    byte num1 = 0;`n"
    $content = $content -replace '\s+byte num2;\s*\n', "`n    byte num2 = 0;`n"
    $content = $content -replace '\s+byte num;\s*\n', "`n    byte num = 0;`n"
    $content = $content -replace '\s+int num1;\s*\n', "`n    int num1 = 0;`n"
    $content = $content -replace '\s+int num2;\s*\n', "`n    int num2 = 0;`n"
    $content = $content -replace '\s+int num;\s*\n', "`n    int num = 0;`n"
    $content = $content -replace '\s+int index;\s*\n', "`n    int index = 0;`n"
    $content = $content -replace '\s+int index1;\s*\n', "`n    int index1 = 0;`n"
    $content = $content -replace '\s+int index2;\s*\n', "`n    int index2 = 0;`n"
    $content = $content -replace '\s+int index3;\s*\n', "`n    int index3 = 0;`n"
    $content = $content -replace '\s+short int16_1;\s*\n', "`n    short int16_1 = 0;`n"
    $content = $content -replace '\s+short int16_2;\s*\n', "`n    short int16_2 = 0;`n"
    $content = $content -replace '\s+short int16_3;\s*\n', "`n    short int16_3 = 0;`n"
    $content = $content -replace '\s+byte cfgSendPkt;\s*\n', "`n    byte cfgSendPkt = 0;`n"
    $content = $content -replace '\s+byte cfgResPkt;\s*\n', "`n    byte cfgResPkt = 0;`n"
    $content = $content -replace '\s+byte cfg;\s*\n', "`n    byte cfg = 0;`n"
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed unassigned variables"
    }
}

Write-Host "Unassigned variable fixes complete!"
