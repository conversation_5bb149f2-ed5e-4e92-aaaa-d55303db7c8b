using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Interface for data conflict resolution service
    /// Manages conflicts between local system data and Railway API data
    /// </summary>
    public interface IDataConflictResolver
    {
        #region Conflict Detection

        /// <summary>
        /// Detects conflicts between local and API data
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of detected conflicts</returns>
        Task<List<DataConflict>> DetectConflictsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Detects conflicts for a specific schedule
        /// </summary>
        /// <param name="localSchedule">Local schedule data</param>
        /// <param name="apiSchedule">API schedule data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of conflicts for the schedule</returns>
        Task<List<DataConflict>> DetectScheduleConflictsAsync(Schedule localSchedule, TrainScheduleDto apiSchedule, CancellationToken cancellationToken = default);

        /// <summary>
        /// Detects conflicts for a specific train status
        /// </summary>
        /// <param name="localTrain">Local train data</param>
        /// <param name="apiStatus">API train status data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of conflicts for the train</returns>
        Task<List<DataConflict>> DetectTrainStatusConflictsAsync(Train localTrain, TrainStatusDto apiStatus, CancellationToken cancellationToken = default);

        /// <summary>
        /// Detects conflicts for platform assignments
        /// </summary>
        /// <param name="localSchedule">Local schedule with platform assignment</param>
        /// <param name="apiAssignment">API platform assignment data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of platform assignment conflicts</returns>
        Task<List<DataConflict>> DetectPlatformConflictsAsync(Schedule localSchedule, PlatformAssignmentDto apiAssignment, CancellationToken cancellationToken = default);

        #endregion

        #region Conflict Resolution

        /// <summary>
        /// Resolves a specific schedule conflict
        /// </summary>
        /// <param name="localSchedule">Local schedule data</param>
        /// <param name="apiSchedule">API schedule data</param>
        /// <param name="strategy">Resolution strategy to use</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Conflict resolution result</returns>
        Task<ConflictResolutionResult> ResolveScheduleConflictAsync(Schedule localSchedule, TrainScheduleDto apiSchedule, ConflictResolutionStrategy? strategy = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Resolves multiple conflicts using specified resolutions
        /// </summary>
        /// <param name="resolutions">List of conflict resolutions to apply</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of resolution results</returns>
        Task<List<ConflictResolutionResult>> ApplyConflictResolutionsAsync(List<ConflictResolution> resolutions, CancellationToken cancellationToken = default);

        /// <summary>
        /// Automatically resolves conflicts using configured strategies
        /// </summary>
        /// <param name="conflicts">Conflicts to resolve</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of resolution results</returns>
        Task<List<ConflictResolutionResult>> AutoResolveConflictsAsync(List<DataConflict> conflicts, CancellationToken cancellationToken = default);

        #endregion

        #region Conflict Management

        /// <summary>
        /// Gets all unresolved conflicts
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of unresolved conflicts</returns>
        Task<List<DataConflict>> GetUnresolvedConflictsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets conflicts by severity level
        /// </summary>
        /// <param name="severity">Severity level to filter by</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of conflicts with specified severity</returns>
        Task<List<DataConflict>> GetConflictsBySeverityAsync(ConflictSeverity severity, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets conflicts for a specific entity
        /// </summary>
        /// <param name="entityType">Type of entity</param>
        /// <param name="entityId">Entity identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of conflicts for the entity</returns>
        Task<List<DataConflict>> GetEntityConflictsAsync(string entityType, string entityId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Marks a conflict as resolved
        /// </summary>
        /// <param name="conflictId">Conflict identifier</param>
        /// <param name="resolutionResult">Resolution result</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the mark operation</returns>
        Task MarkConflictResolvedAsync(string conflictId, ConflictResolutionResult resolutionResult, CancellationToken cancellationToken = default);

        #endregion

        #region Resolution Strategies

        /// <summary>
        /// Gets the default resolution strategy for a conflict type
        /// </summary>
        /// <param name="conflictType">Type of conflict</param>
        /// <param name="severity">Conflict severity</param>
        /// <returns>Default resolution strategy</returns>
        ConflictResolutionStrategy GetDefaultStrategy(ConflictType conflictType, ConflictSeverity severity);

        /// <summary>
        /// Sets the default resolution strategy for a conflict type
        /// </summary>
        /// <param name="conflictType">Type of conflict</param>
        /// <param name="severity">Conflict severity</param>
        /// <param name="strategy">Resolution strategy</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the set operation</returns>
        Task SetDefaultStrategyAsync(ConflictType conflictType, ConflictSeverity severity, ConflictResolutionStrategy strategy, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets available resolution strategies for a conflict
        /// </summary>
        /// <param name="conflict">Conflict to get strategies for</param>
        /// <returns>List of applicable resolution strategies</returns>
        List<ConflictResolutionStrategy> GetApplicableStrategies(DataConflict conflict);

        #endregion

        #region Business Rules

        /// <summary>
        /// Applies business rules to determine conflict resolution
        /// </summary>
        /// <param name="conflict">Conflict to apply rules to</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Recommended resolution strategy</returns>
        Task<ConflictResolutionStrategy> ApplyBusinessRulesAsync(DataConflict conflict, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates a proposed resolution against business rules
        /// </summary>
        /// <param name="conflict">Conflict being resolved</param>
        /// <param name="proposedResolution">Proposed resolution</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateResolutionAsync(DataConflict conflict, ConflictResolution proposedResolution, CancellationToken cancellationToken = default);

        #endregion

        #region Statistics and Reporting

        /// <summary>
        /// Gets conflict resolution statistics
        /// </summary>
        /// <param name="startDate">Start date for statistics</param>
        /// <param name="endDate">End date for statistics</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Conflict resolution statistics</returns>
        Task<ConflictStatistics> GetConflictStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the conflict resolution history
        /// </summary>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of resolution history records</returns>
        Task<List<ConflictResolutionHistory>> GetResolutionHistoryAsync(int limit = 100, CancellationToken cancellationToken = default);

        #endregion

        #region Events

        /// <summary>
        /// Event raised when a new conflict is detected
        /// </summary>
        event EventHandler<ConflictDetectedEventArgs>? ConflictDetected;

        /// <summary>
        /// Event raised when a conflict is resolved
        /// </summary>
        event EventHandler<ConflictResolvedEventArgs>? ConflictResolved;

        /// <summary>
        /// Event raised when conflict resolution fails
        /// </summary>
        event EventHandler<ConflictResolutionFailedEventArgs>? ConflictResolutionFailed;

        /// <summary>
        /// Event raised when a critical conflict requires immediate attention
        /// </summary>
        event EventHandler<CriticalConflictEventArgs>? CriticalConflictDetected;

        #endregion
    }

    /// <summary>
    /// Validation result for conflict resolution
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string? RecommendedAction { get; set; }
    }

    /// <summary>
    /// Conflict resolution statistics
    /// </summary>
    public class ConflictStatistics
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalConflictsDetected { get; set; }
        public int TotalConflictsResolved { get; set; }
        public int AutoResolvedConflicts { get; set; }
        public int ManuallyResolvedConflicts { get; set; }
        public int UnresolvedConflicts { get; set; }
        public Dictionary<ConflictType, int> ConflictsByType { get; set; } = new();
        public Dictionary<ConflictSeverity, int> ConflictsBySeverity { get; set; } = new();
        public Dictionary<ConflictResolutionStrategy, int> ResolutionsByStrategy { get; set; } = new();
        public TimeSpan AverageResolutionTime { get; set; }
        public double ResolutionRate => TotalConflictsDetected > 0 ? (double)TotalConflictsResolved / TotalConflictsDetected * 100 : 0;
    }

    /// <summary>
    /// Conflict resolution history record
    /// </summary>
    public class ConflictResolutionHistory
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ConflictId { get; set; } = string.Empty;
        public ConflictType ConflictType { get; set; }
        public ConflictSeverity Severity { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; }
        public DateTime ResolvedAt { get; set; }
        public ConflictResolutionStrategy Strategy { get; set; }
        public string? ResolvedBy { get; set; }
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan ResolutionDuration => ResolvedAt - DetectedAt;
    }

    #region Event Arguments

    /// <summary>
    /// Event arguments for conflict detected events
    /// </summary>
    public class ConflictDetectedEventArgs : EventArgs
    {
        public DataConflict Conflict { get; set; } = new();
        public DateTime DetectedAt { get; set; }
        public bool RequiresImmediateAttention { get; set; }
    }

    /// <summary>
    /// Event arguments for conflict resolved events
    /// </summary>
    public class ConflictResolvedEventArgs : EventArgs
    {
        public string ConflictId { get; set; } = string.Empty;
        public ConflictResolutionResult Resolution { get; set; } = new();
        public DateTime ResolvedAt { get; set; }
        public TimeSpan ResolutionDuration { get; set; }
    }

    /// <summary>
    /// Event arguments for conflict resolution failed events
    /// </summary>
    public class ConflictResolutionFailedEventArgs : EventArgs
    {
        public string ConflictId { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public DateTime FailedAt { get; set; }
        public int RetryAttempt { get; set; }
    }

    /// <summary>
    /// Event arguments for critical conflict events
    /// </summary>
    public class CriticalConflictEventArgs : EventArgs
    {
        public DataConflict Conflict { get; set; } = new();
        public string ImpactAssessment { get; set; } = string.Empty;
        public string RecommendedAction { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; }
        public bool RequiresImmediateIntervention { get; set; }
    }

    #endregion
}
