// Decompiled with JetBrains decompiler
// Type: ipis.frmHelp
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using AxAcroPDFLib;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmHelp : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("AxAcroPDF1")]
  private AxAcroPDF _AxAcroPDF1;

  [DebuggerNonUserCode]
  static frmHelp()
  {
  }

  [DebuggerNonUserCode]
  public frmHelp()
  {
    this.Load += new EventHandler(this.frmHelp_Load);
    frmHelp.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmHelp.__ENCList)
    {
      if (frmHelp.__ENCList.Count == frmHelp.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmHelp.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmHelp.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmHelp.__ENCList[index1] = frmHelp.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmHelp.__ENCList.RemoveRange(index1, checked (frmHelp.__ENCList.Count - index1));
        frmHelp.__ENCList.Capacity = frmHelp.__ENCList.Count;
      }
      frmHelp.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmHelp));
    this.AxAcroPDF1 = new AxAcroPDF();
    ((ISupportInitialize) this.AxAcroPDF1).BeginInit();
    this.SuspendLayout();
    ((Control) this.AxAcroPDF1).CausesValidation = false;
    ((AxHost) this.AxAcroPDF1).Enabled = true;
    ((Control) this.AxAcroPDF1).Location = new Point(2, 2);
    ((Control) this.AxAcroPDF1).Name = "AxAcroPDF1";
    ((AxHost) this.AxAcroPDF1).OcxState = (AxHost.State) componentResourceManager.GetObject("AxAcroPDF1.OcxState");
    AxAcroPDF axAcroPdF1 = this.AxAcroPDF1;
    Size size1 = new Size(1074, 639);
    Size size2 = size1;
    ((Control) axAcroPdF1).Size = size2;
    ((Control) this.AxAcroPDF1).TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    size1 = new Size(1076, 643);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.AxAcroPDF1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmHelp";
    this.Text = "frmHelp";
    ((ISupportInitialize) this.AxAcroPDF1).EndInit();
    this.ResumeLayout(false);
  }

  internal virtual AxAcroPDF AxAcroPDF1
  {
    [DebuggerNonUserCode] get { return this._AxAcroPDF1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._AxAcroPDF1 = value;
    }
  }

  private void frmHelp_Load(object sender, EventArgs e)
  {
    this.AxAcroPDF1.src = "C:\\IPIS\\ipis_user_guide.pdf";
  }
}

}