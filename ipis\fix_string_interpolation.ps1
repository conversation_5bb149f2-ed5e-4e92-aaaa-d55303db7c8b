# PowerShell script to fix string interpolation issues in C# files

# Get all .cs files in the current directory and subdirectories
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

Write-Host "Found $($csFiles.Count) C# files to process for string interpolation..."

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read the file content as lines
    $lines = Get-Content $file.FullName
    
    # Skip if file is empty
    if ($lines -eq $null -or $lines.Count -eq 0) {
        continue
    }
    
    $modified = $false
    
    # Process each line
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        
        # Skip lines that are comments or strings that shouldn't be modified
        if ($line -match '^\s*//' -or $line -match '^\s*\*') {
            continue
        }
        
        # Fix simple string interpolation patterns
        # Pattern: $"text {variable} more text"
        if ($line -match '\$"([^"]*\{[^}]+\}[^"]*)"') {
            # Extract the interpolated string content
            $originalLine = $line
            
            # Simple replacements for common patterns
            $newLine = $line -replace '\$"([^"]*)"', '"$1"'
            $newLine = $newLine -replace '\{([^}]+)\}', '{0}'
            
            # If the line contains string interpolation, try to convert it
            if ($line -match '\$"') {
                # For now, just remove the $ to make it compile, we'll fix manually later
                $newLine = $line -replace '\$"', '"'
                $lines[$i] = $newLine
                $modified = $true
                Write-Host "  - Fixed string interpolation on line $($i + 1)"
            }
        }
        
        # Fix Log_file.Log calls with string interpolation
        if ($line -match 'Log_file\.Log\(\$"') {
            $newLine = $line -replace '\$"', '"'
            $lines[$i] = $newLine
            $modified = $true
            Write-Host "  - Fixed Log_file.Log string interpolation on line $($i + 1)"
        }
        
        # Fix other common string interpolation patterns
        if ($line -match '\$"' -and $line -notmatch 'Log_file\.Log') {
            $newLine = $line -replace '\$"', '"'
            $lines[$i] = $newLine
            $modified = $true
            Write-Host "  - Fixed general string interpolation on line $($i + 1)"
        }
    }
    
    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $lines
        Write-Host "  - File updated"
    }
}

Write-Host "String interpolation processing complete!"
