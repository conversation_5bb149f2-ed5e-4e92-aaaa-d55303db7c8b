// Decompiled with JetBrains decompiler
// Type: ipis.Checksum
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Diagnostics;

namespace ipis
{

public class Checksum
{
  [DebuggerNonUserCode]
  public Checksum()
  {
  }

  public static void prepare_checksum(ref byte[] str, ushort length)
  {
    ushort index = 2;
    ushort num1 = ushort.MaxValue;
    while ((int) index < checked ((int) length - 2))
    {
      ushort num2 = (ushort) ((uint) (ushort) str[(int) index] << 8);
      byte num3 = 0;
      while (num3 < (byte) 8)
      {
        if ((((int) num1 ^ (int) num2) & 32768 /*0x8000*/) != 0)
          num1 = checked ((ushort) ((int) unchecked ((ushort) ((uint) num1 << 1)) ^ 4129));
        else
          num1 <<= 1;
        num2 <<= 1;
        checked { ++num3; }
      }
      checked { ++index; }
    }
    str[checked ((int) length - 1)] = checked ((byte) ((int) num1 & (int) byte.MaxValue));
    str[checked ((int) length - 2)] = checked ((byte) (((int) num1 & 65280) >> 8));
  }

  public static int Checksum_Calc(ref byte[] rxbuf, ushort Pkt_length)
  {
    ushort index = 0;
    ushort num1 = ushort.MaxValue;
    while ((int) index < checked ((int) Pkt_length - 2))
    {
      ushort num2 = (ushort) ((uint) (ushort) rxbuf[(int) index] << 8);
      byte num3 = 0;
      while (num3 < (byte) 8)
      {
        if ((((int) num1 ^ (int) num2) & 32768 /*0x8000*/) != 0)
          num1 = checked ((ushort) ((int) unchecked ((ushort) ((uint) num1 << 1)) ^ 4129));
        else
          num1 <<= 1;
        checked { ++num3; }
        num2 <<= 1;
      }
      checked { ++index; }
    }
    ushort num4 = (ushort) ((int) (ushort) ((uint) (ushort) rxbuf[checked ((int) Pkt_length - 2)] << 8) | (int) rxbuf[checked ((int) Pkt_length - 1)]);
    return (int) num1 == (int) num4 ? 1 : 0;
  }
}

}