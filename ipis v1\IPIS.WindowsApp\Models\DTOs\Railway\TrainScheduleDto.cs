using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// Data Transfer Object for train schedule information from Railway API
    /// Maps external API data to internal system format
    /// </summary>
    public class TrainScheduleDto
    {
        /// <summary>
        /// Train number from the Railway API
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Train name from the Railway API
        /// </summary>
        [StringLength(100)]
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Station code where this schedule applies
        /// </summary>
        [Required]
        [StringLength(10)]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Scheduled arrival time from API
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Scheduled departure time from API
        /// </summary>
        public DateTime? ScheduledDeparture { get; set; }

        /// <summary>
        /// Expected arrival time (with delays) from API
        /// </summary>
        public DateTime? ExpectedArrival { get; set; }

        /// <summary>
        /// Expected departure time (with delays) from API
        /// </summary>
        public DateTime? ExpectedDeparture { get; set; }

        /// <summary>
        /// Platform number assigned by Railway API
        /// </summary>
        [StringLength(10)]
        public string PlatformNumber { get; set; } = string.Empty;

        /// <summary>
        /// Current status from Railway API
        /// </summary>
        [StringLength(50)]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Delay in minutes from Railway API
        /// </summary>
        [Range(0, 1440)] // Max 24 hours delay
        public int DelayMinutes { get; set; }

        /// <summary>
        /// Reason for delay from Railway API
        /// </summary>
        [StringLength(200)]
        public string DelayReason { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the train is cancelled
        /// </summary>
        public bool IsCancelled { get; set; }

        /// <summary>
        /// Source station code
        /// </summary>
        [StringLength(10)]
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// Destination station code
        /// </summary>
        [StringLength(10)]
        public string Destination { get; set; } = string.Empty;

        /// <summary>
        /// Last updated timestamp from Railway API
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// API version that provided this data
        /// </summary>
        [StringLength(10)]
        public string ApiVersion { get; set; } = string.Empty;

        /// <summary>
        /// Route information from Railway API
        /// </summary>
        [StringLength(500)]
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// Train type from Railway API
        /// </summary>
        [StringLength(50)]
        public string TrainType { get; set; } = string.Empty;

        /// <summary>
        /// Operator code from Railway API
        /// </summary>
        [StringLength(10)]
        public string OperatorCode { get; set; } = string.Empty;

        /// <summary>
        /// Sequence number in the route
        /// </summary>
        public int? SequenceNumber { get; set; }

        /// <summary>
        /// Distance from origin station
        /// </summary>
        public decimal? DistanceFromOrigin { get; set; }

        /// <summary>
        /// Halt time at this station in minutes
        /// </summary>
        [Range(0, 120)]
        public int? HaltTime { get; set; }

        /// <summary>
        /// Indicates if this is the origin station
        /// </summary>
        public bool IsOrigin { get; set; }

        /// <summary>
        /// Indicates if this is the destination station
        /// </summary>
        public bool IsDestination { get; set; }

        /// <summary>
        /// Indicates if this is a technical halt
        /// </summary>
        public bool IsTechnicalHalt { get; set; }

        /// <summary>
        /// Expected passenger load
        /// </summary>
        [StringLength(20)]
        public string ExpectedLoad { get; set; } = string.Empty;

        /// <summary>
        /// Additional metadata from Railway API
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Gets the effective arrival time, preferring expected over scheduled time
        /// </summary>
        /// <value>The expected arrival time if available, otherwise the scheduled arrival time</value>
        public DateTime? EffectiveArrival => ExpectedArrival ?? ScheduledArrival;

        /// <summary>
        /// Gets the effective departure time, preferring expected over scheduled time
        /// </summary>
        /// <value>The expected departure time if available, otherwise the scheduled departure time</value>
        public DateTime? EffectiveDeparture => ExpectedDeparture ?? ScheduledDeparture;

        /// <summary>
        /// Gets a value indicating whether the train is delayed
        /// </summary>
        /// <value>True if the train has any delay minutes, false otherwise</value>
        public bool IsDelayed => DelayMinutes > 0;

        /// <summary>
        /// Computed property: Gets the delay description
        /// </summary>
        public string DelayDescription
        {
            get
            {
                if (DelayMinutes == 0) return "On Time";
                if (DelayMinutes < 60) return $"{DelayMinutes} min late";

                var hours = DelayMinutes / 60;
                var minutes = DelayMinutes % 60;

                if (minutes == 0) return $"{hours}h late";
                return $"{hours}h {minutes}m late";
            }
        }

        /// <summary>
        /// Computed property: Gets the route description
        /// </summary>
        public string RouteDescription => !string.IsNullOrWhiteSpace(Route)
            ? Route
            : $"{Source} to {Destination}";

        /// <summary>
        /// Validates the DTO data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TrainNumber) &&
                   !string.IsNullOrWhiteSpace(StationCode) &&
                   (ScheduledArrival.HasValue || ScheduledDeparture.HasValue) &&
                   LastUpdated != default;
        }

        /// <summary>
        /// Gets a summary of the schedule for display
        /// </summary>
        /// <returns>Schedule summary string</returns>
        public string GetSummary()
        {
            var summary = $"Train {TrainNumber}";
            if (!string.IsNullOrWhiteSpace(TrainName))
                summary += $" ({TrainName})";

            summary += $" at {StationCode}";

            if (!string.IsNullOrWhiteSpace(PlatformNumber))
                summary += $", Platform {PlatformNumber}";

            if (IsDelayed)
                summary += $", {DelayDescription}";
            else if (!string.IsNullOrWhiteSpace(Status))
                summary += $", {Status}";

            return summary;
        }

        /// <summary>
        /// Returns a string representation of the schedule
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }

        /// <summary>
        /// Creates a copy of the DTO
        /// </summary>
        /// <returns>Cloned DTO</returns>
        public TrainScheduleDto Clone()
        {
            return new TrainScheduleDto
            {
                TrainNumber = TrainNumber,
                TrainName = TrainName,
                StationCode = StationCode,
                ScheduledArrival = ScheduledArrival,
                ScheduledDeparture = ScheduledDeparture,
                ExpectedArrival = ExpectedArrival,
                ExpectedDeparture = ExpectedDeparture,
                PlatformNumber = PlatformNumber,
                Status = Status,
                DelayMinutes = DelayMinutes,
                DelayReason = DelayReason,
                IsCancelled = IsCancelled,
                Source = Source,
                Destination = Destination,
                LastUpdated = LastUpdated,
                ApiVersion = ApiVersion,
                Route = Route,
                TrainType = TrainType,
                OperatorCode = OperatorCode,
                SequenceNumber = SequenceNumber,
                DistanceFromOrigin = DistanceFromOrigin,
                HaltTime = HaltTime,
                IsOrigin = IsOrigin,
                IsDestination = IsDestination,
                IsTechnicalHalt = IsTechnicalHalt,
                ExpectedLoad = ExpectedLoad,
                Metadata = new Dictionary<string, object>(Metadata)
            };
        }
    }
}
