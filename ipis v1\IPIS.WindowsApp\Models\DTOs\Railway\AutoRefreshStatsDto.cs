namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for auto-refresh statistics
    /// </summary>
    public class AutoRefreshStatsDto
    {
        /// <summary>
        /// Total number of auto-refresh configurations
        /// </summary>
        public int TotalConfigurations { get; set; }

        /// <summary>
        /// Number of active configurations
        /// </summary>
        public int ActiveConfigurations { get; set; }

        /// <summary>
        /// Number of station configurations
        /// </summary>
        public int StationConfigurations { get; set; }

        /// <summary>
        /// Number of train configurations
        /// </summary>
        public int TrainConfigurations { get; set; }

        /// <summary>
        /// Total number of refresh failures
        /// </summary>
        public int TotalFailures { get; set; }

        /// <summary>
        /// Total number of successful refreshes
        /// </summary>
        public int TotalSuccesses { get; set; }

        /// <summary>
        /// Last refresh time
        /// </summary>
        public DateTime? LastRefreshTime { get; set; }

        /// <summary>
        /// Service status
        /// </summary>
        public string ServiceStatus { get; set; } = string.Empty;

        /// <summary>
        /// Service uptime
        /// </summary>
        public TimeSpan ServiceUptime { get; set; }

        /// <summary>
        /// Average refresh interval in minutes
        /// </summary>
        public double AverageRefreshInterval { get; set; }

        /// <summary>
        /// Success rate percentage
        /// </summary>
        public double SuccessRate
        {
            get
            {
                var total = TotalSuccesses + TotalFailures;
                return total > 0 ? (double)TotalSuccesses / total * 100 : 0;
            }
        }

        /// <summary>
        /// Failure rate percentage
        /// </summary>
        public double FailureRate
        {
            get
            {
                var total = TotalSuccesses + TotalFailures;
                return total > 0 ? (double)TotalFailures / total * 100 : 0;
            }
        }

        /// <summary>
        /// Time since last refresh
        /// </summary>
        public TimeSpan? TimeSinceLastRefresh
        {
            get
            {
                return LastRefreshTime.HasValue ? DateTime.UtcNow - LastRefreshTime.Value : null;
            }
        }

        /// <summary>
        /// Service status color for UI
        /// </summary>
        public string StatusColor
        {
            get
            {
                return ServiceStatus.ToLower() switch
                {
                    "running" => "Green",
                    "paused" => "Orange",
                    "stopped" => "Gray",
                    "error" => "Red",
                    _ => "Gray"
                };
            }
        }

        /// <summary>
        /// Health status based on success rate
        /// </summary>
        public string HealthStatus
        {
            get
            {
                if (SuccessRate >= 95) return "Excellent";
                if (SuccessRate >= 85) return "Good";
                if (SuccessRate >= 70) return "Fair";
                if (SuccessRate >= 50) return "Poor";
                return "Critical";
            }
        }

        /// <summary>
        /// Health status color for UI
        /// </summary>
        public string HealthColor
        {
            get
            {
                return HealthStatus switch
                {
                    "Excellent" => "Green",
                    "Good" => "LightGreen",
                    "Fair" => "Yellow",
                    "Poor" => "Orange",
                    "Critical" => "Red",
                    _ => "Gray"
                };
            }
        }
    }
}
