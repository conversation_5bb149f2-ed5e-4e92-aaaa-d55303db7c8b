using NUnit.Framework;
using FluentAssertions;
using AutoFixture;
using AutoFixture.NUnit3;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Tests.UnitTests.Models.Entities
{
    /// <summary>
    /// Comprehensive test suite for Platform entity
    /// Tests every line of code and business logic
    /// </summary>
    [TestFixture]
    public class PlatformTests
    {
        private IFixture _fixture;

        [SetUp]
        public void SetUp()
        {
            _fixture = new Fixture();
            _fixture.Customize<Platform>(c => c
                .With(p => p.StationId, 1)
                .With(p => p.PlatformNumber, "1")
                .With(p => p.IsActive, true)
                .With(p => p.PlatformType, PlatformType.Passenger));
        }

        #region Constructor and Property Tests

        [Test]
        public void Platform_DefaultConstructor_SetsDefaultValues()
        {
            // Act
            var platform = new Platform();

            // Assert
            platform.Id.Should().Be(0);
            platform.StationId.Should().Be(0);
            platform.PlatformNumber.Should().Be(string.Empty);
            platform.PlatformType.Should().Be(PlatformType.Passenger);
            platform.IsActive.Should().BeTrue();
            platform.HasCover.Should().BeFalse();
            platform.IsAccessible.Should().BeFalse();
            platform.HasLighting.Should().BeTrue();
            platform.HasSeating.Should().BeFalse();
            platform.HasWaterFacility.Should().BeFalse();
            platform.HasRestroom.Should().BeFalse();
            platform.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            platform.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            platform.Schedules.Should().NotBeNull().And.BeEmpty();
            platform.DisplayBoards.Should().NotBeNull().And.BeEmpty();
            platform.Messages.Should().NotBeNull().And.BeEmpty();
        }

        [Test]
        [AutoData]
        public void Platform_SetProperties_SetsCorrectly(int stationId, string platformNumber, string platformName)
        {
            // Arrange
            var platform = new Platform();
            var platformType = PlatformType.Freight;

            // Act
            platform.StationId = stationId;
            platform.PlatformNumber = platformNumber;
            platform.PlatformName = platformName;
            platform.PlatformType = platformType;
            platform.IsActive = false;
            platform.HasCover = true;
            platform.IsAccessible = true;

            // Assert
            platform.StationId.Should().Be(stationId);
            platform.PlatformNumber.Should().Be(platformNumber);
            platform.PlatformName.Should().Be(platformName);
            platform.PlatformType.Should().Be(platformType);
            platform.IsActive.Should().BeFalse();
            platform.HasCover.Should().BeTrue();
            platform.IsAccessible.Should().BeTrue();
        }

        #endregion

        #region Validation Tests

        [Test]
        public void Platform_ValidData_PassesValidation()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            var validationContext = new ValidationContext(platform);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(platform, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeTrue();
            validationResults.Should().BeEmpty();
        }

        [Test]
        [TestCase("", "Platform number is required")]
        [TestCase(null, "Platform number is required")]
        public void Platform_InvalidPlatformNumber_FailsValidation(string platformNumber, string expectedError)
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = platformNumber;
            var validationContext = new ValidationContext(platform);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(platform, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        public void Platform_PlatformNumberTooLong_FailsValidation()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = new string('1', 11); // 11 characters, max is 10
            var validationContext = new ValidationContext(platform);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(platform, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("cannot exceed 10 characters"));
        }

        [Test]
        public void Platform_PlatformNameTooLong_FailsValidation()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformName = new string('A', 51); // 51 characters, max is 50
            var validationContext = new ValidationContext(platform);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(platform, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("cannot exceed 50 characters"));
        }

        #endregion

        #region Computed Properties Tests

        [Test]
        public void DisplayName_WithPlatformName_ReturnsFormattedString()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "1";
            platform.PlatformName = "Main Platform";

            // Act & Assert
            platform.DisplayName.Should().Be("Platform 1 (Main Platform)");
        }

        [Test]
        public void DisplayName_WithoutPlatformName_ReturnsSimpleFormat()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "2A";
            platform.PlatformName = null;

            // Act & Assert
            platform.DisplayName.Should().Be("Platform 2A");
        }

        [Test]
        public void DisplayName_WithEmptyPlatformName_ReturnsSimpleFormat()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "3";
            platform.PlatformName = "";

            // Act & Assert
            platform.DisplayName.Should().Be("Platform 3");
        }

        [Test]
        public void FullIdentifier_WithStation_ReturnsFormattedString()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "1";
            platform.Station = new Station { Code = "NYC" };

            // Act & Assert
            platform.FullIdentifier.Should().Be("NYC-1");
        }

        [Test]
        public void FullIdentifier_WithoutStation_ReturnsFormattedString()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "1";
            platform.Station = null!;

            // Act & Assert
            platform.FullIdentifier.Should().Be("-1");
        }

        [Test]
        public void StatusDescription_WhenActive_ReturnsActive()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.IsActive = true;

            // Act & Assert
            platform.StatusDescription.Should().Be("Active");
        }

        [Test]
        public void StatusDescription_WhenInactive_ReturnsInactive()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.IsActive = false;

            // Act & Assert
            platform.StatusDescription.Should().Be("Inactive");
        }

        [Test]
        public void AccessibilityStatus_WhenAccessible_ReturnsAccessible()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.IsAccessible = true;

            // Act & Assert
            platform.AccessibilityStatus.Should().Be("Accessible");
        }

        [Test]
        public void AccessibilityStatus_WhenNotAccessible_ReturnsNotAccessible()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.IsAccessible = false;

            // Act & Assert
            platform.AccessibilityStatus.Should().Be("Not Accessible");
        }

        [Test]
        public void DisplayBoardCount_WithDisplayBoards_ReturnsCorrectCount()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            var displayBoards = _fixture.CreateMany<DisplayBoard>(3).ToList();
            platform.DisplayBoards = displayBoards;

            // Act & Assert
            platform.DisplayBoardCount.Should().Be(3);
        }

        [Test]
        public void ActiveScheduleCount_WithMixedSchedules_ReturnsCorrectCount()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            var schedules = new List<Schedule>
            {
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.Scheduled).Create(),
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.Cancelled).Create(),
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.OnTime).Create(),
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.Departed).Create()
            };
            platform.Schedules = schedules;

            // Act & Assert
            platform.ActiveScheduleCount.Should().Be(2); // Scheduled and OnTime
        }

        #endregion

        #region Method Tests

        [Test]
        public void UpdateTimestamp_WhenCalled_UpdatesTimestamp()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            var originalTimestamp = platform.UpdatedAt;
            Thread.Sleep(10); // Ensure time difference

            // Act
            platform.UpdateTimestamp();

            // Assert
            platform.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void UpdateTimestamp_WithUser_UpdatesTimestampAndUser()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            var originalTimestamp = platform.UpdatedAt;
            var updatedBy = "testuser";
            Thread.Sleep(10); // Ensure time difference

            // Act
            platform.UpdateTimestamp(updatedBy);

            // Assert
            platform.UpdatedAt.Should().BeAfter(originalTimestamp);
            platform.UpdatedBy.Should().Be(updatedBy);
        }

        [Test]
        public void IsValid_WithValidData_ReturnsTrue()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.StationId = 1;
            platform.PlatformNumber = "1";

            // Act & Assert
            platform.IsValid().Should().BeTrue();
        }

        [Test]
        [TestCase(0, "1", false)]
        [TestCase(1, "", false)]
        [TestCase(1, null, false)]
        [TestCase(1, "VERYLONGPLATFORMNUMBER", false)] // Too long
        public void IsValid_WithInvalidData_ReturnsFalse(int stationId, string platformNumber, bool expected)
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.StationId = stationId;
            platform.PlatformNumber = platformNumber;

            // Act & Assert
            platform.IsValid().Should().Be(expected);
        }

        [Test]
        [TestCase(5, 3, true)]
        [TestCase(5, 5, true)]
        [TestCase(5, 6, false)]
        [TestCase(null, 10, true)] // No limit set
        public void CanAccommodate_WithDifferentCoachCounts_ReturnsExpectedResult(int? maxCoaches, int coachCount, bool expected)
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.MaxCoaches = maxCoaches;

            // Act & Assert
            platform.CanAccommodate(coachCount).Should().Be(expected);
        }

        [Test]
        public void GetSummary_WithValidData_ReturnsFormattedSummary()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "1";
            platform.PlatformName = "Main Platform";
            platform.PlatformType = PlatformType.Passenger;
            platform.Length = 250.5m;
            platform.IsActive = true;

            // Act
            var summary = platform.GetSummary();

            // Assert
            summary.Should().Contain("Platform 1 (Main Platform)");
            summary.Should().Contain("Passenger");
            summary.Should().Contain("250.5m");
            summary.Should().Contain("Active");
        }

        [Test]
        public void GetSummary_WithoutLength_ShowsNA()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "1";
            platform.Length = null;

            // Act
            var summary = platform.GetSummary();

            // Assert
            summary.Should().Contain("N/A");
        }

        [Test]
        public void GetAvailableFacilities_WithAllFacilities_ReturnsAllFacilities()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.HasCover = true;
            platform.IsAccessible = true;
            platform.HasLighting = true;
            platform.HasSeating = true;
            platform.HasWaterFacility = true;
            platform.HasRestroom = true;

            // Act
            var facilities = platform.GetAvailableFacilities();

            // Assert
            facilities.Should().Contain("Covered");
            facilities.Should().Contain("Wheelchair Accessible");
            facilities.Should().Contain("Lighting");
            facilities.Should().Contain("Seating");
            facilities.Should().Contain("Water");
            facilities.Should().Contain("Restroom");
            facilities.Should().HaveCount(6);
        }

        [Test]
        public void GetAvailableFacilities_WithNoFacilities_ReturnsEmptyList()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.HasCover = false;
            platform.IsAccessible = false;
            platform.HasLighting = false;
            platform.HasSeating = false;
            platform.HasWaterFacility = false;
            platform.HasRestroom = false;

            // Act
            var facilities = platform.GetAvailableFacilities();

            // Assert
            facilities.Should().BeEmpty();
        }

        [Test]
        public void ToString_ReturnsDisplayName()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.PlatformNumber = "1";
            platform.PlatformName = "Main Platform";

            // Act
            var result = platform.ToString();

            // Assert
            result.Should().Be("Platform 1 (Main Platform)");
        }

        #endregion

        #region Equality Tests

        [Test]
        public void Equals_WithSameIdStationIdAndPlatformNumber_ReturnsTrue()
        {
            // Arrange
            var platform1 = _fixture.Create<Platform>();
            platform1.Id = 1;
            platform1.StationId = 1;
            platform1.PlatformNumber = "1";

            var platform2 = _fixture.Create<Platform>();
            platform2.Id = 1;
            platform2.StationId = 1;
            platform2.PlatformNumber = "1";

            // Act & Assert
            platform1.Equals(platform2).Should().BeTrue();
        }

        [Test]
        public void Equals_WithDifferentId_ReturnsFalse()
        {
            // Arrange
            var platform1 = _fixture.Create<Platform>();
            platform1.Id = 1;
            platform1.StationId = 1;
            platform1.PlatformNumber = "1";

            var platform2 = _fixture.Create<Platform>();
            platform2.Id = 2;
            platform2.StationId = 1;
            platform2.PlatformNumber = "1";

            // Act & Assert
            platform1.Equals(platform2).Should().BeFalse();
        }

        [Test]
        public void Equals_WithNull_ReturnsFalse()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();

            // Act & Assert
            platform.Equals(null).Should().BeFalse();
        }

        [Test]
        public void GetHashCode_WithSameData_ReturnsSameHashCode()
        {
            // Arrange
            var platform1 = _fixture.Create<Platform>();
            platform1.Id = 1;
            platform1.StationId = 1;
            platform1.PlatformNumber = "1";

            var platform2 = _fixture.Create<Platform>();
            platform2.Id = 1;
            platform2.StationId = 1;
            platform2.PlatformNumber = "1";

            // Act & Assert
            platform1.GetHashCode().Should().Be(platform2.GetHashCode());
        }

        #endregion

        #region Edge Cases and Boundary Tests

        [Test]
        public void Platform_WithMaxLengthValues_ShouldBeValid()
        {
            // Arrange
            var platform = new Platform
            {
                StationId = 1,
                PlatformNumber = new string('1', 10), // Max length
                PlatformName = new string('A', 50), // Max length
                TrackNumber = new string('2', 10), // Max length
                PlatformSide = new string('L', 10), // Max length
                SurfaceType = new string('C', 20), // Max length
                SafetyFeatures = new string('S', 200), // Max length
                Remarks = new string('R', 500) // Max length
            };

            // Act & Assert
            platform.IsValid().Should().BeTrue();
        }

        [Test]
        public void Platform_WithNullCollections_ShouldHandleGracefully()
        {
            // Arrange
            var platform = _fixture.Create<Platform>();
            platform.Schedules = null!;
            platform.DisplayBoards = null!;
            platform.Messages = null!;

            // Act & Assert
            platform.DisplayBoardCount.Should().Be(0);
            platform.ActiveScheduleCount.Should().Be(0);
        }

        #endregion
    }
}
