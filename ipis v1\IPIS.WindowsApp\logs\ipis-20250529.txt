2025-05-29 00:30:22.216 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 00:30:22.422 +05:30 [INF] Applying database migrations
2025-05-29 00:30:22.838 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 00:30:22.913 +05:30 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-05-29 00:30:22.978 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-05-29 00:30:22.985 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-05-29 00:30:22.986 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-29 00:30:22.997 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-29 00:30:22.997 +05:30 [INF] Database migrations completed
2025-05-29 00:30:22.998 +05:30 [FTL] IPIS Windows Application terminated unexpectedly
System.InvalidOperationException: Unable to resolve service for type 'IPIS.WindowsApp.Services.IStationService' while attempting to activate 'IPIS.WindowsApp.Forms.MainForm'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at IPIS.WindowsApp.Program.Main() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Program.cs:line 50
2025-05-29 01:14:52.614 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:14:52.817 +05:30 [INF] Applying database migrations
2025-05-29 01:14:53.223 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:14:53.275 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-05-29 01:14:53.280 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-05-29 01:14:53.285 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-29 01:14:53.296 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-29 01:14:53.296 +05:30 [INF] Database migrations completed
2025-05-29 01:14:53.353 +05:30 [INF] Initializing main form
2025-05-29 01:14:53.354 +05:30 [INF] Loading stations
2025-05-29 01:14:53.357 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:14:53.490 +05:30 [ERR] Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:14:53.501 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IPIS.WindowsApp.Data.IPISDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-05-29 01:14:53.502 +05:30 [ERR] Error retrieving all stations
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\StationService.cs:line 40
2025-05-29 01:14:53.517 +05:30 [ERR] Error loading stations
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\StationService.cs:line 40
   at IPIS.WindowsApp.Forms.MainForm.LoadStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Forms\MainForm.cs:line 61
2025-05-29 01:18:09.912 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:18:10.103 +05:30 [INF] Applying database migrations
2025-05-29 01:18:10.512 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:18:10.564 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-05-29 01:18:10.571 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-05-29 01:18:10.578 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-29 01:18:10.588 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-29 01:18:10.589 +05:30 [INF] Database migrations completed
2025-05-29 01:18:10.666 +05:30 [INF] Initializing main form
2025-05-29 01:18:10.667 +05:30 [INF] Loading stations
2025-05-29 01:18:10.670 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:18:10.798 +05:30 [ERR] Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:18:10.811 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IPIS.WindowsApp.Data.IPISDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-05-29 01:18:10.815 +05:30 [ERR] Error retrieving all stations
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\StationService.cs:line 40
2025-05-29 01:18:10.840 +05:30 [ERR] Error loading stations
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\StationService.cs:line 40
   at IPIS.WindowsApp.Forms.MainForm.LoadStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Forms\MainForm.cs:line 61
2025-05-29 01:19:50.858 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:19:51.057 +05:30 [INF] Creating database schema
2025-05-29 01:19:51.474 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:19:51.530 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-05-29 01:19:51.534 +05:30 [INF] Database schema created successfully
2025-05-29 01:19:51.637 +05:30 [ERR] Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Stations" AS "s")
2025-05-29 01:19:51.648 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IPIS.WindowsApp.Data.IPISDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.ExecuteReader()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__19_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Storage.NonRetryingExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.ExecuteReader()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__19_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Storage.NonRetryingExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-29 01:19:51.649 +05:30 [WRN] Error seeding initial data
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.ExecuteReader()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__19_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Storage.NonRetryingExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method8(Closure , QueryContext )
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at System.Linq.Queryable.Any[TSource](IQueryable`1 source)
   at IPIS.WindowsApp.Program.SeedInitialData(IPISDbContext context) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Program.cs:line 177
2025-05-29 01:19:51.719 +05:30 [INF] Initializing main form
2025-05-29 01:19:51.720 +05:30 [INF] Loading stations
2025-05-29 01:19:51.722 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:19:51.760 +05:30 [ERR] Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:19:51.763 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IPIS.WindowsApp.Data.IPISDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-05-29 01:19:51.763 +05:30 [ERR] Error retrieving all stations
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\StationService.cs:line 40
2025-05-29 01:19:51.764 +05:30 [ERR] Error loading stations
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: Stations'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements(Stopwatch timer)+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\StationService.cs:line 40
   at IPIS.WindowsApp.Forms.MainForm.LoadStationsAsync() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Forms\MainForm.cs:line 61
2025-05-29 01:20:11.717 +05:30 [INF] Main form closing
2025-05-29 01:20:11.741 +05:30 [INF] IPIS Windows Application stopped
2025-05-29 01:21:16.624 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:21:16.823 +05:30 [INF] Ensuring database is deleted and recreated
2025-05-29 01:21:17.239 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:21:17.307 +05:30 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-05-29 01:21:17.394 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Stations" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Stations" PRIMARY KEY AUTOINCREMENT,
    "Code" TEXT NOT NULL,
    "Name" TEXT NOT NULL,
    "Location" TEXT NULL,
    "Type" INTEGER NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
);
2025-05-29 01:21:17.394 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Trains" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Trains" PRIMARY KEY AUTOINCREMENT,
    "TrainNumber" TEXT NOT NULL,
    "TrainName" TEXT NULL,
    "TrainType" TEXT NULL,
    "IsActive" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now'))
);
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "DisplayBoards" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_DisplayBoards" PRIMARY KEY AUTOINCREMENT,
    "StationId" INTEGER NOT NULL,
    "BoardType" TEXT NOT NULL,
    "BoardName" TEXT NOT NULL,
    "IpAddress" TEXT NULL,
    "IsOnline" INTEGER NOT NULL,
    "LastHeartbeat" TEXT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    CONSTRAINT "FK_DisplayBoards_Stations_StationId" FOREIGN KEY ("StationId") REFERENCES "Stations" ("Id") ON DELETE CASCADE
);
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Platforms" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Platforms" PRIMARY KEY AUTOINCREMENT,
    "StationId" INTEGER NOT NULL,
    "PlatformNumber" TEXT NOT NULL,
    "PlatformName" TEXT NULL,
    "IsActive" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    CONSTRAINT "FK_Platforms_Stations_StationId" FOREIGN KEY ("StationId") REFERENCES "Stations" ("Id") ON DELETE CASCADE
);
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Messages" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Messages" PRIMARY KEY AUTOINCREMENT,
    "DisplayBoardId" INTEGER NOT NULL,
    "MessageType" TEXT NOT NULL,
    "Content" TEXT NOT NULL,
    "Language" TEXT NOT NULL DEFAULT 'en',
    "Priority" INTEGER NOT NULL,
    "IsActive" INTEGER NOT NULL,
    "ValidFrom" TEXT NOT NULL,
    "ValidTo" TEXT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    CONSTRAINT "FK_Messages_DisplayBoards_DisplayBoardId" FOREIGN KEY ("DisplayBoardId") REFERENCES "DisplayBoards" ("Id") ON DELETE CASCADE
);
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Schedules" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Schedules" PRIMARY KEY AUTOINCREMENT,
    "TrainId" INTEGER NOT NULL,
    "StationId" INTEGER NOT NULL,
    "PlatformId" INTEGER NULL,
    "ScheduledArrival" TEXT NULL,
    "ScheduledDeparture" TEXT NULL,
    "ActualArrival" TEXT NULL,
    "ActualDeparture" TEXT NULL,
    "Status" TEXT NOT NULL,
    "DelayMinutes" INTEGER NOT NULL,
    "Remarks" TEXT NULL,
    "CreatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    "UpdatedAt" TEXT NOT NULL DEFAULT (datetime('now')),
    CONSTRAINT "FK_Schedules_Platforms_PlatformId" FOREIGN KEY ("PlatformId") REFERENCES "Platforms" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_Schedules_Stations_StationId" FOREIGN KEY ("StationId") REFERENCES "Stations" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Schedules_Trains_TrainId" FOREIGN KEY ("TrainId") REFERENCES "Trains" ("Id") ON DELETE CASCADE
);
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_DisplayBoards_StationId_BoardName" ON "DisplayBoards" ("StationId", "BoardName");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Messages_DisplayBoardId" ON "Messages" ("DisplayBoardId");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Platforms_StationId_PlatformNumber" ON "Platforms" ("StationId", "PlatformNumber");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Schedules_PlatformId" ON "Schedules" ("PlatformId");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Schedules_StationId" ON "Schedules" ("StationId");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Schedules_TrainId" ON "Schedules" ("TrainId");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Stations_Code" ON "Stations" ("Code");
2025-05-29 01:21:17.395 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Trains_TrainNumber" ON "Trains" ("TrainNumber");
2025-05-29 01:21:17.400 +05:30 [INF] Database schema created successfully
2025-05-29 01:21:17.501 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Stations" AS "s")
2025-05-29 01:21:17.505 +05:30 [INF] Seeding initial data
2025-05-29 01:21:17.621 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 5), @p4='?' (Size = 9), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:21:17.633 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 6), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:21:17.634 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 9), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:21:17.652 +05:30 [INF] Initial data seeded successfully
2025-05-29 01:21:17.709 +05:30 [INF] Initializing main form
2025-05-29 01:21:17.709 +05:30 [INF] Loading stations
2025-05-29 01:21:17.715 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:21:17.750 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:21:17.769 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:21:26.209 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
WHERE "s"."Id" = @__id_0
LIMIT 1
2025-05-29 01:21:45.250 +05:30 [INF] Loading stations
2025-05-29 01:21:45.253 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:21:45.258 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:22:14.018 +05:30 [INF] Main form closing
2025-05-29 01:22:14.061 +05:30 [INF] IPIS Windows Application stopped
2025-05-29 01:26:08.949 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:26:09.150 +05:30 [INF] Creating database schema manually
2025-05-29 01:26:09.558 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:26:09.644 +05:30 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                    CREATE TABLE IF NOT EXISTS Stations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL UNIQUE,
                        Name TEXT NOT NULL,
                        Location TEXT,
                        Type INTEGER NOT NULL DEFAULT 0,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT NOT NULL
                    );
2025-05-29 01:26:09.645 +05:30 [INF] Database schema created successfully
2025-05-29 01:26:09.746 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Stations" AS "s")
2025-05-29 01:26:09.748 +05:30 [INF] Seeding initial data
2025-05-29 01:26:09.844 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 5), @p4='?' (Size = 9), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:26:09.855 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 6), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:26:09.855 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 9), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:26:09.864 +05:30 [INF] Initial data seeded successfully
2025-05-29 01:26:09.922 +05:30 [INF] Initializing main form
2025-05-29 01:26:09.923 +05:30 [INF] Loading stations
2025-05-29 01:26:09.924 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:26:09.957 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:26:09.973 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:26:16.713 +05:30 [INF] Main form closing
2025-05-29 01:26:16.746 +05:30 [INF] IPIS Windows Application stopped
2025-05-29 01:48:17.725 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:48:17.928 +05:30 [INF] Creating database schema manually
2025-05-29 01:48:18.353 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:48:18.455 +05:30 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                    CREATE TABLE IF NOT EXISTS Stations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL UNIQUE,
                        Name TEXT NOT NULL,
                        Location TEXT,
                        Type INTEGER NOT NULL DEFAULT 0,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT NOT NULL
                    );
2025-05-29 01:48:18.457 +05:30 [INF] Database schema created successfully
2025-05-29 01:48:18.571 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Stations" AS "s")
2025-05-29 01:48:18.573 +05:30 [INF] Seeding initial data
2025-05-29 01:48:18.686 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 5), @p4='?' (Size = 9), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:48:18.699 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 6), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:48:18.700 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 9), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:48:18.711 +05:30 [INF] Initial data seeded successfully
2025-05-29 01:48:18.785 +05:30 [INF] Initializing main form
2025-05-29 01:48:18.785 +05:30 [INF] Loading stations
2025-05-29 01:48:18.787 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:48:18.819 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:48:18.835 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:48:35.503 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
WHERE "s"."Id" = @__id_0
LIMIT 1
2025-05-29 01:48:41.816 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
WHERE "s"."Id" = @__id_0
LIMIT 1
2025-05-29 01:48:56.430 +05:30 [INF] Loading stations
2025-05-29 01:48:56.431 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:48:56.437 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:49:26.586 +05:30 [INF] Loading stations
2025-05-29 01:49:26.588 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:49:26.593 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:49:40.843 +05:30 [INF] Loading stations
2025-05-29 01:49:40.844 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:49:40.849 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:49:57.863 +05:30 [INF] Main form closing
2025-05-29 01:49:57.895 +05:30 [INF] IPIS Windows Application stopped
2025-05-29 01:59:44.464 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 01:59:44.667 +05:30 [INF] Creating database schema manually
2025-05-29 01:59:45.077 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:59:45.155 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                    CREATE TABLE IF NOT EXISTS Stations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL UNIQUE,
                        Name TEXT NOT NULL,
                        Location TEXT,
                        Type INTEGER NOT NULL DEFAULT 0,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT NOT NULL
                    );
2025-05-29 01:59:45.157 +05:30 [INF] Database schema created successfully
2025-05-29 01:59:45.262 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Stations" AS "s")
2025-05-29 01:59:45.264 +05:30 [INF] Seeding initial data
2025-05-29 01:59:45.363 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 5), @p4='?' (Size = 9), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:59:45.374 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 6), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:59:45.375 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (Size = 3), @p1='?' (DbType = DateTime), @p2='?' (DbType = Boolean), @p3='?' (Size = 9), @p4='?' (Size = 14), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Stations" ("Code", "CreatedAt", "IsActive", "Location", "Name", "Type", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
SELECT "Id"
FROM "Stations"
WHERE changes() = 1 AND "rowid" = last_insert_rowid();
2025-05-29 01:59:45.383 +05:30 [INF] Initial data seeded successfully
2025-05-29 01:59:45.440 +05:30 [INF] Initializing main form
2025-05-29 01:59:45.441 +05:30 [INF] Loading stations
2025-05-29 01:59:45.443 +05:30 [INF] Entity Framework Core 6.0.25 initialized 'IPISDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:6.0.25' with options: DetailedErrorsEnabled 
2025-05-29 01:59:45.475 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
ORDER BY "s"."Name"
2025-05-29 01:59:45.491 +05:30 [INF] Successfully loaded 3 stations
2025-05-29 01:59:48.364 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT "s"."Id", "s"."Code", "s"."CreatedAt", "s"."IsActive", "s"."Location", "s"."Name", "s"."Type", "s"."UpdatedAt"
FROM "Stations" AS "s"
WHERE "s"."Id" = @__id_0
LIMIT 1
2025-05-29 02:00:04.299 +05:30 [INF] Railway API Service initialized with base URL: https://api.railway.gov.in
2025-05-29 02:00:04.301 +05:30 [INF] Auto-refresh service initialized
2025-05-29 02:00:04.304 +05:30 [INF] Initializing train management form
2025-05-29 02:00:04.320 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:04.323 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:04.403 +05:30 [WRN] Retry attempt 1 for health after 2000ms
2025-05-29 02:00:06.414 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:06.414 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:06.416 +05:30 [WRN] Retry attempt 2 for health after 4000ms
2025-05-29 02:00:10.426 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:10.426 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:10.434 +05:30 [WRN] Retry attempt 3 for health after 8000ms
2025-05-29 02:00:18.445 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:18.445 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/health
2025-05-29 02:00:18.455 +05:30 [ERR] Error during API health check
System.Net.Http.HttpRequestException: No such host is known. (api.railway.gov.in:443)
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.<>c__DisplayClass38_0.<<ExecuteApiRequestAsync>b__0>d.MoveNext() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 888
--- End of stack trace from previous location ---
   at Polly.CircuitBreaker.AsyncCircuitBreakerEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, ICircuitController`1 breakerController)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.ExecuteApiRequestAsync(String endpoint, CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 886
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.GetApiHealthAsync(CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 590
2025-05-29 02:00:42.065 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:42.065 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:42.071 +05:30 [WRN] Retry attempt 1 for trains/status/12031 after 2000ms
2025-05-29 02:00:44.073 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:44.073 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:44.074 +05:30 [WRN] Retry attempt 2 for trains/status/12031 after 4000ms
2025-05-29 02:00:48.084 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:48.084 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:48.091 +05:30 [WRN] Retry attempt 3 for trains/status/12031 after 8000ms
2025-05-29 02:00:56.095 +05:30 [INF] Start processing HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:56.095 +05:30 [INF] Sending HTTP request GET https://api.railway.gov.in/api/v1/trains/status/12031
2025-05-29 02:00:56.101 +05:30 [ERR] Error getting status for train 12031
System.Net.Http.HttpRequestException: No such host is known. (api.railway.gov.in:443)
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.<>c__DisplayClass38_0.<<ExecuteApiRequestAsync>b__0>d.MoveNext() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 888
--- End of stack trace from previous location ---
   at Polly.CircuitBreaker.AsyncCircuitBreakerEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, ICircuitController`1 breakerController)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.ExecuteApiRequestAsync(String endpoint, CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 886
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.GetTrainStatusAsync(String trainNumber, CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 173
2025-05-29 02:00:56.104 +05:30 [ERR] Error getting train status for 12031 
System.Net.Http.HttpRequestException: No such host is known. (api.railway.gov.in:443)
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|277_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.<>c__DisplayClass38_0.<<ExecuteApiRequestAsync>b__0>d.MoveNext() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 888
--- End of stack trace from previous location ---
   at Polly.CircuitBreaker.AsyncCircuitBreakerEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, ICircuitController`1 breakerController)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteAsync(Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.ExecuteApiRequestAsync(String endpoint, CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 886
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.GetTrainStatusAsync(String trainNumber, CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 173
   at IPIS.WindowsApp.Services.Implementations.RailwayApiService.GetLiveTrainStatusAsync(String trainNumber, CancellationToken cancellationToken) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Services\Implementations\RailwayApiService.cs:line 939
   at IPIS.WindowsApp.Forms.TrainManagementForm.getTrainStatusButton_Click(Object sender, EventArgs e) in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Forms\TrainManagementForm.cs:line 109
2025-05-29 02:01:00.058 +05:30 [INF] Saved auto-refresh configuration for Train 12031
2025-05-29 02:01:19.414 +05:30 [INF] Train management form closing
2025-05-29 02:01:20.599 +05:30 [INF] Main form closing
2025-05-29 02:01:20.617 +05:30 [INF] IPIS Windows Application stopped
