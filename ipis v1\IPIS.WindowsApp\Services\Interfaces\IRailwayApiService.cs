using IPIS.WindowsApp.Models.DTOs.Railway;
using IPIS.WindowsApp.Models.Entities;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Interface for Railway API service operations
    /// Provides comprehensive integration with external Railway APIs for real-time data synchronization
    /// </summary>
    public interface IRailwayApiService
    {
        #region Schedule Operations

        /// <summary>
        /// Gets train schedules for a specific station and date from Railway API
        /// </summary>
        /// <param name="stationCode">Station code to get schedules for</param>
        /// <param name="date">Date to get schedules for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of train schedules</returns>
        Task<List<TrainScheduleDto>> GetTrainSchedulesAsync(string stationCode, DateTime date, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets train schedules for multiple stations
        /// </summary>
        /// <param name="stationCodes">List of station codes</param>
        /// <param name="date">Date to get schedules for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary of station codes and their schedules</returns>
        Task<Dictionary<string, List<TrainScheduleDto>>> GetTrainSchedulesAsync(List<string> stationCodes, DateTime date, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets schedule for a specific train at a specific station
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="stationCode">Station code</param>
        /// <param name="date">Date to get schedule for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Train schedule or null if not found</returns>
        Task<TrainScheduleDto?> GetTrainScheduleAsync(string trainNumber, string stationCode, DateTime date, CancellationToken cancellationToken = default);

        #endregion

        #region Train Status Operations

        /// <summary>
        /// Gets current status of a specific train from Railway API
        /// </summary>
        /// <param name="trainNumber">Train number to get status for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Train status information</returns>
        Task<TrainStatusDto?> GetTrainStatusAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets status for multiple trains
        /// </summary>
        /// <param name="trainNumbers">List of train numbers</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary of train numbers and their status</returns>
        Task<Dictionary<string, TrainStatusDto>> GetTrainStatusesAsync(List<string> trainNumbers, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets live train positions for trains in a specific region
        /// </summary>
        /// <param name="regionCode">Region or zone code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of train statuses in the region</returns>
        Task<List<TrainStatusDto>> GetLiveTrainPositionsAsync(string regionCode, CancellationToken cancellationToken = default);

        #endregion

        #region Delay Information Operations

        /// <summary>
        /// Gets current delay information from Railway API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of delay information</returns>
        Task<List<DelayInfoDto>> GetDelayUpdatesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets delay information for a specific station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of delays affecting the station</returns>
        Task<List<DelayInfoDto>> GetStationDelayUpdatesAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets delay information for a specific train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of delays for the train</returns>
        Task<List<DelayInfoDto>> GetTrainDelayUpdatesAsync(string trainNumber, CancellationToken cancellationToken = default);

        #endregion

        #region Platform Assignment Operations

        /// <summary>
        /// Gets platform assignments for a specific station from Railway API
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of platform assignments</returns>
        Task<List<PlatformAssignmentDto>> GetPlatformAssignmentsAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets platform assignment for a specific train at a station
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Platform assignment or null if not found</returns>
        Task<PlatformAssignmentDto?> GetTrainPlatformAssignmentAsync(string trainNumber, string stationCode, CancellationToken cancellationToken = default);

        #endregion

        #region Synchronization Operations

        /// <summary>
        /// Performs full synchronization of schedule data from Railway API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if synchronization was successful</returns>
        Task<bool> SyncScheduleDataAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs incremental synchronization of train status data
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if synchronization was successful</returns>
        Task<bool> SyncTrainStatusAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs synchronization of delay information
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if synchronization was successful</returns>
        Task<bool> SyncDelayInformationAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs synchronization of platform assignments
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if synchronization was successful</returns>
        Task<bool> SyncPlatformAssignmentsAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Health and Monitoring Operations

        /// <summary>
        /// Gets health status of the Railway API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>API health information</returns>
        Task<ApiHealthDto> GetApiHealthAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Tests connection to the Railway API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if connection is successful</returns>
        Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets API version information
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>API version string</returns>
        Task<string> GetApiVersionAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Batch Operations

        /// <summary>
        /// Performs batch update of multiple data types
        /// </summary>
        /// <param name="stationCodes">Station codes to update</param>
        /// <param name="includeSchedules">Include schedule updates</param>
        /// <param name="includeStatus">Include status updates</param>
        /// <param name="includeDelays">Include delay updates</param>
        /// <param name="includePlatforms">Include platform updates</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Batch update result</returns>
        Task<BatchUpdateResult> PerformBatchUpdateAsync(
            List<string> stationCodes,
            bool includeSchedules = true,
            bool includeStatus = true,
            bool includeDelays = true,
            bool includePlatforms = true,
            CancellationToken cancellationToken = default);

        #endregion

        #region Enhanced API Methods

        /// <summary>
        /// Gets live train status information
        /// </summary>
        /// <param name="trainNumber">Train number to get status for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Live train status information</returns>
        Task<TrainStatusDto?> GetLiveTrainStatusAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets live train status for a specific date
        /// </summary>
        /// <param name="trainNumber">Train number to get status for</param>
        /// <param name="date">Date to get status for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Live train status information</returns>
        Task<TrainStatusDto?> GetLiveTrainStatusAsync(string trainNumber, DateTime date, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets trains arriving at a station within a time window
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="fromTime">Start time window</param>
        /// <param name="toTime">End time window</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of arriving trains</returns>
        Task<List<TrainArrivalDto>> GetArrivingTrainsAsync(string stationCode, DateTime fromTime, DateTime toTime, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets trains departing from a station within a time window
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="fromTime">Start time window</param>
        /// <param name="toTime">End time window</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of departing trains</returns>
        Task<List<TrainDepartureDto>> GetDepartingTrainsAsync(string stationCode, DateTime fromTime, DateTime toTime, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets detailed train information including route and schedule
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Detailed train information</returns>
        Task<TrainDetailDto?> GetTrainDetailsAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches for trains between two stations
        /// </summary>
        /// <param name="fromStationCode">Source station code</param>
        /// <param name="toStationCode">Destination station code</param>
        /// <param name="date">Journey date</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of trains between stations</returns>
        Task<List<TrainBetweenStationsDto>> GetTrainsBetweenStationsAsync(string fromStationCode, string toStationCode, DateTime date, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets station information by code
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Station information</returns>
        Task<StationInfoDto?> GetStationInfoAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches for stations by name or code
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of matching stations</returns>
        Task<List<StationSearchDto>> SearchStationsAsync(string searchTerm, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets PNR status information
        /// </summary>
        /// <param name="pnrNumber">PNR number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>PNR status information</returns>
        Task<PnrStatusDto?> GetPnrStatusAsync(string pnrNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets seat availability for a train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="fromStationCode">Source station code</param>
        /// <param name="toStationCode">Destination station code</param>
        /// <param name="date">Journey date</param>
        /// <param name="classCode">Class code (e.g., SL, 3A, 2A, 1A)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Seat availability information</returns>
        Task<SeatAvailabilityDto?> GetSeatAvailabilityAsync(string trainNumber, string fromStationCode, string toStationCode, DateTime date, string classCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets fare information for a journey
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="fromStationCode">Source station code</param>
        /// <param name="toStationCode">Destination station code</param>
        /// <param name="classCode">Class code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Fare information</returns>
        Task<FareInfoDto?> GetFareInfoAsync(string trainNumber, string fromStationCode, string toStationCode, string classCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets cancelled trains list
        /// </summary>
        /// <param name="date">Date to check for cancellations</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of cancelled trains</returns>
        Task<List<CancelledTrainDto>> GetCancelledTrainsAsync(DateTime date, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets rescheduled trains list
        /// </summary>
        /// <param name="date">Date to check for reschedules</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of rescheduled trains</returns>
        Task<List<RescheduledTrainDto>> GetRescheduledTrainsAsync(DateTime date, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets diverted trains list
        /// </summary>
        /// <param name="date">Date to check for diversions</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of diverted trains</returns>
        Task<List<DivertedTrainDto>> GetDivertedTrainsAsync(DateTime date, CancellationToken cancellationToken = default);

        #endregion

        #region Event Handling

        /// <summary>
        /// Event raised when new data is received from the API
        /// </summary>
        event EventHandler<ApiDataReceivedEventArgs>? DataReceived;

        /// <summary>
        /// Event raised when an API error occurs
        /// </summary>
        event EventHandler<ApiErrorEventArgs>? ApiError;

        /// <summary>
        /// Event raised when API connection status changes
        /// </summary>
        event EventHandler<ApiConnectionStatusEventArgs>? ConnectionStatusChanged;

        #endregion
    }

    /// <summary>
    /// Result of batch update operations
    /// </summary>
    public class BatchUpdateResult
    {
        public bool IsSuccessful { get; set; }
        public int TotalRecordsProcessed { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsAdded { get; set; }
        public int RecordsFailed { get; set; }
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public DateTime CompletedAt { get; set; }
    }

    /// <summary>
    /// Event arguments for API data received events
    /// </summary>
    public class ApiDataReceivedEventArgs : EventArgs
    {
        public string DataType { get; set; } = string.Empty;
        public int RecordCount { get; set; }
        public DateTime ReceivedAt { get; set; }
        public object Data { get; set; } = new();
    }

    /// <summary>
    /// Event arguments for API error events
    /// </summary>
    public class ApiErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public string Operation { get; set; } = string.Empty;
        public DateTime OccurredAt { get; set; }
        public int RetryAttempt { get; set; }
    }

    /// <summary>
    /// Event arguments for API connection status events
    /// </summary>
    public class ApiConnectionStatusEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime StatusChangedAt { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
