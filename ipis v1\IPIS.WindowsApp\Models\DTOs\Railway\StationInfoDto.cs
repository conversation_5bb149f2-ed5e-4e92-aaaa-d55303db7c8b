namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    public class StationInfoDto
    {
        public string StationCode { get; set; } = string.Empty;
        public string StationName { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Zone { get; set; } = string.Empty;
        public string Division { get; set; } = string.Empty;
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public int PlatformCount { get; set; }
        public List<string> Facilities { get; set; } = new();
        public bool IsJunction { get; set; }
        public bool IsTerminal { get; set; }
    }

    public class StationSearchDto
    {
        public string StationCode { get; set; } = string.Empty;
        public string StationName { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Zone { get; set; } = string.Empty;
        public double MatchScore { get; set; }
    }

    public class PnrStatusDto
    {
        public string PnrNumber { get; set; } = string.Empty;
        public string TrainNumber { get; set; } = string.Empty;
        public string TrainName { get; set; } = string.Empty;
        public DateTime JourneyDate { get; set; }
        public string FromStation { get; set; } = string.Empty;
        public string ToStation { get; set; } = string.Empty;
        public string Class { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public List<PassengerStatusDto> Passengers { get; set; } = new();
    }

    public class PassengerStatusDto
    {
        public int PassengerNumber { get; set; }
        public string BookingStatus { get; set; } = string.Empty;
        public string CurrentStatus { get; set; } = string.Empty;
        public string CoachPosition { get; set; } = string.Empty;
        public int? BerthNumber { get; set; }
    }

    public class SeatAvailabilityDto
    {
        public string TrainNumber { get; set; } = string.Empty;
        public string FromStation { get; set; } = string.Empty;
        public string ToStation { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Class { get; set; } = string.Empty;
        public string Availability { get; set; } = string.Empty;
        public int? AvailableSeats { get; set; }
        public int? WaitingList { get; set; }
    }

    public class FareInfoDto
    {
        public string TrainNumber { get; set; } = string.Empty;
        public string FromStation { get; set; } = string.Empty;
        public string ToStation { get; set; } = string.Empty;
        public string Class { get; set; } = string.Empty;
        public decimal BaseFare { get; set; }
        public decimal ReservationCharge { get; set; }
        public decimal SuperFastCharge { get; set; }
        public decimal TotalFare { get; set; }
        public string Currency { get; set; } = "INR";
    }

    public class CancelledTrainDto
    {
        public string TrainNumber { get; set; } = string.Empty;
        public string TrainName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
    }

    public class RescheduledTrainDto
    {
        public string TrainNumber { get; set; } = string.Empty;
        public string TrainName { get; set; } = string.Empty;
        public DateTime OriginalDate { get; set; }
        public DateTime RescheduledDate { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
    }

    public class DivertedTrainDto
    {
        public string TrainNumber { get; set; } = string.Empty;
        public string TrainName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string OriginalRoute { get; set; } = string.Empty;
        public string DivertedRoute { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public List<string> AffectedStations { get; set; } = new();
    }
}
