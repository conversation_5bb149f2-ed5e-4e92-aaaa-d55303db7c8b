// Decompiled with JetBrains decompiler
// Type: ipis.frmPassword
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmPassword : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblPassword")]
  private Label _lblPassword;
  [AccessedThroughProperty("btnOK")]
  private Button _btnOK;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("txtPassword")]
  private TextBox _txtPassword;

  [DebuggerNonUserCode]
  static frmPassword()
  {
  }

  [DebuggerNonUserCode]
  public frmPassword()
  {
    frmPassword.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmPassword.__ENCList)
    {
      if (frmPassword.__ENCList.Count == frmPassword.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmPassword.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmPassword.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmPassword.__ENCList[index1] = frmPassword.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmPassword.__ENCList.RemoveRange(index1, checked (frmPassword.__ENCList.Count - index1));
        frmPassword.__ENCList.Capacity = frmPassword.__ENCList.Count;
      }
      frmPassword.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblPassword = new Label();
    this.btnOK = new Button();
    this.btnExit = new Button();
    this.txtPassword = new TextBox();
    this.SuspendLayout();
    this.lblPassword.AutoSize = true;
    Label lblPassword1 = this.lblPassword;
    Point point1 = new Point(74, 33);
    Point point2 = point1;
    lblPassword1.Location = point2;
    Label lblPassword2 = this.lblPassword;
    Padding padding1 = new Padding(4, 0, 4, 0);
    Padding padding2 = padding1;
    lblPassword2.Margin = padding2;
    this.lblPassword.Name = "lblPassword";
    Label lblPassword3 = this.lblPassword;
    Size size1 = new Size(116, 16 /*0x10*/);
    Size size2 = size1;
    lblPassword3.Size = size2;
    this.lblPassword.TabIndex = 0;
    this.lblPassword.Text = "Enter Password";
    Button btnOk1 = this.btnOK;
    point1 = new Point(46, 122);
    Point point3 = point1;
    btnOk1.Location = point3;
    Button btnOk2 = this.btnOK;
    padding1 = new Padding(4);
    Padding padding3 = padding1;
    btnOk2.Margin = padding3;
    this.btnOK.Name = "btnOK";
    Button btnOk3 = this.btnOK;
    size1 = new Size(74, 28);
    Size size3 = size1;
    btnOk3.Size = size3;
    this.btnOK.TabIndex = 2;
    this.btnOK.Text = "OK";
    this.btnOK.UseVisualStyleBackColor = true;
    this.btnExit.DialogResult = DialogResult.Cancel;
    Button btnExit1 = this.btnExit;
    point1 = new Point(140, 122);
    Point point4 = point1;
    btnExit1.Location = point4;
    Button btnExit2 = this.btnExit;
    padding1 = new Padding(4);
    Padding padding4 = padding1;
    btnExit2.Margin = padding4;
    this.btnExit.Name = "btnExit";
    Button btnExit3 = this.btnExit;
    size1 = new Size(76, 28);
    Size size4 = size1;
    btnExit3.Size = size4;
    this.btnExit.TabIndex = 3;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = true;
    TextBox txtPassword1 = this.txtPassword;
    point1 = new Point(46, 66);
    Point point5 = point1;
    txtPassword1.Location = point5;
    TextBox txtPassword2 = this.txtPassword;
    padding1 = new Padding(4);
    Padding padding5 = padding1;
    txtPassword2.Margin = padding5;
    this.txtPassword.MaxLength = 20;
    this.txtPassword.Name = "txtPassword";
    this.txtPassword.PasswordChar = '*';
    TextBox txtPassword3 = this.txtPassword;
    size1 = new Size(170, 22);
    Size size5 = size1;
    txtPassword3.Size = size5;
    this.txtPassword.TabIndex = 1;
    this.txtPassword.UseSystemPasswordChar = true;
    this.AcceptButton = (IButtonControl) this.btnOK;
    this.AutoScaleDimensions = new SizeF(9f, 16f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(282, 163);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.txtPassword);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOK);
    this.Controls.Add((Control) this.lblPassword);
    this.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    padding1 = new Padding(4);
    this.Margin = padding1;
    this.Name = "frmPassword";
    this.Text = "Password";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblPassword
  {
    [DebuggerNonUserCode] get { return this._lblPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblPassword = value;
    }
  }

  internal virtual Button btnOK
  {
    [DebuggerNonUserCode] get { return this._btnOK; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOK_Click);
      if (this._btnOK != null)
        this._btnOK.Click -= eventHandler;
      this._btnOK = value;
      if (this._btnOK == null)
        return;
      this._btnOK.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual TextBox txtPassword
  {
    [DebuggerNonUserCode] get { return this._txtPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPassword = value;
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOK_Click(object sender, EventArgs e)
  {
    network_db_read.user_info();
    int index = 0;
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    while (index < (int) frmMainFormIPIS.user_cnt.cnt)
    {
      if (Operators.CompareString(Strings.Trim(frmChangeUserDetails.user_name), frmMainFormIPIS.user_details[index].user_name, false) == 0)
      {
        network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty2, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
        if (Operators.CompareString(this.txtPassword.Text, empty2, false) == 0)
        {
          MyProject.Forms.frmChangeUserDetails.btnChangeAccountType.Enabled = true;
          MyProject.Forms.frmChangeUserDetails.btnChangeName.Enabled = true;
          MyProject.Forms.frmChangeUserDetails.btnChangePwd.Enabled = true;
          MyProject.Forms.frmChangeUserDetails.btnDeleteUser.Enabled = true;
          this.Close();
          return;
        }
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Incorrect password ", "Msg Box", 0, 0, 0);
        this.txtPassword.Text = string.Empty;
        return;
      }
      checked { ++index; }
    }
    int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Incorrect Password", "Msg Box", 0, 0, 0);
    this.txtPassword.Text = string.Empty;
  }
}

}