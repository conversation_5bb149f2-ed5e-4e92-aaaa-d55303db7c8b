using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for data override information
    /// </summary>
    public class DataOverrideInfoDto
    {
        /// <summary>
        /// Unique identifier for the override
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Type of override (Train, Station, Schedule)
        /// </summary>
        [Required]
        public string OverrideType { get; set; } = string.Empty;

        /// <summary>
        /// Entity identifier (train number, station code, etc.)
        /// </summary>
        [Required]
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Entity name for display
        /// </summary>
        public string EntityName { get; set; } = string.Empty;

        /// <summary>
        /// Override reason/description
        /// </summary>
        [Required]
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// User who created the override
        /// </summary>
        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Override creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Override expiry timestamp
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// Whether this is a permanent override
        /// </summary>
        public bool IsPermanent { get; set; }

        /// <summary>
        /// Priority level
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; }

        /// <summary>
        /// Current status of the override
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Affected stations (comma-separated)
        /// </summary>
        public string? AffectedStations { get; set; }

        /// <summary>
        /// Number of displays affected
        /// </summary>
        public int AffectedDisplays { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// User who last updated the override
        /// </summary>
        public string? LastUpdatedBy { get; set; }

        /// <summary>
        /// Whether the override is currently active
        /// </summary>
        public bool IsActive
        {
            get
            {
                if (IsPermanent) return true;
                if (ExpiryTime.HasValue)
                    return DateTime.UtcNow < ExpiryTime.Value;
                return true;
            }
        }

        /// <summary>
        /// Time remaining until expiry
        /// </summary>
        public TimeSpan? TimeToExpiry
        {
            get
            {
                if (IsPermanent) return null;
                if (ExpiryTime.HasValue)
                {
                    var remaining = ExpiryTime.Value - DateTime.UtcNow;
                    return remaining.TotalSeconds > 0 ? remaining : TimeSpan.Zero;
                }
                return null;
            }
        }

        /// <summary>
        /// Override duration description
        /// </summary>
        public string DurationDescription
        {
            get
            {
                if (IsPermanent) return "Permanent";
                if (TimeToExpiry.HasValue)
                {
                    var remaining = TimeToExpiry.Value;
                    if (remaining.TotalDays >= 1)
                        return $"{(int)remaining.TotalDays}d {remaining.Hours}h";
                    else if (remaining.TotalHours >= 1)
                        return $"{(int)remaining.TotalHours}h {remaining.Minutes}m";
                    else
                        return $"{remaining.Minutes}m";
                }
                return "Expired";
            }
        }

        /// <summary>
        /// Override type icon for UI
        /// </summary>
        public string TypeIcon
        {
            get
            {
                return OverrideType.ToLower() switch
                {
                    "train" => "🚂",
                    "station" => "🚉",
                    "schedule" => "📅",
                    "announcement" => "📢",
                    _ => "⚙️"
                };
            }
        }

        /// <summary>
        /// Priority color for UI
        /// </summary>
        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    >= 8 => "Red",
                    >= 6 => "Orange",
                    >= 4 => "Yellow",
                    _ => "Green"
                };
            }
        }

        /// <summary>
        /// Status color for UI
        /// </summary>
        public string StatusColor
        {
            get
            {
                return Status.ToLower() switch
                {
                    "active" => "Green",
                    "expired" => "Gray",
                    "disabled" => "Red",
                    "pending" => "Orange",
                    _ => "Gray"
                };
            }
        }
    }
}
