using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for detailed train information
    /// </summary>
    public class TrainDetailDto
    {
        /// <summary>
        /// Train number
        /// </summary>
        [Required]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Train name
        /// </summary>
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Train type (Express, Passenger, Rajdhani, etc.)
        /// </summary>
        public string TrainType { get; set; } = string.Empty;

        /// <summary>
        /// Source station code
        /// </summary>
        public string SourceStationCode { get; set; } = string.Empty;

        /// <summary>
        /// Source station name
        /// </summary>
        public string SourceStationName { get; set; } = string.Empty;

        /// <summary>
        /// Destination station code
        /// </summary>
        public string DestinationStationCode { get; set; } = string.Empty;

        /// <summary>
        /// Destination station name
        /// </summary>
        public string DestinationStationName { get; set; } = string.Empty;

        /// <summary>
        /// Departure time from source
        /// </summary>
        public TimeSpan? DepartureTime { get; set; }

        /// <summary>
        /// Arrival time at destination
        /// </summary>
        public TimeSpan? ArrivalTime { get; set; }

        /// <summary>
        /// Total journey duration
        /// </summary>
        public TimeSpan? Duration { get; set; }

        /// <summary>
        /// Total distance in kilometers
        /// </summary>
        public double? Distance { get; set; }

        /// <summary>
        /// Average speed in km/h
        /// </summary>
        public double? AverageSpeed { get; set; }

        /// <summary>
        /// Available classes (SL, 3A, 2A, 1A, etc.)
        /// </summary>
        public List<string> AvailableClasses { get; set; } = new();

        /// <summary>
        /// Train route information
        /// </summary>
        public List<TrainRouteStationDto> Route { get; set; } = new();

        /// <summary>
        /// Current location of the train
        /// </summary>
        public string? CurrentLocation { get; set; }

        /// <summary>
        /// Current speed of the train
        /// </summary>
        public double? CurrentSpeed { get; set; }

        /// <summary>
        /// Overall delay in minutes
        /// </summary>
        public int OverallDelayMinutes { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Data source information
        /// </summary>
        public string DataSource { get; set; } = "API";

        /// <summary>
        /// Whether this data is from manual override
        /// </summary>
        public bool IsManualOverride { get; set; }

        /// <summary>
        /// Train frequency (daily, weekly, etc.)
        /// </summary>
        public string? Frequency { get; set; }

        /// <summary>
        /// Days of operation
        /// </summary>
        public string? OperatingDays { get; set; }

        /// <summary>
        /// Coach composition details
        /// </summary>
        public string? CoachComposition { get; set; }

        /// <summary>
        /// Pantry car availability
        /// </summary>
        public bool HasPantryCar { get; set; }

        /// <summary>
        /// WiFi availability
        /// </summary>
        public bool HasWiFi { get; set; }

        /// <summary>
        /// Current status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Whether train is cancelled
        /// </summary>
        public bool IsCancelled { get; set; }

        /// <summary>
        /// Whether train is diverted
        /// </summary>
        public bool IsDiverted { get; set; }

        /// <summary>
        /// Diversion information
        /// </summary>
        public string? DiversionInfo { get; set; }

        /// <summary>
        /// Whether train is currently running
        /// </summary>
        public bool IsCurrentlyRunning
        {
            get
            {
                var today = DateTime.Today;
                return Route.Any(r => r.ScheduledArrival?.Date == today || r.ScheduledDeparture?.Date == today);
            }
        }

        /// <summary>
        /// Next major station
        /// </summary>
        public TrainRouteStationDto? NextMajorStation
        {
            get
            {
                var now = DateTime.Now;
                return Route.Where(r => r.ScheduledArrival > now && r.IsImportantStation)
                           .OrderBy(r => r.ScheduledArrival)
                           .FirstOrDefault();
            }
        }

        /// <summary>
        /// Formatted delay text
        /// </summary>
        public string DelayText
        {
            get
            {
                if (OverallDelayMinutes == 0) return "On Time";
                if (OverallDelayMinutes > 0) return $"Late by {OverallDelayMinutes} min";
                return $"Early by {Math.Abs(OverallDelayMinutes)} min";
            }
        }

        /// <summary>
        /// Status color for UI
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (IsCancelled) return "Red";
                if (IsDiverted) return "Purple";
                return Status.ToLower() switch
                {
                    "on time" => "Green",
                    "delayed" or "running late" => "Orange",
                    "terminated" => "Red",
                    _ => "Gray"
                };
            }
        }
    }
}
