using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for train arrival information
    /// </summary>
    public class TrainArrivalDto
    {
        /// <summary>
        /// Train number
        /// </summary>
        [Required]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Train name
        /// </summary>
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Station code where train is arriving
        /// </summary>
        [Required]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Station name where train is arriving
        /// </summary>
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// Scheduled arrival time
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Expected arrival time (with delays)
        /// </summary>
        public DateTime? ExpectedArrival { get; set; }

        /// <summary>
        /// Actual arrival time
        /// </summary>
        public DateTime? ActualArrival { get; set; }

        /// <summary>
        /// Platform number
        /// </summary>
        public string? Platform { get; set; }

        /// <summary>
        /// Current status of the train
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Delay in minutes
        /// </summary>
        public int DelayMinutes { get; set; }

        /// <summary>
        /// Source station code
        /// </summary>
        public string? SourceStation { get; set; }

        /// <summary>
        /// Destination station code
        /// </summary>
        public string? DestinationStation { get; set; }

        /// <summary>
        /// Train type (Express, Passenger, etc.)
        /// </summary>
        public string? TrainType { get; set; }

        /// <summary>
        /// Distance from current location to arrival station
        /// </summary>
        public double? DistanceRemaining { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Data source (API, Manual Override, etc.)
        /// </summary>
        public string DataSource { get; set; } = "API";

        /// <summary>
        /// Additional remarks or notes
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// Whether this is a manual override
        /// </summary>
        public bool IsManualOverride { get; set; }

        /// <summary>
        /// Coach composition information
        /// </summary>
        public string? CoachComposition { get; set; }

        /// <summary>
        /// Current location of the train
        /// </summary>
        public string? CurrentLocation { get; set; }

        /// <summary>
        /// Next station code
        /// </summary>
        public string? NextStation { get; set; }

        /// <summary>
        /// Previous station code
        /// </summary>
        public string? PreviousStation { get; set; }

        /// <summary>
        /// Speed of the train (if available)
        /// </summary>
        public double? Speed { get; set; }

        /// <summary>
        /// Whether the train is running late
        /// </summary>
        public bool IsRunningLate => DelayMinutes > 0;

        /// <summary>
        /// Whether the train has arrived
        /// </summary>
        public bool HasArrived => ActualArrival.HasValue;

        /// <summary>
        /// Time remaining for arrival (in minutes)
        /// </summary>
        public int? MinutesToArrival
        {
            get
            {
                if (HasArrived) return 0;
                var arrivalTime = ExpectedArrival ?? ScheduledArrival;
                if (arrivalTime.HasValue)
                {
                    var diff = arrivalTime.Value - DateTime.Now;
                    return diff.TotalMinutes > 0 ? (int)diff.TotalMinutes : 0;
                }
                return null;
            }
        }

        /// <summary>
        /// Formatted delay string
        /// </summary>
        public string DelayText
        {
            get
            {
                if (DelayMinutes == 0) return "On Time";
                if (DelayMinutes > 0) return $"Late by {DelayMinutes} min";
                return $"Early by {Math.Abs(DelayMinutes)} min";
            }
        }

        /// <summary>
        /// Status color for UI display
        /// </summary>
        public string StatusColor
        {
            get
            {
                return Status.ToLower() switch
                {
                    "on time" or "arrived" => "Green",
                    "delayed" or "running late" => "Orange",
                    "cancelled" => "Red",
                    "diverted" => "Purple",
                    _ => "Gray"
                };
            }
        }
    }
}
