using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Service for managing manual data overrides
    /// Allows operators to manually override API data with custom information
    /// </summary>
    public interface IDataOverrideService
    {
        /// <summary>
        /// Occurs when a data override is created or updated
        /// </summary>
        event EventHandler<DataOverrideEventArgs>? OverrideCreated;

        /// <summary>
        /// Occurs when a data override is removed
        /// </summary>
        event EventHandler<DataOverrideEventArgs>? OverrideRemoved;

        /// <summary>
        /// Occurs when a data override expires
        /// </summary>
        event EventHandler<DataOverrideEventArgs>? OverrideExpired;

        /// <summary>
        /// Creates or updates a train data override
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="overrideData">Override data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> OverrideTrainDataAsync(string trainNumber, TrainDataOverrideDto overrideData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates or updates a station data override
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="overrideData">Override data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> OverrideStationDataAsync(string stationCode, StationDataOverrideDto overrideData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Removes manual override for train data
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RemoveTrainDataOverrideAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Removes manual override for station data
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> RemoveStationDataOverrideAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets active override for a train
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Active override or null</returns>
        Task<TrainDataOverrideDto?> GetTrainOverrideAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets active override for a station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Active override or null</returns>
        Task<StationDataOverrideDto?> GetStationOverrideAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets list of all active data overrides
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of active overrides</returns>
        Task<List<DataOverrideInfoDto>> GetActiveOverridesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets list of expired overrides
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of expired overrides</returns>
        Task<List<DataOverrideInfoDto>> GetExpiredOverridesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Extends the expiry time of an override
        /// </summary>
        /// <param name="overrideId">Override ID</param>
        /// <param name="newExpiryTime">New expiry time</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> ExtendOverrideAsync(int overrideId, DateTime newExpiryTime, CancellationToken cancellationToken = default);

        /// <summary>
        /// Makes an override permanent (no expiry)
        /// </summary>
        /// <param name="overrideId">Override ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        Task<bool> MakeOverridePermanentAsync(int overrideId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Clears all expired overrides
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Number of overrides cleared</returns>
        Task<int> ClearExpiredOverridesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Clears all overrides (active and expired)
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Number of overrides cleared</returns>
        Task<int> ClearAllOverridesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets override statistics
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Override statistics</returns>
        Task<DataOverrideStatsDto> GetOverrideStatsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if a train has an active override
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if override exists</returns>
        Task<bool> HasTrainOverrideAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if a station has an active override
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if override exists</returns>
        Task<bool> HasStationOverrideAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets merged data (API data with overrides applied)
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Merged train data</returns>
        Task<TrainStatusDto?> GetMergedTrainDataAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets merged station data (API data with overrides applied)
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Merged station data</returns>
        Task<List<TrainArrivalDto>> GetMergedStationArrivalsAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets merged station departure data (API data with overrides applied)
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Merged station departure data</returns>
        Task<List<TrainDepartureDto>> GetMergedStationDeparturesAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates override data before applying
        /// </summary>
        /// <param name="overrideData">Override data to validate</param>
        /// <returns>Validation result</returns>
        Task<OverrideValidationResult> ValidateOverrideAsync(TrainDataOverrideDto overrideData);

        /// <summary>
        /// Validates station override data before applying
        /// </summary>
        /// <param name="overrideData">Override data to validate</param>
        /// <returns>Validation result</returns>
        Task<OverrideValidationResult> ValidateStationOverrideAsync(StationDataOverrideDto overrideData);

        /// <summary>
        /// Starts the override monitoring service (for expiry checks)
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        Task StartMonitoringAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops the override monitoring service
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Event arguments for data override events
    /// </summary>
    public class DataOverrideEventArgs : EventArgs
    {
        public string OverrideType { get; set; } = string.Empty; // "Train" or "Station"
        public string EntityId { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty; // "Created", "Updated", "Removed", "Expired"
        public DateTime EventTime { get; set; } = DateTime.UtcNow;
        public string? Reason { get; set; }
        public object? OverrideData { get; set; }
    }

    /// <summary>
    /// Override validation result
    /// </summary>
    public class OverrideValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string? RecommendedAction { get; set; }
    }
}
