namespace IPIS.WindowsApp.Forms
{
    partial class TrainManagementForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox1 = new GroupBox();
            enableAutoRefreshButton = new Button();
            getTrainStatusButton = new Button();
            trainStatusTextBox = new TextBox();
            label2 = new Label();
            trainNumberTextBox = new TextBox();
            label1 = new Label();
            groupBox2 = new GroupBox();
            refreshStatsLabel = new Label();
            connectionStatusLabel = new Label();
            label4 = new Label();
            label3 = new Label();
            groupBox3 = new GroupBox();
            apiEventsTextBox = new TextBox();
            groupBox4 = new GroupBox();
            autoRefreshEventsTextBox = new TextBox();
            statusStrip1 = new StatusStrip();
            statusLabel = new ToolStripStatusLabel();
            groupBox1.SuspendLayout();
            groupBox2.SuspendLayout();
            groupBox3.SuspendLayout();
            groupBox4.SuspendLayout();
            statusStrip1.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(enableAutoRefreshButton);
            groupBox1.Controls.Add(getTrainStatusButton);
            groupBox1.Controls.Add(trainStatusTextBox);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(trainNumberTextBox);
            groupBox1.Controls.Add(label1);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(400, 300);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "Train Information";
            // 
            // enableAutoRefreshButton
            // 
            enableAutoRefreshButton.Location = new Point(200, 60);
            enableAutoRefreshButton.Name = "enableAutoRefreshButton";
            enableAutoRefreshButton.Size = new Size(120, 30);
            enableAutoRefreshButton.TabIndex = 5;
            enableAutoRefreshButton.Text = "Enable Auto-Refresh";
            enableAutoRefreshButton.UseVisualStyleBackColor = true;
            enableAutoRefreshButton.Click += enableAutoRefreshButton_Click;
            // 
            // getTrainStatusButton
            // 
            getTrainStatusButton.Location = new Point(320, 30);
            getTrainStatusButton.Name = "getTrainStatusButton";
            getTrainStatusButton.Size = new Size(70, 25);
            getTrainStatusButton.TabIndex = 4;
            getTrainStatusButton.Text = "Get Status";
            getTrainStatusButton.UseVisualStyleBackColor = true;
            getTrainStatusButton.Click += getTrainStatusButton_Click;
            // 
            // trainStatusTextBox
            // 
            trainStatusTextBox.Location = new Point(15, 120);
            trainStatusTextBox.Multiline = true;
            trainStatusTextBox.Name = "trainStatusTextBox";
            trainStatusTextBox.ReadOnly = true;
            trainStatusTextBox.ScrollBars = ScrollBars.Vertical;
            trainStatusTextBox.Size = new Size(375, 170);
            trainStatusTextBox.TabIndex = 3;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(15, 100);
            label2.Name = "label2";
            label2.Size = new Size(74, 15);
            label2.TabIndex = 2;
            label2.Text = "Train Status:";
            // 
            // trainNumberTextBox
            // 
            trainNumberTextBox.Location = new Point(100, 30);
            trainNumberTextBox.Name = "trainNumberTextBox";
            trainNumberTextBox.Size = new Size(200, 23);
            trainNumberTextBox.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(15, 33);
            label1.Name = "label1";
            label1.Size = new Size(79, 15);
            label1.TabIndex = 0;
            label1.Text = "Train Number:";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(refreshStatsLabel);
            groupBox2.Controls.Add(connectionStatusLabel);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(label3);
            groupBox2.Location = new Point(430, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(350, 100);
            groupBox2.TabIndex = 1;
            groupBox2.TabStop = false;
            groupBox2.Text = "System Status";
            // 
            // refreshStatsLabel
            // 
            refreshStatsLabel.AutoSize = true;
            refreshStatsLabel.Location = new Point(120, 60);
            refreshStatsLabel.Name = "refreshStatsLabel";
            refreshStatsLabel.Size = new Size(58, 15);
            refreshStatsLabel.TabIndex = 3;
            refreshStatsLabel.Text = "Loading...";
            // 
            // connectionStatusLabel
            // 
            connectionStatusLabel.AutoSize = true;
            connectionStatusLabel.Location = new Point(120, 30);
            connectionStatusLabel.Name = "connectionStatusLabel";
            connectionStatusLabel.Size = new Size(58, 15);
            connectionStatusLabel.TabIndex = 2;
            connectionStatusLabel.Text = "Loading...";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(15, 60);
            label4.Name = "label4";
            label4.Size = new Size(99, 15);
            label4.TabIndex = 1;
            label4.Text = "Auto-Refresh Stats:";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(15, 30);
            label3.Name = "label3";
            label3.Size = new Size(99, 15);
            label3.TabIndex = 0;
            label3.Text = "API Connection:";
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(apiEventsTextBox);
            groupBox3.Location = new Point(430, 130);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new Size(350, 180);
            groupBox3.TabIndex = 2;
            groupBox3.TabStop = false;
            groupBox3.Text = "Railway API Events";
            // 
            // apiEventsTextBox
            // 
            apiEventsTextBox.Dock = DockStyle.Fill;
            apiEventsTextBox.Location = new Point(3, 19);
            apiEventsTextBox.Multiline = true;
            apiEventsTextBox.Name = "apiEventsTextBox";
            apiEventsTextBox.ReadOnly = true;
            apiEventsTextBox.ScrollBars = ScrollBars.Vertical;
            apiEventsTextBox.Size = new Size(344, 158);
            apiEventsTextBox.TabIndex = 0;
            // 
            // groupBox4
            // 
            groupBox4.Controls.Add(autoRefreshEventsTextBox);
            groupBox4.Location = new Point(12, 330);
            groupBox4.Name = "groupBox4";
            groupBox4.Size = new Size(768, 180);
            groupBox4.TabIndex = 3;
            groupBox4.TabStop = false;
            groupBox4.Text = "Auto-Refresh Events";
            // 
            // autoRefreshEventsTextBox
            // 
            autoRefreshEventsTextBox.Dock = DockStyle.Fill;
            autoRefreshEventsTextBox.Location = new Point(3, 19);
            autoRefreshEventsTextBox.Multiline = true;
            autoRefreshEventsTextBox.Name = "autoRefreshEventsTextBox";
            autoRefreshEventsTextBox.ReadOnly = true;
            autoRefreshEventsTextBox.ScrollBars = ScrollBars.Vertical;
            autoRefreshEventsTextBox.Size = new Size(762, 158);
            autoRefreshEventsTextBox.TabIndex = 0;
            // 
            // statusStrip1
            // 
            statusStrip1.Items.AddRange(new ToolStripItem[] { statusLabel });
            statusStrip1.Location = new Point(0, 528);
            statusStrip1.Name = "statusStrip1";
            statusStrip1.Size = new Size(800, 22);
            statusStrip1.TabIndex = 4;
            statusStrip1.Text = "statusStrip1";
            // 
            // statusLabel
            // 
            statusLabel.Name = "statusLabel";
            statusLabel.Size = new Size(39, 17);
            statusLabel.Text = "Ready";
            // 
            // TrainManagementForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 550);
            Controls.Add(statusStrip1);
            Controls.Add(groupBox4);
            Controls.Add(groupBox3);
            Controls.Add(groupBox2);
            Controls.Add(groupBox1);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            Name = "TrainManagementForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Train Management - Railway API Integration";
            FormClosing += TrainManagementForm_FormClosing;
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox3.ResumeLayout(false);
            groupBox3.PerformLayout();
            groupBox4.ResumeLayout(false);
            groupBox4.PerformLayout();
            statusStrip1.ResumeLayout(false);
            statusStrip1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox groupBox1;
        private Button enableAutoRefreshButton;
        private Button getTrainStatusButton;
        private TextBox trainStatusTextBox;
        private Label label2;
        private TextBox trainNumberTextBox;
        private Label label1;
        private GroupBox groupBox2;
        private Label refreshStatsLabel;
        private Label connectionStatusLabel;
        private Label label4;
        private Label label3;
        private GroupBox groupBox3;
        private TextBox apiEventsTextBox;
        private GroupBox groupBox4;
        private TextBox autoRefreshEventsTextBox;
        private StatusStrip statusStrip1;
        private ToolStripStatusLabel statusLabel;
    }
}
