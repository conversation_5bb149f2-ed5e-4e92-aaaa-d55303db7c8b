# PowerShell script to fix virtual/abstract members that cannot be private

Write-Host "=== FIXING VIRTUAL MEMBERS ==="

# Get all C# files
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix private virtual/abstract members - change private to protected
    $content = $content -replace 'private\s+(virtual|abstract)\s+', 'protected $1 '
    $content = $content -replace 'private\s+(.*?)\s+(virtual|abstract)\s+', 'protected $1 $2 '
    
    # Also fix cases where virtual/abstract comes before private
    $content = $content -replace '(virtual|abstract)\s+private\s+', '$1 protected '
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed virtual/abstract member access modifiers"
    }
}

Write-Host "Virtual members fix complete!"
