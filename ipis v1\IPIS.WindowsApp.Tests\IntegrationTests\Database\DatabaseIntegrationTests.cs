using NUnit.Framework;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using IPIS.WindowsApp.Data;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;
using IPIS.WindowsApp.Tests.Helpers;

namespace IPIS.WindowsApp.Tests.IntegrationTests.Database
{
    /// <summary>
    /// Integration tests for database operations
    /// Tests the complete data access layer with real database operations
    /// </summary>
    [TestFixture]
    public class DatabaseIntegrationTests
    {
        private IPISDbContext _context = null!;
        private DbContextOptions<IPISDbContext> _options = null!;

        [SetUp]
        public void SetUp()
        {
            // Use in-memory database for testing
            _options = new DbContextOptionsBuilder<IPISDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new IPISDbContext(_options);
            _context.Database.EnsureCreated();
        }

        [TearDown]
        public void TearDown()
        {
            _context.Database.EnsureDeleted();
            _context.Dispose();
        }

        #region Station CRUD Tests

        [Test]
        public async Task Station_CreateReadUpdateDelete_WorksCorrectly()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();

            // Act - Create
            _context.Stations.Add(station);
            await _context.SaveChangesAsync();

            // Assert - Create
            var createdStation = await _context.Stations.FindAsync(station.Id);
            createdStation.Should().NotBeNull();
            createdStation!.Code.Should().Be(station.Code);
            createdStation.Name.Should().Be(station.Name);

            // Act - Update
            createdStation.Name = "Updated Station Name";
            createdStation.UpdateTimestamp();
            await _context.SaveChangesAsync();

            // Assert - Update
            var updatedStation = await _context.Stations.FindAsync(station.Id);
            updatedStation!.Name.Should().Be("Updated Station Name");
            updatedStation.UpdatedAt.Should().BeAfter(updatedStation.CreatedAt);

            // Act - Delete
            _context.Stations.Remove(updatedStation);
            await _context.SaveChangesAsync();

            // Assert - Delete
            var deletedStation = await _context.Stations.FindAsync(station.Id);
            deletedStation.Should().BeNull();
        }

        [Test]
        public async Task Station_WithPlatforms_CascadeOperationsWork()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            var platform1 = TestDataHelper.CreateValidPlatform();
            var platform2 = TestDataHelper.CreateValidPlatform();

            station.Platforms.Add(platform1);
            station.Platforms.Add(platform2);

            // Act
            _context.Stations.Add(station);
            await _context.SaveChangesAsync();

            // Assert
            var savedStation = await _context.Stations
                .Include(s => s.Platforms)
                .FirstAsync(s => s.Id == station.Id);

            savedStation.Platforms.Should().HaveCount(2);
            savedStation.PlatformCount.Should().Be(2);
        }

        #endregion

        #region Platform CRUD Tests

        [Test]
        public async Task Platform_CreateWithStation_WorksCorrectly()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            _context.Stations.Add(station);
            await _context.SaveChangesAsync();

            var platform = TestDataHelper.CreateValidPlatform(station.Id);

            // Act
            _context.Platforms.Add(platform);
            await _context.SaveChangesAsync();

            // Assert
            var savedPlatform = await _context.Platforms
                .Include(p => p.Station)
                .FirstAsync(p => p.Id == platform.Id);

            savedPlatform.Station.Should().NotBeNull();
            savedPlatform.Station.Code.Should().Be(station.Code);
            savedPlatform.FullIdentifier.Should().Be($"{station.Code}-{platform.PlatformNumber}");
        }

        [Test]
        public async Task Platform_QueryByStationId_ReturnsCorrectPlatforms()
        {
            // Arrange
            var station1 = TestDataHelper.CreateValidStation();
            var station2 = TestDataHelper.CreateValidStation();
            _context.Stations.AddRange(station1, station2);
            await _context.SaveChangesAsync();

            var platform1 = TestDataHelper.CreateValidPlatform(station1.Id);
            var platform2 = TestDataHelper.CreateValidPlatform(station1.Id);
            var platform3 = TestDataHelper.CreateValidPlatform(station2.Id);

            _context.Platforms.AddRange(platform1, platform2, platform3);
            await _context.SaveChangesAsync();

            // Act
            var station1Platforms = await _context.Platforms
                .Where(p => p.StationId == station1.Id)
                .ToListAsync();

            // Assert
            station1Platforms.Should().HaveCount(2);
            station1Platforms.All(p => p.StationId == station1.Id).Should().BeTrue();
        }

        #endregion

        #region Train CRUD Tests

        [Test]
        public async Task Train_CreateWithSchedules_WorksCorrectly()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            var train = TestDataHelper.CreateValidTrain();
            
            _context.Stations.Add(station);
            _context.Trains.Add(train);
            await _context.SaveChangesAsync();

            var schedule1 = TestDataHelper.CreateValidSchedule(train.Id, station.Id);
            var schedule2 = TestDataHelper.CreateValidSchedule(train.Id, station.Id);

            train.Schedules.Add(schedule1);
            train.Schedules.Add(schedule2);

            // Act
            await _context.SaveChangesAsync();

            // Assert
            var savedTrain = await _context.Trains
                .Include(t => t.Schedules)
                .FirstAsync(t => t.Id == train.Id);

            savedTrain.Schedules.Should().HaveCount(2);
            savedTrain.ActiveScheduleCount.Should().BeGreaterOrEqualTo(0);
        }

        [Test]
        public async Task Train_UniqueTrainNumber_EnforcesConstraint()
        {
            // Arrange
            var train1 = TestDataHelper.CreateValidTrain();
            var train2 = TestDataHelper.CreateValidTrain();
            train2.TrainNumber = train1.TrainNumber; // Same train number

            _context.Trains.Add(train1);
            await _context.SaveChangesAsync();

            // Act & Assert
            _context.Trains.Add(train2);
            
            // This should throw an exception due to unique constraint
            var act = async () => await _context.SaveChangesAsync();
            await act.Should().ThrowAsync<InvalidOperationException>();
        }

        #endregion

        #region Schedule CRUD Tests

        [Test]
        public async Task Schedule_CompleteScenario_WorksCorrectly()
        {
            // Arrange
            var (train, station, platform, schedule) = TestDataHelper.CreateCompleteScheduleScenario();

            _context.Stations.Add(station);
            _context.Platforms.Add(platform);
            _context.Trains.Add(train);
            _context.Schedules.Add(schedule);

            // Act
            await _context.SaveChangesAsync();

            // Assert
            var savedSchedule = await _context.Schedules
                .Include(s => s.Train)
                .Include(s => s.Station)
                .Include(s => s.Platform)
                .FirstAsync(s => s.Id == schedule.Id);

            savedSchedule.Train.Should().NotBeNull();
            savedSchedule.Station.Should().NotBeNull();
            savedSchedule.Platform.Should().NotBeNull();
            savedSchedule.DisplayInfo.Should().Contain(train.TrainNumber);
            savedSchedule.DisplayInfo.Should().Contain(station.Name);
        }

        [Test]
        public async Task Schedule_UpdateStatus_PersistsCorrectly()
        {
            // Arrange
            var (train, station, platform, schedule) = TestDataHelper.CreateCompleteScheduleScenario();
            schedule.Status = ScheduleStatus.Scheduled;
            schedule.DelayMinutes = 0;

            _context.Stations.Add(station);
            _context.Platforms.Add(platform);
            _context.Trains.Add(train);
            _context.Schedules.Add(schedule);
            await _context.SaveChangesAsync();

            // Act
            schedule.Status = ScheduleStatus.Delayed;
            schedule.DelayMinutes = 15;
            schedule.DelayReason = "Signal failure";
            schedule.UpdateTimestamp();
            await _context.SaveChangesAsync();

            // Assert
            var updatedSchedule = await _context.Schedules.FindAsync(schedule.Id);
            updatedSchedule!.Status.Should().Be(ScheduleStatus.Delayed);
            updatedSchedule.DelayMinutes.Should().Be(15);
            updatedSchedule.DelayReason.Should().Be("Signal failure");
            updatedSchedule.IsDelayed.Should().BeTrue();
        }

        [Test]
        public async Task Schedule_QueryTodaysSchedules_ReturnsCorrectResults()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            var train = TestDataHelper.CreateValidTrain();
            
            _context.Stations.Add(station);
            _context.Trains.Add(train);
            await _context.SaveChangesAsync();

            var todaySchedule = TestDataHelper.CreateValidSchedule(train.Id, station.Id);
            todaySchedule.ScheduledArrival = DateTime.Today.AddHours(10);

            var tomorrowSchedule = TestDataHelper.CreateValidSchedule(train.Id, station.Id);
            tomorrowSchedule.ScheduledArrival = DateTime.Today.AddDays(1).AddHours(10);

            _context.Schedules.AddRange(todaySchedule, tomorrowSchedule);
            await _context.SaveChangesAsync();

            // Act
            var todaysSchedules = await _context.Schedules
                .Where(s => s.ScheduledArrival.HasValue && 
                           s.ScheduledArrival.Value.Date == DateTime.Today)
                .ToListAsync();

            // Assert
            todaysSchedules.Should().HaveCount(1);
            todaysSchedules.First().IsToday.Should().BeTrue();
        }

        #endregion

        #region DisplayBoard CRUD Tests

        [Test]
        public async Task DisplayBoard_CreateWithStation_WorksCorrectly()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            _context.Stations.Add(station);
            await _context.SaveChangesAsync();

            var displayBoard = TestDataHelper.CreateValidDisplayBoard(station.Id);

            // Act
            _context.DisplayBoards.Add(displayBoard);
            await _context.SaveChangesAsync();

            // Assert
            var savedBoard = await _context.DisplayBoards
                .Include(db => db.Station)
                .FirstAsync(db => db.Id == displayBoard.Id);

            savedBoard.Station.Should().NotBeNull();
            savedBoard.FullIdentifier.Should().Contain(station.Code);
            savedBoard.NetworkEndpoint.Should().Contain(displayBoard.IpAddress!);
        }

        [Test]
        public async Task DisplayBoard_UpdateHeartbeat_WorksCorrectly()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            var displayBoard = TestDataHelper.CreateValidDisplayBoard(station.Id);
            displayBoard.IsOnline = false;
            displayBoard.LastHeartbeat = null;

            _context.Stations.Add(station);
            _context.DisplayBoards.Add(displayBoard);
            await _context.SaveChangesAsync();

            // Act
            displayBoard.UpdateHeartbeat();
            await _context.SaveChangesAsync();

            // Assert
            var updatedBoard = await _context.DisplayBoards.FindAsync(displayBoard.Id);
            updatedBoard!.IsOnline.Should().BeTrue();
            updatedBoard.LastHeartbeat.Should().NotBeNull();
            updatedBoard.LastHeartbeat.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        #endregion

        #region Message CRUD Tests

        [Test]
        public async Task Message_CreateWithDisplayBoard_WorksCorrectly()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            var displayBoard = TestDataHelper.CreateValidDisplayBoard(station.Id);
            
            _context.Stations.Add(station);
            _context.DisplayBoards.Add(displayBoard);
            await _context.SaveChangesAsync();

            var message = TestDataHelper.CreateValidMessage(displayBoard.Id, station.Id);

            // Act
            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            // Assert
            var savedMessage = await _context.Messages
                .Include(m => m.DisplayBoard)
                .Include(m => m.Station)
                .FirstAsync(m => m.Id == message.Id);

            savedMessage.DisplayBoard.Should().NotBeNull();
            savedMessage.Station.Should().NotBeNull();
            savedMessage.ShouldDisplay.Should().Be(message.IsActive && message.IsCurrentlyValid);
        }

        [Test]
        public async Task Message_QueryActiveMessages_ReturnsCorrectResults()
        {
            // Arrange
            var station = TestDataHelper.CreateValidStation();
            _context.Stations.Add(station);
            await _context.SaveChangesAsync();

            var activeMessage = TestDataHelper.CreateValidMessage(null, station.Id);
            activeMessage.IsActive = true;
            activeMessage.ValidFrom = DateTime.UtcNow.AddMinutes(-10);
            activeMessage.ValidTo = DateTime.UtcNow.AddMinutes(10);

            var inactiveMessage = TestDataHelper.CreateValidMessage(null, station.Id);
            inactiveMessage.IsActive = false;

            var expiredMessage = TestDataHelper.CreateValidMessage(null, station.Id);
            expiredMessage.IsActive = true;
            expiredMessage.ValidFrom = DateTime.UtcNow.AddDays(-2);
            expiredMessage.ValidTo = DateTime.UtcNow.AddDays(-1);

            _context.Messages.AddRange(activeMessage, inactiveMessage, expiredMessage);
            await _context.SaveChangesAsync();

            // Act
            var activeMessages = await _context.Messages
                .Where(m => m.IsActive && 
                           m.ValidFrom <= DateTime.UtcNow &&
                           (m.ValidTo == null || m.ValidTo >= DateTime.UtcNow))
                .ToListAsync();

            // Assert
            activeMessages.Should().HaveCount(1);
            activeMessages.First().Id.Should().Be(activeMessage.Id);
        }

        #endregion

        #region User CRUD Tests

        [Test]
        public async Task User_CreateWithAuthentication_WorksCorrectly()
        {
            // Arrange
            var user = TestDataHelper.CreateValidUser();

            // Act
            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Assert
            var savedUser = await _context.Users.FindAsync(user.Id);
            savedUser.Should().NotBeNull();
            savedUser!.Username.Should().Be(user.Username);
            savedUser.Email.Should().Be(user.Email);
            savedUser.PasswordHash.Should().NotBeNullOrEmpty();
            
            // Verify password can be validated
            BCrypt.Net.BCrypt.Verify("TestPassword123", savedUser.PasswordHash).Should().BeTrue();
        }

        [Test]
        public async Task User_RecordLogin_UpdatesCorrectly()
        {
            // Arrange
            var user = TestDataHelper.CreateValidUser();
            user.LastLoginAt = null;
            user.FailedLoginAttempts = 3;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Act
            user.RecordLogin("*************", "session123", TimeSpan.FromHours(8));
            await _context.SaveChangesAsync();

            // Assert
            var updatedUser = await _context.Users.FindAsync(user.Id);
            updatedUser!.LastLoginAt.Should().NotBeNull();
            updatedUser.LastLoginIp.Should().Be("*************");
            updatedUser.CurrentSessionId.Should().Be("session123");
            updatedUser.FailedLoginAttempts.Should().Be(0);
            updatedUser.HasValidSession.Should().BeTrue();
        }

        #endregion

        #region Complex Query Tests

        [Test]
        public async Task ComplexQuery_StationWithAllRelatedEntities_WorksCorrectly()
        {
            // Arrange
            var stations = TestDataHelper.CreateStationsWithRelatedEntities(2, 3, 2);
            
            foreach (var station in stations)
            {
                _context.Stations.Add(station);
            }
            await _context.SaveChangesAsync();

            // Act
            var stationWithEntities = await _context.Stations
                .Include(s => s.Platforms)
                .Include(s => s.DisplayBoards)
                .Include(s => s.Schedules)
                .ThenInclude(sch => sch.Train)
                .Include(s => s.Messages)
                .FirstAsync();

            // Assert
            stationWithEntities.Should().NotBeNull();
            stationWithEntities.Platforms.Should().HaveCount(3);
            stationWithEntities.DisplayBoards.Should().HaveCount(2);
            stationWithEntities.PlatformCount.Should().Be(3);
            stationWithEntities.DisplayBoardCount.Should().Be(2);
        }

        [Test]
        public async Task PerformanceTest_BulkOperations_CompletesWithinTimeLimit()
        {
            // Arrange
            const int recordCount = 1000;
            var stations = new List<Station>();
            
            for (int i = 0; i < recordCount; i++)
            {
                stations.Add(TestDataHelper.CreateValidStation());
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            _context.Stations.AddRange(stations);
            await _context.SaveChangesAsync();

            stopwatch.Stop();

            // Assert
            var savedCount = await _context.Stations.CountAsync();
            savedCount.Should().Be(recordCount);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
        }

        #endregion

        #region Transaction Tests

        [Test]
        public async Task Transaction_RollbackOnError_WorksCorrectly()
        {
            // Arrange
            var station1 = TestDataHelper.CreateValidStation();
            var station2 = TestDataHelper.CreateValidStation();
            station2.Code = station1.Code; // Duplicate code should cause error

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // Act
                _context.Stations.Add(station1);
                await _context.SaveChangesAsync();

                _context.Stations.Add(station2);
                await _context.SaveChangesAsync(); // This should fail

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
            }

            // Assert
            var stationCount = await _context.Stations.CountAsync();
            stationCount.Should().Be(0); // Both stations should be rolled back
        }

        #endregion
    }
}
