using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a train schedule entity
    /// Based on legacy system analysis - enhanced with real-time tracking capabilities
    /// </summary>
    [Table("Schedules")]
    public class Schedule
    {
        /// <summary>
        /// Primary key for the schedule
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the train
        /// </summary>
        [Required]
        [ForeignKey("Train")]
        public int TrainId { get; set; }

        /// <summary>
        /// Foreign key to the station
        /// </summary>
        [Required]
        [ForeignKey("Station")]
        public int StationId { get; set; }

        /// <summary>
        /// Foreign key to the platform (optional)
        /// </summary>
        [ForeignKey("Platform")]
        public int? PlatformId { get; set; }

        /// <summary>
        /// Scheduled arrival time
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Scheduled departure time
        /// </summary>
        public DateTime? ScheduledDeparture { get; set; }

        /// <summary>
        /// Actual arrival time (updated in real-time)
        /// </summary>
        public DateTime? ActualArrival { get; set; }

        /// <summary>
        /// Actual departure time (updated in real-time)
        /// </summary>
        public DateTime? ActualDeparture { get; set; }

        /// <summary>
        /// Current status of the schedule
        /// </summary>
        [Required]
        public ScheduleStatus Status { get; set; } = ScheduleStatus.Scheduled;

        /// <summary>
        /// Delay in minutes (calculated automatically)
        /// </summary>
        public int DelayMinutes { get; set; } = 0;

        /// <summary>
        /// Reason for delay or status change
        /// </summary>
        [StringLength(200)]
        public string? DelayReason { get; set; }

        /// <summary>
        /// Additional remarks or notes
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// Indicates if this schedule is published to display boards
        /// </summary>
        [Required]
        public bool IsPublished { get; set; } = false;

        /// <summary>
        /// Priority level for display (1=Highest, 10=Lowest)
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Sequence number for this station in the train's route
        /// </summary>
        public int? SequenceNumber { get; set; }

        /// <summary>
        /// Distance from origin station in kilometers
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal? DistanceFromOrigin { get; set; }

        /// <summary>
        /// Halt time at this station in minutes
        /// </summary>
        public int? HaltTime { get; set; }

        /// <summary>
        /// Indicates if this is the origin station for the train
        /// </summary>
        public bool IsOrigin { get; set; } = false;

        /// <summary>
        /// Indicates if this is the destination station for the train
        /// </summary>
        public bool IsDestination { get; set; } = false;

        /// <summary>
        /// Indicates if this is a technical halt (no passenger boarding)
        /// </summary>
        public bool IsTechnicalHalt { get; set; } = false;

        /// <summary>
        /// Coach position information (for coach guidance)
        /// </summary>
        [StringLength(200)]
        public string? CoachPosition { get; set; }

        /// <summary>
        /// Expected passenger load (Light, Medium, Heavy)
        /// </summary>
        [StringLength(20)]
        public string? ExpectedLoad { get; set; }

        /// <summary>
        /// Weather conditions affecting the schedule
        /// </summary>
        [StringLength(100)]
        public string? WeatherConditions { get; set; }

        /// <summary>
        /// External API reference ID for synchronization
        /// </summary>
        [StringLength(50)]
        public string? ExternalApiId { get; set; }

        /// <summary>
        /// Last synchronization timestamp with external API
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// The train associated with this schedule
        /// </summary>
        public virtual Train Train { get; set; } = null!;

        /// <summary>
        /// The station associated with this schedule
        /// </summary>
        public virtual Station Station { get; set; } = null!;

        /// <summary>
        /// The platform associated with this schedule (optional)
        /// </summary>
        public virtual Platform? Platform { get; set; }

        /// <summary>
        /// Collection of messages generated for this schedule
        /// </summary>
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        // Computed Properties

        /// <summary>
        /// Gets the expected arrival time (actual if available, otherwise scheduled)
        /// </summary>
        [NotMapped]
        public DateTime? ExpectedArrival => ActualArrival ?? ScheduledArrival;

        /// <summary>
        /// Gets the expected departure time (actual if available, otherwise scheduled)
        /// </summary>
        [NotMapped]
        public DateTime? ExpectedDeparture => ActualDeparture ?? ScheduledDeparture;

        /// <summary>
        /// Gets the arrival delay in minutes
        /// </summary>
        [NotMapped]
        public int ArrivalDelay
        {
            get
            {
                if (ActualArrival == null || ScheduledArrival == null)
                    return 0;
                return (int)(ActualArrival.Value - ScheduledArrival.Value).TotalMinutes;
            }
        }

        /// <summary>
        /// Gets the departure delay in minutes
        /// </summary>
        [NotMapped]
        public int DepartureDelay
        {
            get
            {
                if (ActualDeparture == null || ScheduledDeparture == null)
                    return 0;
                return (int)(ActualDeparture.Value - ScheduledDeparture.Value).TotalMinutes;
            }
        }

        /// <summary>
        /// Gets a formatted display string for the schedule
        /// </summary>
        [NotMapped]
        public string DisplayInfo => $"{Train?.TrainNumber} - {Station?.Name} - {Status}";

        /// <summary>
        /// Gets the platform display string
        /// </summary>
        [NotMapped]
        public string PlatformDisplay => Platform?.PlatformNumber ?? "TBA";

        /// <summary>
        /// Indicates if the train is currently delayed
        /// </summary>
        [NotMapped]
        public bool IsDelayed => DelayMinutes > 0 || Status == ScheduleStatus.Delayed;

        /// <summary>
        /// Indicates if the schedule is for today
        /// </summary>
        [NotMapped]
        public bool IsToday => ScheduledArrival?.Date == DateTime.Today || 
                              ScheduledDeparture?.Date == DateTime.Today;

        // Methods

        /// <summary>
        /// Updates the delay minutes based on current status and times
        /// </summary>
        public void CalculateDelay()
        {
            if (Status == ScheduleStatus.Arrived && ActualArrival != null && ScheduledArrival != null)
            {
                DelayMinutes = Math.Max(0, (int)(ActualArrival.Value - ScheduledArrival.Value).TotalMinutes);
            }
            else if (Status == ScheduleStatus.Departed && ActualDeparture != null && ScheduledDeparture != null)
            {
                DelayMinutes = Math.Max(0, (int)(ActualDeparture.Value - ScheduledDeparture.Value).TotalMinutes);
            }
            else if (Status == ScheduleStatus.Delayed)
            {
                var referenceTime = ScheduledArrival ?? ScheduledDeparture;
                if (referenceTime != null)
                {
                    DelayMinutes = Math.Max(0, (int)(DateTime.Now - referenceTime.Value).TotalMinutes);
                }
            }
        }

        /// <summary>
        /// Updates the schedule status based on current time and actual times
        /// </summary>
        public void UpdateStatus()
        {
            var now = DateTime.Now;

            if (ActualDeparture != null)
            {
                Status = ScheduleStatus.Departed;
            }
            else if (ActualArrival != null)
            {
                Status = ScheduleStatus.Arrived;
            }
            else if (ScheduledDeparture != null && now > ScheduledDeparture.Value.AddMinutes(30))
            {
                Status = ScheduleStatus.Delayed;
            }
            else if (ScheduledArrival != null && now > ScheduledArrival.Value.AddMinutes(15))
            {
                Status = ScheduleStatus.Delayed;
            }
            else if (ScheduledArrival != null && now >= ScheduledArrival.Value.AddMinutes(-10))
            {
                Status = ScheduleStatus.Approaching;
            }

            CalculateDelay();
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the schedule has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return TrainId > 0 && 
                   StationId > 0 && 
                   (ScheduledArrival != null || ScheduledDeparture != null);
        }

        /// <summary>
        /// Gets a summary of the schedule for display purposes
        /// </summary>
        /// <returns>Schedule summary string</returns>
        public string GetSummary()
        {
            var arrivalTime = ExpectedArrival?.ToString("HH:mm") ?? "N/A";
            var departureTime = ExpectedDeparture?.ToString("HH:mm") ?? "N/A";
            var delayInfo = IsDelayed ? $" (Delayed {DelayMinutes}min)" : "";
            
            return $"{Train?.DisplayName} - Arr: {arrivalTime}, Dep: {departureTime}, Platform: {PlatformDisplay}{delayInfo}";
        }

        /// <summary>
        /// Gets the delay description for display
        /// </summary>
        /// <returns>Delay description string</returns>
        public string GetDelayDescription()
        {
            if (DelayMinutes <= 0) return "On Time";
            if (DelayMinutes < 60) return $"{DelayMinutes} min late";
            
            var hours = DelayMinutes / 60;
            var minutes = DelayMinutes % 60;
            return minutes > 0 ? $"{hours}h {minutes}m late" : $"{hours}h late";
        }

        /// <summary>
        /// Returns a string representation of the schedule
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayInfo;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current schedule
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Schedule other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the schedule
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
