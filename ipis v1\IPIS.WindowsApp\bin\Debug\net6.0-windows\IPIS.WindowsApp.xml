<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IPIS.WindowsApp</name>
    </assembly>
    <members>
        <member name="T:IPIS.WindowsApp.Configuration.RailwayApiConfiguration">
            <summary>
            Configuration class for Railway API integration
            Contains all settings required for external Railway API communication
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.SectionName">
            <summary>
            Configuration section name in appsettings.json
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.BaseUrl">
            <summary>
            Base URL of the Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.ApiKey">
            <summary>
            API key for authentication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.ApiVersion">
            <summary>
            API version to use
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.TimeoutSeconds">
            <summary>
            Request timeout in seconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.RetryAttempts">
            <summary>
            Number of retry attempts for failed requests
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.RetryDelaySeconds">
            <summary>
            Delay between retry attempts in seconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.SyncIntervalMinutes">
            <summary>
            Synchronization interval in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.EnableRealTimeSync">
            <summary>
            Enable real-time synchronization
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.EnableOfflineMode">
            <summary>
            Enable offline mode support
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.CacheExpirationHours">
            <summary>
            Cache expiration time in hours
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.MaxConcurrentRequests">
            <summary>
            Maximum number of concurrent API requests
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.EnableCircuitBreaker">
            <summary>
            Enable circuit breaker pattern
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.CircuitBreakerFailureThreshold">
            <summary>
            Circuit breaker failure threshold
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.CircuitBreakerTimeoutMinutes">
            <summary>
            Circuit breaker timeout in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.EnableLogging">
            <summary>
            Enable request/response logging
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.LogSensitiveData">
            <summary>
            Log sensitive data (API keys, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.EnablePerformanceMonitoring">
            <summary>
            Enable performance monitoring
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.UserAgent">
            <summary>
            User agent string for API requests
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.AdditionalHeaders">
            <summary>
            Additional HTTP headers to include in requests
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.Endpoints">
            <summary>
            API endpoints configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.Authentication">
            <summary>
            Authentication configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.RateLimit">
            <summary>
            Rate limiting configuration
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.Validate">
            <summary>
            Validates the configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.RailwayApiConfiguration.GetApiUrl(System.String)">
            <summary>
            Gets the complete API URL for an endpoint
            </summary>
            <param name="endpoint">Endpoint path</param>
            <returns>Complete URL</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.ApiEndpoints">
            <summary>
            API endpoints configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.Health">
            <summary>
            Health check endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.Schedules">
            <summary>
            Train schedules endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.TrainStatus">
            <summary>
            Train status endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.Delays">
            <summary>
            Delay information endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.PlatformAssignments">
            <summary>
            Platform assignments endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.LivePositions">
            <summary>
            Live train positions endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ApiEndpoints.Batch">
            <summary>
            Batch operations endpoint
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.ApiEndpoints.Validate">
            <summary>
            Validates the endpoints configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.AuthenticationConfig">
            <summary>
            Authentication configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.Type">
            <summary>
            Authentication type (ApiKey, Bearer, Basic)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.ApiKeyHeader">
            <summary>
            Header name for API key authentication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.BearerToken">
            <summary>
            Bearer token for Bearer authentication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.Username">
            <summary>
            Username for Basic authentication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.Password">
            <summary>
            Password for Basic authentication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.TokenRefreshEndpoint">
            <summary>
            Token refresh endpoint
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.AuthenticationConfig.TokenExpirationMinutes">
            <summary>
            Token expiration time in minutes
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.AuthenticationConfig.Validate">
            <summary>
            Validates the authentication configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.RateLimitConfig">
            <summary>
            Rate limiting configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RateLimitConfig.Enabled">
            <summary>
            Enable rate limiting
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RateLimitConfig.RequestsPerMinute">
            <summary>
            Maximum requests per minute
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RateLimitConfig.RequestsPerHour">
            <summary>
            Maximum requests per hour
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RateLimitConfig.BurstLimit">
            <summary>
            Burst limit for short-term spikes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.RateLimitConfig.DelayBetweenRequestsMs">
            <summary>
            Delay between requests in milliseconds
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.ValidationResult">
            <summary>
            Validation result for configuration
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.SyncConfiguration">
            <summary>
            Configuration class for data synchronization settings
            Controls how data is synchronized between local system and Railway API
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Configuration.SyncConfiguration.SectionName">
            <summary>
            Configuration section name in appsettings.json
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.FullSyncIntervalHours">
            <summary>
            Full synchronization interval in hours
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.IncrementalSyncIntervalMinutes">
            <summary>
            Incremental synchronization interval in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.MaxSyncRetries">
            <summary>
            Maximum number of sync retry attempts
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.EnableConflictResolution">
            <summary>
            Enable automatic conflict resolution
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.DefaultStrategy">
            <summary>
            Default conflict resolution strategy
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.SyncBatchSize">
            <summary>
            Number of records to process in each sync batch
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.EnableSyncLogging">
            <summary>
            Enable detailed synchronization logging
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.EnableParallelProcessing">
            <summary>
            Enable parallel processing during sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.MaxDegreeOfParallelism">
            <summary>
            Maximum degree of parallelism
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.SyncTimeoutMinutes">
            <summary>
            Sync timeout in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.EnableProgressNotifications">
            <summary>
            Enable sync progress notifications
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.ProgressNotificationInterval">
            <summary>
            Progress notification interval in percentage
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.DataRetention">
            <summary>
            Data retention settings
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.ConflictResolution">
            <summary>
            Conflict resolution settings
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.Performance">
            <summary>
            Performance optimization settings
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SyncConfiguration.Scheduling">
            <summary>
            Sync scheduling settings
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.SyncConfiguration.Validate">
            <summary>
            Validates the synchronization configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.DataRetentionConfig">
            <summary>
            Data retention configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.DataRetentionConfig.ScheduleRetentionDays">
            <summary>
            Retain schedule data for specified days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.DataRetentionConfig.TrainStatusRetentionDays">
            <summary>
            Retain train status data for specified days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.DataRetentionConfig.DelayInfoRetentionDays">
            <summary>
            Retain delay information for specified days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.DataRetentionConfig.SyncHistoryRetentionDays">
            <summary>
            Retain sync history for specified days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.DataRetentionConfig.EnableAutoCleanup">
            <summary>
            Enable automatic cleanup of old data
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.DataRetentionConfig.CleanupIntervalHours">
            <summary>
            Cleanup interval in hours
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.DataRetentionConfig.Validate">
            <summary>
            Validates the data retention configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.ConflictResolutionConfig">
            <summary>
            Conflict resolution configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ConflictResolutionConfig.EnableAutoResolution">
            <summary>
            Enable automatic conflict resolution
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ConflictResolutionConfig.MaxResolutionTimeMinutes">
            <summary>
            Maximum time to spend on conflict resolution in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ConflictResolutionConfig.RequireManualApprovalForCritical">
            <summary>
            Require manual approval for critical conflicts
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ConflictResolutionConfig.StrategyMappings">
            <summary>
            Strategy mappings for different conflict types
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.ConflictResolutionConfig.EnableNotifications">
            <summary>
            Enable conflict resolution notifications
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.ConflictResolutionConfig.Validate">
            <summary>
            Validates the conflict resolution configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.PerformanceConfig">
            <summary>
            Performance optimization configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.EnableConnectionPooling">
            <summary>
            Enable database connection pooling
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.MaxDatabaseConnections">
            <summary>
            Maximum database connections
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.EnableQueryCaching">
            <summary>
            Enable query result caching
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.QueryCacheExpirationMinutes">
            <summary>
            Query cache expiration in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.EnableBulkOperations">
            <summary>
            Enable bulk operations for better performance
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.BulkOperationBatchSize">
            <summary>
            Bulk operation batch size
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.EnableMemoryOptimization">
            <summary>
            Enable memory optimization
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.PerformanceConfig.MaxMemoryUsageMB">
            <summary>
            Maximum memory usage in MB
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.PerformanceConfig.Validate">
            <summary>
            Validates the performance configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Configuration.SchedulingConfig">
            <summary>
            Sync scheduling configuration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.EnableScheduledSync">
            <summary>
            Enable scheduled synchronization
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.CronSchedule">
            <summary>
            Sync schedule in cron format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.TimeZone">
            <summary>
            Time zone for scheduling
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.SyncDuringMaintenanceOnly">
            <summary>
            Enable sync during maintenance windows only
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.MaintenanceWindowStart">
            <summary>
            Maintenance window start time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.MaintenanceWindowEnd">
            <summary>
            Maintenance window end time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.AllowedSyncDays">
            <summary>
            Days of week when sync is allowed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.PauseDuringPeakHours">
            <summary>
            Enable sync pause during peak hours
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.PeakHoursStart">
            <summary>
            Peak hours start time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Configuration.SchedulingConfig.PeakHoursEnd">
            <summary>
            Peak hours end time
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Configuration.SchedulingConfig.Validate">
            <summary>
            Validates the scheduling configuration
            </summary>
            <returns>Validation result</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Data.IPISDbContext">
            <summary>
            Entity Framework DbContext for the IPIS application
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Data.IPISDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{IPIS.WindowsApp.Data.IPISDbContext})">
            <summary>
            Initializes a new instance of the IPISDbContext class
            </summary>
            <param name="options">The options for this context</param>
        </member>
        <member name="P:IPIS.WindowsApp.Data.IPISDbContext.Stations">
            <summary>
            Gets or sets the Stations DbSet
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Data.IPISDbContext.Platforms">
            <summary>
            Gets or sets the Platforms DbSet
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Data.IPISDbContext.Trains">
            <summary>
            Gets or sets the Trains DbSet
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Data.IPISDbContext.Schedules">
            <summary>
            Gets or sets the Schedules DbSet
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Data.IPISDbContext.DisplayBoards">
            <summary>
            Gets or sets the DisplayBoards DbSet
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Data.IPISDbContext.Messages">
            <summary>
            Gets or sets the Messages DbSet
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Data.IPISDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the model that was discovered by convention from the entity types
            </summary>
            <param name="modelBuilder">The builder being used to construct the model for this context</param>
        </member>
        <member name="M:IPIS.WindowsApp.Data.IPISDbContext.SaveChanges">
            <summary>
            Override SaveChanges to automatically update UpdatedAt timestamps
            </summary>
            <returns>The number of state entries written to the database</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Data.IPISDbContext.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            Override SaveChangesAsync to automatically update UpdatedAt timestamps
            </summary>
            <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
            <returns>A task that represents the asynchronous save operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Data.IPISDbContext.UpdateTimestamps">
            <summary>
            Updates the UpdatedAt timestamp for modified entities
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Forms.AddStationForm">
            <summary>
            Form for adding new railway stations to the IPIS system
            Provides comprehensive station configuration and validation
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.#ctor(IPIS.WindowsApp.Services.Interfaces.IStationService,Microsoft.Extensions.Logging.ILogger{IPIS.WindowsApp.Forms.AddStationForm})">
            <summary>
            Initializes a new instance of the AddStationForm
            </summary>
            <param name="stationService">Station service for data operations</param>
            <param name="logger">Logger for error tracking</param>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.#ctor(IPIS.WindowsApp.Services.Interfaces.IStationService,Microsoft.Extensions.Logging.ILogger{IPIS.WindowsApp.Forms.AddStationForm},IPIS.WindowsApp.Models.Station)">
            <summary>
            Initializes a new instance for editing an existing station
            </summary>
            <param name="stationService">Station service for data operations</param>
            <param name="logger">Logger for error tracking</param>
            <param name="station">Station to edit</param>
        </member>
        <member name="P:IPIS.WindowsApp.Forms.AddStationForm.Station">
            <summary>
            Gets the station that was created or edited
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.InitializeComponent">
            <summary>
            Initializes the form components and layout
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.CreateFormControls">
            <summary>
            Creates all form controls
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.LayoutControls">
            <summary>
            Layouts all controls on the form
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.AttachEventHandlers">
            <summary>
            Attaches event handlers to form controls
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.InitializeFormData">
            <summary>
            Initializes form data
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.LoadStationData">
            <summary>
            Loads existing station data for editing
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.ValidateInput">
            <summary>
            Validates form input data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.AddStationForm.CreateStationFromForm">
            <summary>
            Creates or updates station from form data
            </summary>
            <returns>Created or updated station</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Forms.MainForm">
            <summary>
            Main form for the IPIS Windows application
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.#ctor(IPIS.WindowsApp.Services.Interfaces.IStationService,Microsoft.Extensions.Logging.ILogger{IPIS.WindowsApp.Forms.MainForm})">
            <summary>
            Initializes a new instance of the MainForm class
            </summary>
            <param name="stationService">The station service</param>
            <param name="logger">The logger instance</param>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.InitializeFormAsync">
            <summary>
            Initializes the form asynchronously
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.LoadStationsAsync">
            <summary>
            Loads stations into the DataGridView
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.ConfigureDataGridView">
            <summary>
            Configures the DataGridView appearance and behavior
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.addStationButton_Click(System.Object,System.EventArgs)">
            <summary>
            Handles the Add Station button click event
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.refreshButton_Click(System.Object,System.EventArgs)">
            <summary>
            Handles the Refresh button click event
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.MainForm_FormClosing(System.Object,System.Windows.Forms.FormClosingEventArgs)">
            <summary>
            Handles the form closing event
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.stationsDataGridView_CellDoubleClick(System.Object,System.Windows.Forms.DataGridViewCellEventArgs)">
            <summary>
            Handles the DataGridView cell double-click event for editing
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.exitToolStripMenuItem_Click(System.Object,System.EventArgs)">
            <summary>
            Handles the Exit menu item click event
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Forms.MainForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:IPIS.WindowsApp.Forms.MainForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DisplayBoardType">
            <summary>
            Represents the type of display board
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DisplayBoard">
            <summary>
            Represents a display board in the IPIS system
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.Id">
            <summary>
            Gets or sets the unique identifier for the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.StationId">
            <summary>
            Gets or sets the station ID this display board belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.BoardType">
            <summary>
            Gets or sets the type of display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.BoardName">
            <summary>
            Gets or sets the name or identifier of the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.IpAddress">
            <summary>
            Gets or sets the IP address of the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.IsOnline">
            <summary>
            Gets or sets whether the display board is currently online
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.LastHeartbeat">
            <summary>
            Gets or sets the last heartbeat timestamp from the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.CreatedAt">
            <summary>
            Gets or sets the date and time when the display board was created
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.Station">
            <summary>
            Navigation property for the station this display board belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DisplayBoard.Messages">
            <summary>
            Navigation property for messages associated with this display board
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto">
            <summary>
            Data Transfer Object for Railway API health information
            Monitors API availability and performance metrics
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.IsHealthy">
            <summary>
            Indicates if the API is healthy and operational
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.Status">
            <summary>
            Current status description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.CheckTime">
            <summary>
            Timestamp when health check was performed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.ResponseTimeMs">
            <summary>
            Response time in milliseconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.Version">
            <summary>
            API version information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.AdditionalInfo">
            <summary>
            Additional health information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.Endpoint">
            <summary>
            API endpoint that was checked
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.HttpStatusCode">
            <summary>
            HTTP status code from health check
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.ErrorMessage">
            <summary>
            Error message if health check failed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.ConsecutiveSuccesses">
            <summary>
            Number of consecutive successful checks
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.ConsecutiveFailures">
            <summary>
            Number of consecutive failed checks
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.UptimePercentage">
            <summary>
            Uptime percentage over the last 24 hours
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.AverageResponseTimeMs">
            <summary>
            Average response time over the last hour
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.PeakResponseTimeMs">
            <summary>
            Peak response time in the last hour
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.RequestsPerHour">
            <summary>
            Number of requests processed in the last hour
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.ErrorRatePercentage">
            <summary>
            Error rate percentage in the last hour
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.LastSuccessfulCheck">
            <summary>
            Last successful check timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.LastFailedCheck">
            <summary>
            Last failed check timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.HealthColor">
            <summary>
            Computed property: Health status color
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.PerformanceRating">
            <summary>
            Computed property: Performance rating
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.HasIssues">
            <summary>
            Computed property: Indicates if API is experiencing issues
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.TimeSinceCheck">
            <summary>
            Computed property: Time since last check
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.IsValid">
            <summary>
            Validates the DTO data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.GetSummary">
            <summary>
            Gets a summary of the API health
            </summary>
            <returns>Health summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.GetDetailedReport">
            <summary>
            Gets detailed health information
            </summary>
            <returns>Detailed health report</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ApiHealthDto.ToString">
            <summary>
            Returns a string representation of the API health
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto">
            <summary>
            Data Transfer Object for synchronization status information
            Tracks data synchronization between local system and Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.IsActive">
            <summary>
            Indicates if synchronization is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.LastSyncTime">
            <summary>
            Last synchronization timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.NextSyncTime">
            <summary>
            Next scheduled synchronization timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.RecordsProcessed">
            <summary>
            Number of records processed in last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.RecordsUpdated">
            <summary>
            Number of records updated in last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.RecordsAdded">
            <summary>
            Number of records added in last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.ConflictsDetected">
            <summary>
            Number of conflicts detected in last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.ConflictsResolved">
            <summary>
            Number of conflicts resolved in last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.Errors">
            <summary>
            Errors encountered during last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.LastSyncDuration">
            <summary>
            Duration of last synchronization
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.CurrentStatus">
            <summary>
            Current synchronization status
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.SyncType">
            <summary>
            Synchronization type (Full, Incremental, Manual)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.ProgressPercentage">
            <summary>
            Progress percentage for active sync (0-100)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.CurrentOperation">
            <summary>
            Current operation being performed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.SuccessfulSyncsToday">
            <summary>
            Number of successful syncs in the last 24 hours
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.FailedSyncsToday">
            <summary>
            Number of failed syncs in the last 24 hours
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.AverageSyncDuration">
            <summary>
            Average sync duration over the last 10 syncs
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.WasLastSyncSuccessful">
            <summary>
            Computed property: Indicates if sync was successful
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.TimeSinceLastSync">
            <summary>
            Computed property: Time since last sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.TimeUntilNextSync">
            <summary>
            Computed property: Time until next sync
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.SuccessRateToday">
            <summary>
            Computed property: Success rate today
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.HasUnresolvedConflicts">
            <summary>
            Computed property: Indicates if there are unresolved conflicts
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.IsValid">
            <summary>
            Validates the DTO data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.GetSummary">
            <summary>
            Gets a summary of the sync status
            </summary>
            <returns>Sync status summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.SyncStatusDto.ToString">
            <summary>
            Returns a string representation of the sync status
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict">
            <summary>
            Data Transfer Object for data conflicts between local and API data
            Manages conflict detection and resolution processes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.Id">
            <summary>
            Unique identifier for the conflict
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.EntityType">
            <summary>
            Type of entity where conflict occurred
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.EntityId">
            <summary>
            Identifier of the conflicting entity
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ConflictType">
            <summary>
            Type of conflict (Value, Status, Timing, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ConflictField">
            <summary>
            Field or property where conflict occurred
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.LocalValue">
            <summary>
            Local system value
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ApiValue">
            <summary>
            Railway API value
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.DetectedAt">
            <summary>
            Timestamp when conflict was detected
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.Severity">
            <summary>
            Severity level of the conflict
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.Description">
            <summary>
            Description of the conflict
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.IsResolved">
            <summary>
            Indicates if the conflict has been resolved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ResolutionStrategy">
            <summary>
            Resolution strategy used
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ResolvedValue">
            <summary>
            Resolved value
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ResolvedAt">
            <summary>
            Timestamp when conflict was resolved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ResolvedBy">
            <summary>
            User who resolved the conflict (for manual resolution)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ResolutionNotes">
            <summary>
            Resolution notes or comments
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.RequiresManualIntervention">
            <summary>
            Indicates if manual intervention is required
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ImpactAssessment">
            <summary>
            Impact assessment of the conflict
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.RecommendedAction">
            <summary>
            Recommended action for resolution
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.Metadata">
            <summary>
            Additional metadata about the conflict
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.TimeSinceDetected">
            <summary>
            Computed property: Time since conflict was detected
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ResolutionDuration">
            <summary>
            Computed property: Resolution duration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.SeverityColor">
            <summary>
            Computed property: Severity color for UI display
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.IsValid">
            <summary>
            Validates the conflict data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.GetSummary">
            <summary>
            Gets a summary of the conflict
            </summary>
            <returns>Conflict summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DataConflict.ToString">
            <summary>
            Returns a string representation of the conflict
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult">
            <summary>
            Data Transfer Object for conflict resolution results
            Contains information about how a conflict was resolved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.IsResolved">
            <summary>
            Indicates if the conflict was successfully resolved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.StrategyUsed">
            <summary>
            Strategy used for resolution
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ResolvedValue">
            <summary>
            Final resolved value
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ResolutionReason">
            <summary>
            Reason for the resolution choice
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ResolvedAt">
            <summary>
            Timestamp when resolution was completed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ResolvedBy">
            <summary>
            User who performed the resolution (for manual resolution)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ConfidenceLevel">
            <summary>
            Confidence level in the resolution (0-100)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.Notes">
            <summary>
            Additional notes about the resolution
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ApplyToSimilarConflicts">
            <summary>
            Indicates if the resolution should be applied to similar conflicts
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ValidationErrors">
            <summary>
            Validation errors if resolution failed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.IsHighConfidence">
            <summary>
            Computed property: Indicates if resolution is highly confident
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.GetSummary">
            <summary>
            Gets a summary of the resolution result
            </summary>
            <returns>Resolution summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult.ToString">
            <summary>
            Returns a string representation of the resolution result
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution">
            <summary>
            Data Transfer Object for conflict resolution configuration
            Defines how conflicts should be resolved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.ConflictId">
            <summary>
            Conflict identifier to resolve
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.Strategy">
            <summary>
            Resolution strategy to apply
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.ManualValue">
            <summary>
            Manual value to use (for manual resolution)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.ResolvedBy">
            <summary>
            User performing the resolution
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.Notes">
            <summary>
            Notes about the resolution decision
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.RememberForSimilar">
            <summary>
            Indicates if this resolution should be remembered for similar conflicts
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution.IsValid">
            <summary>
            Validates the resolution configuration
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.ConflictSeverity">
            <summary>
            Enumeration for conflict severity levels
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy">
            <summary>
            Enumeration for conflict resolution strategies
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.ApiPriority">
            <summary>
            Use the value from the Railway API
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.LocalPriority">
            <summary>
            Use the local system value
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.MostRecent">
            <summary>
            Use the most recent value based on timestamp
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.Oldest">
            <summary>
            Use the oldest value based on timestamp
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.Manual">
            <summary>
            Require manual intervention to resolve
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.Merge">
            <summary>
            Merge values if possible
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.Ignore">
            <summary>
            Ignore the conflict and keep current value
            </summary>
        </member>
        <member name="F:IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy.BusinessRule">
            <summary>
            Use a predefined business rule
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.ConflictType">
            <summary>
            Enumeration for conflict types
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto">
            <summary>
            Data Transfer Object for delay information from Railway API
            Provides detailed delay tracking and recovery information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.TrainNumber">
            <summary>
            Train number experiencing the delay
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.StationCode">
            <summary>
            Station code where delay is reported
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.DelayMinutes">
            <summary>
            Delay amount in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.DelayReason">
            <summary>
            Reason for the delay
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.DelayReportedAt">
            <summary>
            Timestamp when delay was reported
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.DelayCategory">
            <summary>
            Category of delay (Technical, Weather, Operational, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.IsRecovered">
            <summary>
            Indicates if the delay has been recovered
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.RecoveryTime">
            <summary>
            Time when delay was recovered (if applicable)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.TrainName">
            <summary>
            Train name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.StationName">
            <summary>
            Station name where delay occurred
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.SeverityLevel">
            <summary>
            Severity level of the delay
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.ExpectedRecoveryTime">
            <summary>
            Expected recovery time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.ImpactDescription">
            <summary>
            Impact on subsequent stations
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.ResponsibleDepartment">
            <summary>
            Responsible department/authority
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.CorrectiveActions">
            <summary>
            Corrective actions taken
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.PreviousDelayMinutes">
            <summary>
            Previous delay at this station (for comparison)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.DelayTrend">
            <summary>
            Delay trend (Increasing, Decreasing, Stable)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.DelayDescription">
            <summary>
            Computed property: Gets the delay description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.SeverityColor">
            <summary>
            Computed property: Gets the severity color code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.TimeSinceReported">
            <summary>
            Computed property: Time since delay was reported
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.RecoveryDuration">
            <summary>
            Computed property: Recovery duration (if recovered)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.IsSignificantDelay">
            <summary>
            Computed property: Indicates if delay is significant (>30 minutes)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.IsCriticalDelay">
            <summary>
            Computed property: Indicates if delay is critical (>2 hours)
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.IsValid">
            <summary>
            Validates the DTO data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.GetDelayChange">
            <summary>
            Gets the delay change compared to previous report
            </summary>
            <returns>Delay change in minutes (positive = increased, negative = decreased)</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.GetSummary">
            <summary>
            Gets a summary of the delay information
            </summary>
            <returns>Delay summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.GetDetailedReport">
            <summary>
            Gets detailed delay information for reporting
            </summary>
            <returns>Detailed delay report</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.ToString">
            <summary>
            Returns a string representation of the delay info
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.DelayInfoDto.Clone">
            <summary>
            Creates a copy of the DTO
            </summary>
            <returns>Cloned DTO</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.DelaySeverity">
            <summary>
            Enumeration for delay severity levels
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.DelayCategory">
            <summary>
            Enumeration for delay categories
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.DelayTrend">
            <summary>
            Enumeration for delay trends
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto">
            <summary>
            Data Transfer Object for platform assignment information from Railway API
            Manages dynamic platform allocations and changes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.TrainNumber">
            <summary>
            Train number for platform assignment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.StationCode">
            <summary>
            Station code where platform is assigned
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.PlatformNumber">
            <summary>
            Assigned platform number
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.AssignedAt">
            <summary>
            Timestamp when platform was assigned
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.IsConfirmed">
            <summary>
            Indicates if the assignment is confirmed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.AssignmentReason">
            <summary>
            Reason for platform assignment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.TrainName">
            <summary>
            Train name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.StationName">
            <summary>
            Station name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.PreviousPlatformNumber">
            <summary>
            Previous platform number (if changed)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.ScheduledArrival">
            <summary>
            Scheduled arrival time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.ScheduledDeparture">
            <summary>
            Scheduled departure time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.AssignmentType">
            <summary>
            Assignment type (Automatic, Manual, Emergency)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.AssignedBy">
            <summary>
            User who made the assignment (for manual assignments)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.Priority">
            <summary>
            Priority level of the assignment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.ExpectedOccupationMinutes">
            <summary>
            Expected duration of platform occupation in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.CapacityUtilization">
            <summary>
            Platform capacity utilization percentage
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.IsTemporary">
            <summary>
            Indicates if this is a temporary assignment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.ExpiryTime">
            <summary>
            Expiry time for temporary assignments
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.ConflictResolution">
            <summary>
            Conflict resolution information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.Notes">
            <summary>
            Additional notes about the assignment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.IsPlatformChanged">
            <summary>
            Computed property: Indicates if platform was changed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.TimeSinceAssignment">
            <summary>
            Computed property: Time since assignment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.IsRecentAssignment">
            <summary>
            Computed property: Indicates if assignment is recent (within last hour)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.IsExpired">
            <summary>
            Computed property: Indicates if assignment has expired
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.AssignmentStatus">
            <summary>
            Computed property: Gets the assignment status
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.PriorityDescription">
            <summary>
            Computed property: Gets the priority description
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.IsValid">
            <summary>
            Validates the DTO data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.GetExpectedOccupationDuration">
            <summary>
            Gets the expected occupation duration
            </summary>
            <returns>Occupation duration or calculated duration</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.GetSummary">
            <summary>
            Gets a summary of the platform assignment
            </summary>
            <returns>Assignment summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.GetDetailedInfo">
            <summary>
            Gets detailed assignment information
            </summary>
            <returns>Detailed assignment report</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.ToString">
            <summary>
            Returns a string representation of the platform assignment
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto.Clone">
            <summary>
            Creates a copy of the DTO
            </summary>
            <returns>Cloned DTO</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentType">
            <summary>
            Enumeration for platform assignment types
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto">
            <summary>
            Data Transfer Object for train schedule information from Railway API
            Maps external API data to internal system format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.TrainNumber">
            <summary>
            Train number from the Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.TrainName">
            <summary>
            Train name from the Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.StationCode">
            <summary>
            Station code where this schedule applies
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ScheduledArrival">
            <summary>
            Scheduled arrival time from API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ScheduledDeparture">
            <summary>
            Scheduled departure time from API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ExpectedArrival">
            <summary>
            Expected arrival time (with delays) from API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ExpectedDeparture">
            <summary>
            Expected departure time (with delays) from API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.PlatformNumber">
            <summary>
            Platform number assigned by Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.Status">
            <summary>
            Current status from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.DelayMinutes">
            <summary>
            Delay in minutes from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.DelayReason">
            <summary>
            Reason for delay from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.IsCancelled">
            <summary>
            Indicates if the train is cancelled
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.Source">
            <summary>
            Source station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.Destination">
            <summary>
            Destination station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.LastUpdated">
            <summary>
            Last updated timestamp from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ApiVersion">
            <summary>
            API version that provided this data
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.Route">
            <summary>
            Route information from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.TrainType">
            <summary>
            Train type from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.OperatorCode">
            <summary>
            Operator code from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.SequenceNumber">
            <summary>
            Sequence number in the route
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.DistanceFromOrigin">
            <summary>
            Distance from origin station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.HaltTime">
            <summary>
            Halt time at this station in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.IsOrigin">
            <summary>
            Indicates if this is the origin station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.IsDestination">
            <summary>
            Indicates if this is the destination station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.IsTechnicalHalt">
            <summary>
            Indicates if this is a technical halt
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ExpectedLoad">
            <summary>
            Expected passenger load
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.Metadata">
            <summary>
            Additional metadata from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.EffectiveArrival">
            <summary>
            Gets the effective arrival time, preferring expected over scheduled time
            </summary>
            <value>The expected arrival time if available, otherwise the scheduled arrival time</value>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.EffectiveDeparture">
            <summary>
            Gets the effective departure time, preferring expected over scheduled time
            </summary>
            <value>The expected departure time if available, otherwise the scheduled departure time</value>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.IsDelayed">
            <summary>
            Gets a value indicating whether the train is delayed
            </summary>
            <value>True if the train has any delay minutes, false otherwise</value>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.DelayDescription">
            <summary>
            Computed property: Gets the delay description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.RouteDescription">
            <summary>
            Computed property: Gets the route description
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.IsValid">
            <summary>
            Validates the DTO data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.GetSummary">
            <summary>
            Gets a summary of the schedule for display
            </summary>
            <returns>Schedule summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.ToString">
            <summary>
            Returns a string representation of the schedule
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto.Clone">
            <summary>
            Creates a copy of the DTO
            </summary>
            <returns>Cloned DTO</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto">
            <summary>
            Data Transfer Object for train status information from Railway API
            Provides real-time train location and status data
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.TrainNumber">
            <summary>
            Train number
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.CurrentStatus">
            <summary>
            Current status of the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.CurrentLocation">
            <summary>
            Current location of the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.LastLocationUpdate">
            <summary>
            Last location update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.OverallDelayMinutes">
            <summary>
            Overall delay in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.StationStatuses">
            <summary>
            Station-wise status information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.IsRunning">
            <summary>
            Indicates if the train is currently running
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.DataTimestamp">
            <summary>
            Data timestamp from Railway API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.TrainName">
            <summary>
            Train name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.SourceStation">
            <summary>
            Source station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.DestinationStation">
            <summary>
            Destination station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.CurrentSpeed">
            <summary>
            Current speed in km/h
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.NextStation">
            <summary>
            Next station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.ExpectedNextStationTime">
            <summary>
            Expected time to reach next station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.DistanceToNextStation">
            <summary>
            Distance to next station in kilometers
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.LastReportedStation">
            <summary>
            Last reported station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.LastReportedTime">
            <summary>
            Time when last reported at station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.GpsCoordinates">
            <summary>
            GPS coordinates if available
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.AdditionalInfo">
            <summary>
            Additional status information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.IsDelayed">
            <summary>
            Computed property: Indicates if the train is delayed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.DelayDescription">
            <summary>
            Computed property: Gets the delay description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.RouteDescription">
            <summary>
            Computed property: Gets the route description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.LocationDescription">
            <summary>
            Computed property: Gets the current location description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.TimeSinceLastUpdate">
            <summary>
            Computed property: Time since last update
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.IsValid">
            <summary>
            Validates the DTO data
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.GetStationStatus(System.String)">
            <summary>
            Gets the status for a specific station
            </summary>
            <param name="stationCode">Station code</param>
            <returns>Station status or null if not found</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.GetSummary">
            <summary>
            Gets a summary of the train status
            </summary>
            <returns>Status summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto.ToString">
            <summary>
            Returns a string representation of the train status
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto">
            <summary>
            Station-specific status information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.StationCode">
            <summary>
            Station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.StationName">
            <summary>
            Station name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.ScheduledArrival">
            <summary>
            Scheduled arrival time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.ActualArrival">
            <summary>
            Actual arrival time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.ScheduledDeparture">
            <summary>
            Scheduled departure time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.ActualDeparture">
            <summary>
            Actual departure time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.PlatformNumber">
            <summary>
            Platform number
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.Status">
            <summary>
            Status at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.DelayMinutes">
            <summary>
            Delay in minutes at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.SequenceNumber">
            <summary>
            Sequence number in the route
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.DistanceFromOrigin">
            <summary>
            Distance from origin
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.IsDelayed">
            <summary>
            Computed property: Indicates if delayed at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.HasArrived">
            <summary>
            Computed property: Indicates if the train has arrived
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.StationStatusDto.HasDeparted">
            <summary>
            Computed property: Indicates if the train has departed
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.DTOs.Railway.GpsCoordinatesDto">
            <summary>
            GPS coordinates information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.GpsCoordinatesDto.Latitude">
            <summary>
            Latitude coordinate
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.GpsCoordinatesDto.Longitude">
            <summary>
            Longitude coordinate
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.GpsCoordinatesDto.Accuracy">
            <summary>
            Accuracy in meters
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.DTOs.Railway.GpsCoordinatesDto.Timestamp">
            <summary>
            Timestamp when coordinates were recorded
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.DTOs.Railway.GpsCoordinatesDto.ToString">
            <summary>
            Returns a string representation of the coordinates
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.Advertisement">
            <summary>
            Represents an advertisement entity for commercial content management
            Based on legacy system analysis - supports advertisement scheduling and revenue tracking
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Id">
            <summary>
            Primary key for the advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.StationId">
            <summary>
            Foreign key to the station (optional, for station-specific ads)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Title">
            <summary>
            Title of the advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Description">
            <summary>
            Description of the advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Advertiser">
            <summary>
            Advertiser or client name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.AdvertiserContact">
            <summary>
            Contact information for the advertiser
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ContentType">
            <summary>
            Type of advertisement content (Audio, Video, Text, Image)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ContentFilePath">
            <summary>
            File path to the advertisement content
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.TextContent">
            <summary>
            Text content for text-based advertisements
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.DurationSeconds">
            <summary>
            Duration of the advertisement in seconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.FileSizeBytes">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Priority">
            <summary>
            Priority level (1=Highest, 10=Lowest)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Category">
            <summary>
            Category or classification of the advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.TargetAudience">
            <summary>
            Target audience description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.SupportedLanguages">
            <summary>
            Languages supported by this advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.StartDate">
            <summary>
            Start date for the advertisement campaign
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.EndDate">
            <summary>
            End date for the advertisement campaign
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.TimeSlots">
            <summary>
            Specific time slots when the ad should play (JSON format)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.PlayDays">
            <summary>
            Days of the week when the ad should play (JSON format)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.PlaybackFrequency">
            <summary>
            Frequency of playback (times per hour/day)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.PlaybackInterval">
            <summary>
            Interval between playbacks in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.MaxPlaysPerDay">
            <summary>
            Maximum number of plays per day
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.CostPerPlay">
            <summary>
            Cost per play in the local currency
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ContractValue">
            <summary>
            Total contract value
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Currency">
            <summary>
            Currency code (USD, INR, EUR, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.IsActive">
            <summary>
            Indicates if the advertisement is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.IsApproved">
            <summary>
            Indicates if the advertisement is approved for broadcast
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ApprovalStatus">
            <summary>
            Approval status (Pending, Approved, Rejected)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ApprovedBy">
            <summary>
            User who approved the advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ApprovedAt">
            <summary>
            Timestamp when the advertisement was approved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ApprovalNotes">
            <summary>
            Approval notes or comments
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.PlayCount">
            <summary>
            Number of times the advertisement has been played
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.TotalRevenue">
            <summary>
            Total revenue generated from this advertisement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.LastPlayedAt">
            <summary>
            Last time the advertisement was played
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.NextScheduledPlay">
            <summary>
            Next scheduled play time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.PerformanceRating">
            <summary>
            Performance rating (1-10 based on effectiveness)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.EngagementRate">
            <summary>
            Click-through rate or engagement metrics
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Tags">
            <summary>
            Tags for categorization and search
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Metadata">
            <summary>
            Additional metadata in JSON format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.Station">
            <summary>
            The station this advertisement is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.IsInCampaignPeriod">
            <summary>
            Indicates if the advertisement is currently within its campaign period
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ShouldPlay">
            <summary>
            Indicates if the advertisement should be played based on all criteria
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.CampaignDurationDays">
            <summary>
            Gets the campaign duration in days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.RemainingCampaignDays">
            <summary>
            Gets the remaining campaign days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.FileSizeFormatted">
            <summary>
            Gets the file size in a human-readable format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.DurationFormatted">
            <summary>
            Gets the duration in a human-readable format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.RevenuePerPlay">
            <summary>
            Gets the revenue per play
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.PriorityDescription">
            <summary>
            Gets the priority description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.ContentFileExists">
            <summary>
            Indicates if the content file exists
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Advertisement.SupportedLanguageList">
            <summary>
            Gets the supported languages as a list
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.RecordPlay">
            <summary>
            Records a play event and updates revenue
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.CalculateNextScheduledPlay">
            <summary>
            Calculates the next scheduled play time based on interval
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.Approve(System.String,System.String)">
            <summary>
            Approves the advertisement
            </summary>
            <param name="approvedBy">User who approved the advertisement</param>
            <param name="notes">Approval notes</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.Reject(System.String,System.String)">
            <summary>
            Rejects the advertisement
            </summary>
            <param name="rejectedBy">User who rejected the advertisement</param>
            <param name="reason">Rejection reason</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.ExtendCampaign(System.DateTime)">
            <summary>
            Extends the campaign end date
            </summary>
            <param name="newEndDate">New end date</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.IsValid">
            <summary>
            Validates if the advertisement has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.GetSummary">
            <summary>
            Gets a summary of the advertisement for display purposes
            </summary>
            <returns>Advertisement summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.ToString">
            <summary>
            Returns a string representation of the advertisement
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current advertisement
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Advertisement.GetHashCode">
            <summary>
            Returns a hash code for the advertisement
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.AuditLog">
            <summary>
            Represents an audit log entry for compliance and tracking
            Based on legacy system analysis - comprehensive audit trail for railway operations
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Id">
            <summary>
            Primary key for the audit log entry
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.UserId">
            <summary>
            Foreign key to the user who performed the action
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Action">
            <summary>
            Type of action performed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.EntityName">
            <summary>
            Name of the entity that was affected
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.EntityId">
            <summary>
            ID of the entity that was affected
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ActionDescription">
            <summary>
            Description of the action performed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Details">
            <summary>
            Detailed description or notes about the action
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.OldValues">
            <summary>
            Old values before the change (JSON format)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.NewValues">
            <summary>
            New values after the change (JSON format)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.IpAddress">
            <summary>
            IP address from which the action was performed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.UserAgent">
            <summary>
            User agent or application information
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.SessionId">
            <summary>
            Session ID when the action was performed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Module">
            <summary>
            Module or feature where the action occurred
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Function">
            <summary>
            Function or method that was called
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Severity">
            <summary>
            Severity level of the action (Info, Warning, Error, Critical)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Result">
            <summary>
            Result of the action (Success, Failed, Partial)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ErrorMessage">
            <summary>
            Error message if the action failed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.DurationMs">
            <summary>
            Duration of the action in milliseconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.RiskLevel">
            <summary>
            Risk level associated with the action (Low, Medium, High, Critical)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ComplianceCategory">
            <summary>
            Compliance category (Security, Safety, Operational, Financial)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ExternalReference">
            <summary>
            Reference to external system or transaction ID
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Tags">
            <summary>
            Tags for categorization and search
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Metadata">
            <summary>
            Additional metadata in JSON format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.RequiresReview">
            <summary>
            Indicates if this entry requires review
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.IsReviewed">
            <summary>
            Indicates if this entry has been reviewed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ReviewedBy">
            <summary>
            User who reviewed this entry
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ReviewedAt">
            <summary>
            Timestamp when this entry was reviewed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ReviewNotes">
            <summary>
            Review notes or comments
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.IsArchived">
            <summary>
            Indicates if this entry is archived
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ArchivedAt">
            <summary>
            Timestamp when this entry was archived
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.RetentionDays">
            <summary>
            Retention period in days (0 = indefinite)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Timestamp">
            <summary>
            Timestamp when the action occurred
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.User">
            <summary>
            The user who performed the action
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.ShouldBeDeleted">
            <summary>
            Indicates if this entry should be deleted based on retention policy
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.AgeDays">
            <summary>
            Gets the age of this audit entry in days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.RemainingRetentionDays">
            <summary>
            Gets the remaining retention days
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.DurationFormatted">
            <summary>
            Gets the duration in a human-readable format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.TagList">
            <summary>
            Gets the tags as a list
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.AuditLog.Summary">
            <summary>
            Gets a formatted summary of the audit entry
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.MarkAsReviewed(System.String,System.String)">
            <summary>
            Marks this entry as reviewed
            </summary>
            <param name="reviewedBy">User who reviewed the entry</param>
            <param name="notes">Review notes</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.Archive">
            <summary>
            Archives this entry
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.AddTag(System.String)">
            <summary>
            Adds a tag to the audit entry
            </summary>
            <param name="tag">Tag to add</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.RemoveTag(System.String)">
            <summary>
            Removes a tag from the audit entry
            </summary>
            <param name="tag">Tag to remove</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.SetChangeValues(System.Object,System.Object)">
            <summary>
            Sets the old and new values for change tracking
            </summary>
            <param name="oldValues">Old values object</param>
            <param name="newValues">New values object</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.GetOldValues``1">
            <summary>
            Gets the old values as a typed object
            </summary>
            <typeparam name="T">Type to deserialize to</typeparam>
            <returns>Deserialized old values or default</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.GetNewValues``1">
            <summary>
            Gets the new values as a typed object
            </summary>
            <typeparam name="T">Type to deserialize to</typeparam>
            <returns>Deserialized new values or default</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.IsValid">
            <summary>
            Validates if the audit log has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.GetDetailedDescription">
            <summary>
            Gets a detailed description of the audit entry
            </summary>
            <returns>Detailed audit description</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.ToString">
            <summary>
            Returns a string representation of the audit log
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current audit log
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.AuditLog.GetHashCode">
            <summary>
            Returns a hash code for the audit log
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.DisplayBoard">
            <summary>
            Represents a display board entity
            Based on legacy system analysis - supports all 5 board types (AGDB, CGDB, MLDB, PDB, PDCH)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Id">
            <summary>
            Primary key for the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.StationId">
            <summary>
            Foreign key to the station this display board belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.PlatformId">
            <summary>
            Foreign key to the platform (optional, for platform-specific boards)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.BoardType">
            <summary>
            Type of display board (AGDB, CGDB, MLDB, PDB, PDCH)
            Based on legacy system analysis
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.BoardName">
            <summary>
            Unique identifier/name for the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.BoardLocation">
            <summary>
            Physical location description of the board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.IpAddress">
            <summary>
            IP address for network communication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Port">
            <summary>
            Port number for network communication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Protocol">
            <summary>
            Communication protocol (TCP, UDP, Serial)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.IsOnline">
            <summary>
            Indicates if the display board is currently online
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.LastHeartbeat">
            <summary>
            Last heartbeat/ping timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Configuration">
            <summary>
            Board-specific configuration in JSON format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.DisplayLanguages">
            <summary>
            Supported display languages (comma-separated)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.RefreshInterval">
            <summary>
            Refresh interval in seconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.MaxMessageLength">
            <summary>
            Maximum message length for this board type
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.DisplayLines">
            <summary>
            Number of display lines (for multi-line boards)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Brightness">
            <summary>
            Display brightness level (0-100)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.SupportsScrolling">
            <summary>
            Indicates if the board supports scrolling text
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.ScrollSpeed">
            <summary>
            Scrolling speed (characters per second)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.FontSize">
            <summary>
            Font size for display
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.FontName">
            <summary>
            Font name/family
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.IsActive">
            <summary>
            Indicates if the board is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.LastMaintenance">
            <summary>
            Last maintenance date
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.NextMaintenance">
            <summary>
            Next scheduled maintenance date
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.FirmwareVersion">
            <summary>
            Firmware version of the display board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.HardwareModel">
            <summary>
            Hardware model/type
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.SerialNumber">
            <summary>
            Serial number of the device
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.InstallationDate">
            <summary>
            Installation date
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.WarrantyExpiry">
            <summary>
            Warranty expiration date
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Remarks">
            <summary>
            Additional remarks or notes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Station">
            <summary>
            The station this display board belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Platform">
            <summary>
            The platform this display board is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.Messages">
            <summary>
            Collection of messages displayed on this board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.DisplayName">
            <summary>
            Gets a formatted display name for the board
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.FullIdentifier">
            <summary>
            Gets the full board identifier including station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.ConnectionStatus">
            <summary>
            Gets the connection status description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.NetworkEndpoint">
            <summary>
            Gets the network endpoint (IP:Port)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.TimeSinceLastHeartbeat">
            <summary>
            Gets the time since last heartbeat
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.IsHealthy">
            <summary>
            Indicates if the board is considered healthy
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.DisplayBoard.SupportedLanguages">
            <summary>
            Gets the supported languages as a list
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.UpdateHeartbeat">
            <summary>
            Updates the heartbeat timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.MarkOffline">
            <summary>
            Marks the board as offline
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.IsValid">
            <summary>
            Validates if the display board has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.SupportsLanguage(System.String)">
            <summary>
            Checks if the board supports a specific language
            </summary>
            <param name="language">Language code to check</param>
            <returns>True if supported, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.GetMaxMessageLength">
            <summary>
            Gets the maximum message length based on board type
            </summary>
            <returns>Maximum message length</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.GetSummary">
            <summary>
            Gets a summary of the display board for display purposes
            </summary>
            <returns>Display board summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.ToString">
            <summary>
            Returns a string representation of the display board
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current display board
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.DisplayBoard.GetHashCode">
            <summary>
            Returns a hash code for the display board
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.Message">
            <summary>
            Represents a message entity for display boards and announcements
            Based on legacy system analysis - enhanced for multi-language support
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Id">
            <summary>
            Primary key for the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.DisplayBoardId">
            <summary>
            Foreign key to the display board (optional, for board-specific messages)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.StationId">
            <summary>
            Foreign key to the station (optional, for station-wide messages)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.PlatformId">
            <summary>
            Foreign key to the platform (optional, for platform-specific messages)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ScheduleId">
            <summary>
            Foreign key to the schedule (optional, for train-specific messages)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.MessageType">
            <summary>
            Type of message (Train, Announcement, Emergency, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Content">
            <summary>
            Plain text content of the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ContentHtml">
            <summary>
            Rich text/HTML content (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Language">
            <summary>
            Language of the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Priority">
            <summary>
            Priority level (1=Highest, 10=Lowest)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.IsActive">
            <summary>
            Indicates if the message is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.IsScrolling">
            <summary>
            Indicates if the message should scroll on display
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ScrollSpeed">
            <summary>
            Scrolling speed (characters per second)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ValidFrom">
            <summary>
            Message validity start time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ValidTo">
            <summary>
            Message validity end time (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.RepeatCount">
            <summary>
            Number of times to repeat the message (0 = infinite)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.CurrentRepeatCount">
            <summary>
            Current repeat counter
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.AudioEnabled">
            <summary>
            Indicates if audio announcement is enabled
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.AudioFilePath">
            <summary>
            Audio file path for voice announcement
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.DisplayDuration">
            <summary>
            Display duration in seconds (0 = until manually removed)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.FontSize">
            <summary>
            Font size for display (overrides board default)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.FontColor">
            <summary>
            Font color (hex format)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.BackgroundColor">
            <summary>
            Background color (hex format)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.TextAlignment">
            <summary>
            Text alignment (Left, Center, Right)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.IsPublished">
            <summary>
            Indicates if the message has been published to display boards
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.PublishedAt">
            <summary>
            Timestamp when the message was published
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.LastDisplayed">
            <summary>
            Timestamp when the message was last displayed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.DisplayCount">
            <summary>
            Number of times the message has been displayed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ApprovalStatus">
            <summary>
            Approval status for the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ApprovedBy">
            <summary>
            User who approved the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ApprovedAt">
            <summary>
            Timestamp when the message was approved
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Metadata">
            <summary>
            Additional metadata in JSON format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.DisplayBoard">
            <summary>
            The display board this message is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Station">
            <summary>
            The station this message is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Platform">
            <summary>
            The platform this message is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.Schedule">
            <summary>
            The schedule this message is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.IsCurrentlyValid">
            <summary>
            Indicates if the message is currently valid (within validity period)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.IsExpired">
            <summary>
            Indicates if the message has expired
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.RemainingValidTime">
            <summary>
            Gets the remaining validity time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ShouldDisplay">
            <summary>
            Indicates if the message should be displayed based on all criteria
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.PriorityDescription">
            <summary>
            Gets the priority description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Message.ContentPreview">
            <summary>
            Gets the content preview (first 50 characters)
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.Publish">
            <summary>
            Publishes the message to display boards
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.RecordDisplay">
            <summary>
            Records a display event
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.Approve(System.String)">
            <summary>
            Approves the message
            </summary>
            <param name="approvedBy">User who approved the message</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.Reject(System.String)">
            <summary>
            Rejects the message
            </summary>
            <param name="rejectedBy">User who rejected the message</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.Deactivate">
            <summary>
            Deactivates the message
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.ExtendValidity(System.DateTime)">
            <summary>
            Extends the validity period
            </summary>
            <param name="newValidTo">New validity end time</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.IsValid">
            <summary>
            Validates if the message has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.GetSummary">
            <summary>
            Gets a summary of the message for display purposes
            </summary>
            <returns>Message summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.ToString">
            <summary>
            Returns a string representation of the message
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current message
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Message.GetHashCode">
            <summary>
            Returns a hash code for the message
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.Platform">
            <summary>
            Represents a railway platform entity
            Based on legacy system analysis - enhanced with comprehensive features
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Id">
            <summary>
            Primary key for the platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.StationId">
            <summary>
            Foreign key to the station this platform belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.PlatformNumber">
            <summary>
            Platform number or identifier (e.g., "1", "2A", "3B")
            Based on legacy system analysis - supports alphanumeric platform numbers
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.PlatformName">
            <summary>
            Descriptive name for the platform (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.PlatformType">
            <summary>
            Type of platform (Passenger, Freight, Mixed, Maintenance)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.TrackNumber">
            <summary>
            Track number associated with this platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Length">
            <summary>
            Platform length in meters
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Width">
            <summary>
            Platform width in meters
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Height">
            <summary>
            Platform height in meters (above rail level)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.IsActive">
            <summary>
            Indicates if the platform is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.HasCover">
            <summary>
            Indicates if the platform has roof/cover
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.IsAccessible">
            <summary>
            Indicates if the platform is wheelchair accessible
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.HasLighting">
            <summary>
            Indicates if the platform has lighting
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.HasSeating">
            <summary>
            Indicates if the platform has seating arrangements
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.HasWaterFacility">
            <summary>
            Indicates if the platform has water facilities
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.HasRestroom">
            <summary>
            Indicates if the platform has restroom facilities
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.MaxCoaches">
            <summary>
            Maximum number of coaches that can be accommodated
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Capacity">
            <summary>
            Platform capacity (number of passengers)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.PlatformSide">
            <summary>
            Side of the platform (Left, Right, Both)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.SurfaceType">
            <summary>
            Surface type of the platform (Concrete, Asphalt, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.SafetyFeatures">
            <summary>
            Safety features available on the platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Remarks">
            <summary>
            Additional remarks or notes about the platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Station">
            <summary>
            The station this platform belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Schedules">
            <summary>
            Collection of schedules for trains at this platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.DisplayBoards">
            <summary>
            Collection of display boards on this platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.Messages">
            <summary>
            Collection of messages specific to this platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.DisplayName">
            <summary>
            Gets a formatted display name for the platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.FullIdentifier">
            <summary>
            Gets the full platform identifier including station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.StatusDescription">
            <summary>
            Gets the platform status description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.AccessibilityStatus">
            <summary>
            Gets the accessibility status description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.DisplayBoardCount">
            <summary>
            Gets the number of display boards on this platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Platform.ActiveScheduleCount">
            <summary>
            Gets the number of active schedules for this platform
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.IsValid">
            <summary>
            Validates if the platform has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.CanAccommodate(System.Int32)">
            <summary>
            Checks if the platform can accommodate a train with specified number of coaches
            </summary>
            <param name="coachCount">Number of coaches</param>
            <returns>True if can accommodate, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.GetSummary">
            <summary>
            Gets a summary of the platform for display purposes
            </summary>
            <returns>Platform summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.GetAvailableFacilities">
            <summary>
            Gets the facilities available on this platform
            </summary>
            <returns>List of available facilities</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.ToString">
            <summary>
            Returns a string representation of the platform
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current platform
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Platform.GetHashCode">
            <summary>
            Returns a hash code for the platform
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.Schedule">
            <summary>
            Represents a train schedule entity
            Based on legacy system analysis - enhanced with real-time tracking capabilities
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Id">
            <summary>
            Primary key for the schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.TrainId">
            <summary>
            Foreign key to the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.StationId">
            <summary>
            Foreign key to the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.PlatformId">
            <summary>
            Foreign key to the platform (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ScheduledArrival">
            <summary>
            Scheduled arrival time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ScheduledDeparture">
            <summary>
            Scheduled departure time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ActualArrival">
            <summary>
            Actual arrival time (updated in real-time)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ActualDeparture">
            <summary>
            Actual departure time (updated in real-time)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Status">
            <summary>
            Current status of the schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.DelayMinutes">
            <summary>
            Delay in minutes (calculated automatically)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.DelayReason">
            <summary>
            Reason for delay or status change
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Remarks">
            <summary>
            Additional remarks or notes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.IsPublished">
            <summary>
            Indicates if this schedule is published to display boards
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Priority">
            <summary>
            Priority level for display (1=Highest, 10=Lowest)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.SequenceNumber">
            <summary>
            Sequence number for this station in the train's route
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.DistanceFromOrigin">
            <summary>
            Distance from origin station in kilometers
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.HaltTime">
            <summary>
            Halt time at this station in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.IsOrigin">
            <summary>
            Indicates if this is the origin station for the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.IsDestination">
            <summary>
            Indicates if this is the destination station for the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.IsTechnicalHalt">
            <summary>
            Indicates if this is a technical halt (no passenger boarding)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.CoachPosition">
            <summary>
            Coach position information (for coach guidance)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ExpectedLoad">
            <summary>
            Expected passenger load (Light, Medium, Heavy)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.WeatherConditions">
            <summary>
            Weather conditions affecting the schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ExternalApiId">
            <summary>
            External API reference ID for synchronization
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.LastSyncTime">
            <summary>
            Last synchronization timestamp with external API
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Train">
            <summary>
            The train associated with this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Station">
            <summary>
            The station associated with this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Platform">
            <summary>
            The platform associated with this schedule (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.Messages">
            <summary>
            Collection of messages generated for this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ExpectedArrival">
            <summary>
            Gets the expected arrival time (actual if available, otherwise scheduled)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ExpectedDeparture">
            <summary>
            Gets the expected departure time (actual if available, otherwise scheduled)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.ArrivalDelay">
            <summary>
            Gets the arrival delay in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.DepartureDelay">
            <summary>
            Gets the departure delay in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.DisplayInfo">
            <summary>
            Gets a formatted display string for the schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.PlatformDisplay">
            <summary>
            Gets the platform display string
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.IsDelayed">
            <summary>
            Indicates if the train is currently delayed
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Schedule.IsToday">
            <summary>
            Indicates if the schedule is for today
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.CalculateDelay">
            <summary>
            Updates the delay minutes based on current status and times
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.UpdateStatus">
            <summary>
            Updates the schedule status based on current time and actual times
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.IsValid">
            <summary>
            Validates if the schedule has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.GetSummary">
            <summary>
            Gets a summary of the schedule for display purposes
            </summary>
            <returns>Schedule summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.GetDelayDescription">
            <summary>
            Gets the delay description for display
            </summary>
            <returns>Delay description string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.ToString">
            <summary>
            Returns a string representation of the schedule
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current schedule
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Schedule.GetHashCode">
            <summary>
            Returns a hash code for the schedule
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.Station">
            <summary>
            Represents a railway station entity
            Based on legacy system analysis - enhanced with comprehensive features
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Id">
            <summary>
            Primary key for the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Code">
            <summary>
            Unique station code (e.g., "NYC", "BOS", "DEL")
            Based on legacy system analysis - supports existing station codes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Name">
            <summary>
            Full name of the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Location">
            <summary>
            Location or address of the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.StationType">
            <summary>
            Type of station (Terminal, Junction, Regular, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.TimeZone">
            <summary>
            Time zone for the station (important for scheduling)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.IsActive">
            <summary>
            Indicates if the station is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.HasPassengerFacilities">
            <summary>
            Indicates if the station has passenger facilities
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.HasFreightFacilities">
            <summary>
            Indicates if the station has freight facilities
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Elevation">
            <summary>
            Station elevation in meters (for operational purposes)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Latitude">
            <summary>
            Latitude coordinate for GPS positioning
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Longitude">
            <summary>
            Longitude coordinate for GPS positioning
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.ContactPhone">
            <summary>
            Contact phone number for the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.ContactEmail">
            <summary>
            Contact email for the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.StationMaster">
            <summary>
            Station master or manager name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.OperatingHours">
            <summary>
            Operating hours for the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Remarks">
            <summary>
            Additional remarks or notes about the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Platforms">
            <summary>
            Collection of platforms associated with this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Schedules">
            <summary>
            Collection of schedules for trains at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.DisplayBoards">
            <summary>
            Collection of display boards at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Messages">
            <summary>
            Collection of messages specific to this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.VoiceFiles">
            <summary>
            Collection of voice files specific to this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.Advertisements">
            <summary>
            Collection of advertisements displayed at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.PlatformCount">
            <summary>
            Gets the total number of platforms at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.ActivePlatformCount">
            <summary>
            Gets the number of active platforms at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.DisplayBoardCount">
            <summary>
            Gets the number of display boards at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.OnlineDisplayBoardCount">
            <summary>
            Gets the number of online display boards at this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.DisplayName">
            <summary>
            Gets a formatted display name for the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Station.StatusDescription">
            <summary>
            Gets the station status description
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.IsValid">
            <summary>
            Validates if the station has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.GetSummary">
            <summary>
            Gets a summary of the station for display purposes
            </summary>
            <returns>Station summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.ToString">
            <summary>
            Returns a string representation of the station
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current station
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Station.GetHashCode">
            <summary>
            Returns a hash code for the station
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.Train">
            <summary>
            Represents a railway train entity
            Based on legacy system analysis - enhanced with comprehensive features
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Id">
            <summary>
            Primary key for the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.TrainNumber">
            <summary>
            Unique train number (e.g., "12345", "EXP123")
            Based on legacy system analysis - supports existing train numbering
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.TrainName">
            <summary>
            Name of the train (e.g., "Rajdhani Express", "Shatabdi Express")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.TrainType">
            <summary>
            Type of train (Express, Local, Passenger, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.OperatorCode">
            <summary>
            Railway operator or zone code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.MaxSpeed">
            <summary>
            Maximum speed of the train in km/h
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.CoachCount">
            <summary>
            Number of coaches in the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.SeatingCapacity">
            <summary>
            Total seating capacity of the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.SourceStation">
            <summary>
            Source station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.DestinationStation">
            <summary>
            Destination station code
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Route">
            <summary>
            Route description or via stations
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.OperatingDays">
            <summary>
            Days of operation (e.g., "Daily", "Mon,Wed,Fri")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.IsActive">
            <summary>
            Indicates if the train is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.HasPantryCar">
            <summary>
            Indicates if the train has pantry car
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.HasACCoaches">
            <summary>
            Indicates if the train has AC coaches
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.HasSleeperCoaches">
            <summary>
            Indicates if the train has sleeper coaches
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.HasGeneralCoaches">
            <summary>
            Indicates if the train has general coaches
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.HasLadiesCompartment">
            <summary>
            Indicates if the train has ladies compartment
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.HasDisabledFacilities">
            <summary>
            Indicates if the train has disabled-friendly facilities
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Composition">
            <summary>
            Train composition details (coach arrangement)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.EngineType">
            <summary>
            Engine type (Electric, Diesel, Steam)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.GaugeType">
            <summary>
            Gauge type (Broad, Meter, Narrow)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Distance">
            <summary>
            Distance covered by the train in kilometers
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.JourneyTime">
            <summary>
            Average journey time in minutes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Frequency">
            <summary>
            Frequency of the train (Daily, Weekly, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Remarks">
            <summary>
            Additional remarks or notes about the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.Schedules">
            <summary>
            Collection of schedules for this train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.VoiceFiles">
            <summary>
            Collection of voice files specific to this train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.DisplayName">
            <summary>
            Gets a formatted display name for the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.StatusDescription">
            <summary>
            Gets the train status description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.RouteDescription">
            <summary>
            Gets the route description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.ActiveScheduleCount">
            <summary>
            Gets the number of active schedules for this train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.Train.AvailableFacilities">
            <summary>
            Gets the facilities available on this train
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.IsValid">
            <summary>
            Validates if the train has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.OperatesOnDay(System.DayOfWeek)">
            <summary>
            Checks if the train operates on a specific day
            </summary>
            <param name="dayOfWeek">Day of the week to check</param>
            <returns>True if operates on the day, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.GetSummary">
            <summary>
            Gets a summary of the train for display purposes
            </summary>
            <returns>Train summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.GetFormattedJourneyTime">
            <summary>
            Gets the estimated journey time as a formatted string
            </summary>
            <returns>Formatted journey time</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.ToString">
            <summary>
            Returns a string representation of the train
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current train
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.Train.GetHashCode">
            <summary>
            Returns a hash code for the train
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.User">
            <summary>
            Represents a user entity for authentication and authorization
            Based on legacy system analysis - enhanced with comprehensive security features
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Id">
            <summary>
            Primary key for the user
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Username">
            <summary>
            Unique username for login
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Email">
            <summary>
            Email address (unique)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PasswordHash">
            <summary>
            Hashed password (using BCrypt)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PasswordSalt">
            <summary>
            Password salt for additional security
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.FirstName">
            <summary>
            First name of the user
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.LastName">
            <summary>
            Last name of the user
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Role">
            <summary>
            User role in the system
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.StationAccess">
            <summary>
            Station access permissions (JSON array of station IDs)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PhoneNumber">
            <summary>
            Phone number
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.EmployeeId">
            <summary>
            Employee ID or badge number
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Department">
            <summary>
            Department or division
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.JobTitle">
            <summary>
            Job title or position
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.IsActive">
            <summary>
            Indicates if the user account is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.IsLocked">
            <summary>
            Indicates if the user account is locked
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.IsEmailVerified">
            <summary>
            Indicates if email is verified
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.IsTwoFactorEnabled">
            <summary>
            Indicates if two-factor authentication is enabled
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.LastLoginAt">
            <summary>
            Last login timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.LastLoginIp">
            <summary>
            Last login IP address
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.CurrentSessionId">
            <summary>
            Current login session ID
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.SessionExpiresAt">
            <summary>
            Session expiration time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.FailedLoginAttempts">
            <summary>
            Number of failed login attempts
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.AccountLockedUntil">
            <summary>
            Account locked until this timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PasswordChangedAt">
            <summary>
            Password last changed timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.MustChangePassword">
            <summary>
            Indicates if password must be changed on next login
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PasswordResetToken">
            <summary>
            Password reset token
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PasswordResetTokenExpiry">
            <summary>
            Password reset token expiration
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.EmailVerificationToken">
            <summary>
            Email verification token
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.TwoFactorSecret">
            <summary>
            Two-factor authentication secret key
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.TwoFactorBackupCodes">
            <summary>
            Backup codes for two-factor authentication
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Preferences">
            <summary>
            User preferences in JSON format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.ProfilePicture">
            <summary>
            Profile picture file path
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.PreferredLanguage">
            <summary>
            User's preferred language
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.TimeZone">
            <summary>
            User's timezone
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.Notes">
            <summary>
            Additional notes about the user
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.AuditLogs">
            <summary>
            Collection of audit logs for this user
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.FullName">
            <summary>
            Gets the full name of the user
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.DisplayName">
            <summary>
            Gets the display name (full name with username)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.IsCurrentlyLocked">
            <summary>
            Indicates if the account is currently locked
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.HasValidSession">
            <summary>
            Indicates if the current session is valid
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.TimeSinceLastLogin">
            <summary>
            Gets the time since last login
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.NeedsPasswordChange">
            <summary>
            Indicates if password needs to be changed (expired or forced)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.User.AccessibleStationIds">
            <summary>
            Gets the accessible station IDs as a list
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.RecordLogin(System.String,System.String,System.TimeSpan)">
            <summary>
            Records a successful login
            </summary>
            <param name="ipAddress">IP address of the login</param>
            <param name="sessionId">Session ID</param>
            <param name="sessionDuration">Session duration</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.RecordFailedLogin(System.Int32,System.Nullable{System.TimeSpan})">
            <summary>
            Records a failed login attempt
            </summary>
            <param name="maxAttempts">Maximum allowed attempts before locking</param>
            <param name="lockoutDuration">Duration to lock the account</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.Logout">
            <summary>
            Logs out the user by clearing session information
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.Unlock">
            <summary>
            Unlocks the user account
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.ChangePassword(System.String)">
            <summary>
            Changes the user's password
            </summary>
            <param name="newPasswordHash">New password hash</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.SetPasswordResetToken(System.String,System.DateTime)">
            <summary>
            Sets a password reset token
            </summary>
            <param name="token">Reset token</param>
            <param name="expiry">Token expiry time</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.VerifyEmail">
            <summary>
            Verifies the email address
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.HasStationAccess(System.Int32)">
            <summary>
            Checks if the user has access to a specific station
            </summary>
            <param name="stationId">Station ID to check</param>
            <returns>True if has access, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.AddStationAccess(System.Int32)">
            <summary>
            Adds access to a station
            </summary>
            <param name="stationId">Station ID to add</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.RemoveStationAccess(System.Int32)">
            <summary>
            Removes access to a station
            </summary>
            <param name="stationId">Station ID to remove</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.IsValid">
            <summary>
            Validates if the user has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.GetSummary">
            <summary>
            Gets a summary of the user for display purposes
            </summary>
            <returns>User summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.ToString">
            <summary>
            Returns a string representation of the user
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current user
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.User.GetHashCode">
            <summary>
            Returns a hash code for the user
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Entities.VoiceFile">
            <summary>
            Represents a voice file entity for announcements
            Based on legacy system analysis - supports 1000+ voice segments
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Id">
            <summary>
            Primary key for the voice file
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.StationId">
            <summary>
            Foreign key to the station (optional, for station-specific voices)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.TrainId">
            <summary>
            Foreign key to the train (optional, for train-specific voices)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.AnnouncementType">
            <summary>
            Type of announcement this voice file is for
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Language">
            <summary>
            Language of the voice file
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FileName">
            <summary>
            File name of the voice file
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FilePath">
            <summary>
            Full file path to the voice file
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.TextContent">
            <summary>
            Text content that this voice file represents
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.PhoneticContent">
            <summary>
            Phonetic representation for pronunciation guidance
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.DurationMs">
            <summary>
            Duration of the voice file in milliseconds
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FileSizeBytes">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.AudioFormat">
            <summary>
            Audio format (WAV, MP3, etc.)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.SampleRate">
            <summary>
            Sample rate in Hz
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.BitDepth">
            <summary>
            Bit depth (8, 16, 24, 32)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Channels">
            <summary>
            Number of audio channels (1=Mono, 2=Stereo)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Quality">
            <summary>
            Quality rating (1-10, 10 being highest quality)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VoiceArtist">
            <summary>
            Voice artist or speaker name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VoiceGender">
            <summary>
            Gender of the voice (Male, Female, Other)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VoiceAge">
            <summary>
            Age category of the voice (Young, Adult, Senior)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VoiceStyle">
            <summary>
            Voice style or tone (Formal, Friendly, Urgent)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.IsPrimary">
            <summary>
            Indicates if this is the primary voice file for the content
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.IsActive">
            <summary>
            Indicates if the voice file is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.IsVerified">
            <summary>
            Indicates if the file has been verified for quality
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VerificationNotes">
            <summary>
            Verification notes or comments
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VerifiedAt">
            <summary>
            Date when the file was verified
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.VerifiedBy">
            <summary>
            User who verified the file
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.PlayCount">
            <summary>
            Number of times this voice file has been played
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.LastPlayedAt">
            <summary>
            Last time this voice file was played
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FileHash">
            <summary>
            MD5 hash of the file for integrity checking
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Version">
            <summary>
            Version number of the voice file
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Tags">
            <summary>
            Tags for categorization and search
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Metadata">
            <summary>
            Additional metadata in JSON format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.CreatedAt">
            <summary>
            Record creation timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.UpdatedAt">
            <summary>
            Record last update timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.CreatedBy">
            <summary>
            User who created the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.UpdatedBy">
            <summary>
            User who last updated the record
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Station">
            <summary>
            The station this voice file is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.Train">
            <summary>
            The train this voice file is associated with (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FileExtension">
            <summary>
            Gets the file extension from the file name
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FileSizeFormatted">
            <summary>
            Gets the file size in a human-readable format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.DurationFormatted">
            <summary>
            Gets the duration in a human-readable format
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.AudioSpecs">
            <summary>
            Gets the audio specifications as a formatted string
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.FileExists">
            <summary>
            Indicates if the file exists on disk
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.QualityDescription">
            <summary>
            Gets the quality description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Entities.VoiceFile.TagList">
            <summary>
            Gets the tags as a list
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.RecordPlay">
            <summary>
            Records a play event
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.Verify(System.String,System.String)">
            <summary>
            Verifies the voice file
            </summary>
            <param name="verifiedBy">User who verified the file</param>
            <param name="notes">Verification notes</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.CalculateFileHash">
            <summary>
            Calculates and sets the file hash
            </summary>
            <returns>The calculated MD5 hash</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.ValidateFileIntegrity">
            <summary>
            Validates the file integrity using stored hash
            </summary>
            <returns>True if file is valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.AddTag(System.String)">
            <summary>
            Adds a tag to the voice file
            </summary>
            <param name="tag">Tag to add</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.RemoveTag(System.String)">
            <summary>
            Removes a tag from the voice file
            </summary>
            <param name="tag">Tag to remove</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.UpdateTimestamp">
            <summary>
            Updates the UpdatedAt timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.UpdateTimestamp(System.String)">
            <summary>
            Updates the UpdatedAt timestamp and UpdatedBy user
            </summary>
            <param name="updatedBy">User who is updating the record</param>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.IsValid">
            <summary>
            Validates if the voice file has the minimum required information
            </summary>
            <returns>True if valid, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.GetSummary">
            <summary>
            Gets a summary of the voice file for display purposes
            </summary>
            <returns>Voice file summary string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.ToString">
            <summary>
            Returns a string representation of the voice file
            </summary>
            <returns>String representation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current voice file
            </summary>
            <param name="obj">The object to compare</param>
            <returns>True if equal, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Models.Entities.VoiceFile.GetHashCode">
            <summary>
            Returns a hash code for the voice file
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.ScheduleStatus">
            <summary>
            Represents the status of a train schedule
            Based on legacy system analysis - supports all existing status types
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.DisplayBoardType">
            <summary>
            Represents different types of display boards
            Based on legacy system analysis - supports all 5 board types
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.MessageType">
            <summary>
            Represents different types of messages
            Based on legacy system analysis - supports all message categories
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.UserRole">
            <summary>
            Represents user roles in the system
            Based on legacy system analysis - supports all access levels
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.TrainType">
            <summary>
            Represents different types of trains
            Based on legacy system analysis - supports all train categories
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.StationType">
            <summary>
            Represents different types of stations
            Based on legacy system analysis
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.PlatformType">
            <summary>
            Represents different types of platforms
            Based on legacy system analysis
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.CommunicationProtocol">
            <summary>
            Represents communication protocols
            Based on legacy system analysis
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.AuditAction">
            <summary>
            Represents audit action types
            For compliance and tracking
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.AnnouncementType">
            <summary>
            Represents voice announcement types
            Based on legacy system analysis
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Enums.Language">
            <summary>
            Represents supported languages
            Based on legacy system analysis
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.MessageType">
            <summary>
            Represents the type of message
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Message">
            <summary>
            Represents a message displayed on display boards
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.Id">
            <summary>
            Gets or sets the unique identifier for the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.DisplayBoardId">
            <summary>
            Gets or sets the display board ID this message belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.MessageType">
            <summary>
            Gets or sets the type of message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.Content">
            <summary>
            Gets or sets the content of the message
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.Language">
            <summary>
            Gets or sets the language of the message (e.g., "en", "es", "fr")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.Priority">
            <summary>
            Gets or sets the priority of the message (1 = highest, 10 = lowest)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.IsActive">
            <summary>
            Gets or sets whether the message is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.ValidFrom">
            <summary>
            Gets or sets when the message becomes valid
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.ValidTo">
            <summary>
            Gets or sets when the message expires (null for no expiration)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.CreatedAt">
            <summary>
            Gets or sets the date and time when the message was created
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Message.DisplayBoard">
            <summary>
            Navigation property for the display board this message belongs to
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Platform">
            <summary>
            Represents a platform within a railway station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.Id">
            <summary>
            Gets or sets the unique identifier for the platform
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.StationId">
            <summary>
            Gets or sets the station ID this platform belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.PlatformNumber">
            <summary>
            Gets or sets the platform number (e.g., "1", "2A", "3B")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.PlatformName">
            <summary>
            Gets or sets the platform name or description
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.IsActive">
            <summary>
            Gets or sets whether the platform is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.CreatedAt">
            <summary>
            Gets or sets the date and time when the platform was created
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.Station">
            <summary>
            Navigation property for the station this platform belongs to
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Platform.Schedules">
            <summary>
            Navigation property for schedules associated with this platform
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Schedule">
            <summary>
            Represents a train schedule entry in the IPIS system
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.Id">
            <summary>
            Gets or sets the unique identifier for the schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.TrainId">
            <summary>
            Gets or sets the train ID for this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.StationId">
            <summary>
            Gets or sets the station ID for this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.PlatformId">
            <summary>
            Gets or sets the platform ID for this schedule (optional)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.ScheduledArrival">
            <summary>
            Gets or sets the scheduled arrival time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.ScheduledDeparture">
            <summary>
            Gets or sets the scheduled departure time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.ActualArrival">
            <summary>
            Gets or sets the actual arrival time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.ActualDeparture">
            <summary>
            Gets or sets the actual departure time
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.Status">
            <summary>
            Gets or sets the current status of the schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.DelayMinutes">
            <summary>
            Gets or sets the delay in minutes (positive for delays, negative for early)
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.Remarks">
            <summary>
            Gets or sets additional remarks or notes
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.CreatedAt">
            <summary>
            Gets or sets the date and time when the schedule was created
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.UpdatedAt">
            <summary>
            Gets or sets the date and time when the schedule was last updated
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.Train">
            <summary>
            Navigation property for the train associated with this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.Station">
            <summary>
            Navigation property for the station associated with this schedule
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Schedule.Platform">
            <summary>
            Navigation property for the platform associated with this schedule
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Station">
            <summary>
            Represents a railway station in the IPIS system
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Id">
            <summary>
            Gets or sets the unique identifier for the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Code">
            <summary>
            Gets or sets the station code (e.g., "NYC", "BOS")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Name">
            <summary>
            Gets or sets the full name of the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Location">
            <summary>
            Gets or sets the location description of the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Type">
            <summary>
            Gets or sets the type of the station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.IsActive">
            <summary>
            Gets or sets whether the station is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.CreatedAt">
            <summary>
            Gets or sets the date and time when the station was created
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.UpdatedAt">
            <summary>
            Gets or sets the date and time when the station was last updated
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Platforms">
            <summary>
            Navigation property for platforms associated with this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.Schedules">
            <summary>
            Navigation property for schedules associated with this station
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Station.DisplayBoards">
            <summary>
            Navigation property for display boards associated with this station
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Models.Train">
            <summary>
            Represents a train in the IPIS system
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.Id">
            <summary>
            Gets or sets the unique identifier for the train
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.TrainNumber">
            <summary>
            Gets or sets the train number (e.g., "12345", "EXP001")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.TrainName">
            <summary>
            Gets or sets the train name (e.g., "Express", "Local")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.TrainType">
            <summary>
            Gets or sets the type of train (e.g., "Express", "Local", "Freight")
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.IsActive">
            <summary>
            Gets or sets whether the train is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.CreatedAt">
            <summary>
            Gets or sets the date and time when the train was created
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Models.Train.Schedules">
            <summary>
            Navigation property for schedules associated with this train
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Program">
            <summary>
            Main program entry point for IPIS Windows Forms Application
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Program.Main">
            <summary>
            The main entry point for the application.
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Program.BuildConfiguration">
            <summary>
            Builds the application configuration
            </summary>
            <returns>Configuration root</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Program.BuildServiceProvider(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Builds the service provider with dependency injection
            </summary>
            <param name="configuration">Application configuration</param>
            <returns>Service provider</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Program.InitializeDatabase(Microsoft.Extensions.DependencyInjection.ServiceProvider)">
            <summary>
            Initializes the database with migrations
            </summary>
            <param name="serviceProvider">Service provider</param>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Implementations.RailwayApiService">
            <summary>
            Implementation of Railway API service for external API communication
            Provides comprehensive integration with Railway APIs for real-time data synchronization
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Implementations.RailwayApiService.DataReceived">
            <summary>
            Occurs when new data is received from the Railway API
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Implementations.RailwayApiService.ApiError">
            <summary>
            Occurs when an error is encountered during API operations
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Implementations.RailwayApiService.ConnectionStatusChanged">
            <summary>
            Occurs when the API connection status changes
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Implementations.ScheduleSyncService">
            <summary>
            Implementation of background schedule synchronization service
            Manages continuous synchronization between local system and Railway API
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Implementations.StationService">
            <summary>
            Service implementation for managing railway stations
            Provides CRUD operations and business logic for station management
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.#ctor(IPIS.WindowsApp.Data.IPISDbContext,Microsoft.Extensions.Logging.ILogger{IPIS.WindowsApp.Services.Implementations.StationService})">
            <summary>
            Initializes a new instance of the StationService
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger instance</param>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetAllStationsAsync">
            <summary>
            Gets all stations from the database
            </summary>
            <returns>Collection of all stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetStationByIdAsync(System.Int32)">
            <summary>
            Gets a station by its unique identifier
            </summary>
            <param name="id">Station ID</param>
            <returns>Station if found, null otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetStationByCodeAsync(System.String)">
            <summary>
            Gets a station by its code
            </summary>
            <param name="code">Station code</param>
            <returns>Station if found, null otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.CreateStationAsync(IPIS.WindowsApp.Models.Station)">
            <summary>
            Creates a new station
            </summary>
            <param name="station">Station to create</param>
            <returns>Created station with assigned ID</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.UpdateStationAsync(IPIS.WindowsApp.Models.Station)">
            <summary>
            Updates an existing station
            </summary>
            <param name="station">Station to update</param>
            <returns>Updated station</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.DeleteStationAsync(System.Int32)">
            <summary>
            Deletes a station by ID
            </summary>
            <param name="id">Station ID to delete</param>
            <returns>True if deleted successfully, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.StationCodeExistsAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Checks if a station code already exists
            </summary>
            <param name="code">Station code to check</param>
            <param name="excludeId">Station ID to exclude from check (for updates)</param>
            <returns>True if code exists, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.SearchStationsAsync(System.String)">
            <summary>
            Searches stations by name or code
            </summary>
            <param name="searchTerm">Search term</param>
            <returns>Collection of matching stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetStationsByTypeAsync(IPIS.WindowsApp.Models.Enums.StationType)">
            <summary>
            Gets stations by type
            </summary>
            <param name="stationType">Type of station</param>
            <returns>Collection of stations of the specified type</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.ValidateStationAsync(IPIS.WindowsApp.Models.Station)">
            <summary>
            Validates station data
            </summary>
            <param name="station">Station to validate</param>
            <returns>Validation result with any errors</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetStationCountAsync">
            <summary>
            Gets the total count of stations
            </summary>
            <returns>Total number of stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetStationsPagedAsync(System.Int32,System.Int32)">
            <summary>
            Gets stations with pagination
            </summary>
            <param name="pageNumber">Page number (1-based)</param>
            <param name="pageSize">Number of items per page</param>
            <returns>Paginated result of stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Implementations.StationService.GetActiveStationsAsync">
            <summary>
            Gets all active stations
            </summary>
            <returns>Collection of active stations</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver">
            <summary>
            Interface for data conflict resolution service
            Manages conflicts between local system data and Railway API data
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.DetectConflictsAsync(System.Threading.CancellationToken)">
            <summary>
            Detects conflicts between local and API data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of detected conflicts</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.DetectScheduleConflictsAsync(IPIS.WindowsApp.Models.Entities.Schedule,IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto,System.Threading.CancellationToken)">
            <summary>
            Detects conflicts for a specific schedule
            </summary>
            <param name="localSchedule">Local schedule data</param>
            <param name="apiSchedule">API schedule data</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of conflicts for the schedule</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.DetectTrainStatusConflictsAsync(IPIS.WindowsApp.Models.Entities.Train,IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto,System.Threading.CancellationToken)">
            <summary>
            Detects conflicts for a specific train status
            </summary>
            <param name="localTrain">Local train data</param>
            <param name="apiStatus">API train status data</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of conflicts for the train</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.DetectPlatformConflictsAsync(IPIS.WindowsApp.Models.Entities.Schedule,IPIS.WindowsApp.Models.DTOs.Railway.PlatformAssignmentDto,System.Threading.CancellationToken)">
            <summary>
            Detects conflicts for platform assignments
            </summary>
            <param name="localSchedule">Local schedule with platform assignment</param>
            <param name="apiAssignment">API platform assignment data</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of platform assignment conflicts</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ResolveScheduleConflictAsync(IPIS.WindowsApp.Models.Entities.Schedule,IPIS.WindowsApp.Models.DTOs.Railway.TrainScheduleDto,System.Nullable{IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy},System.Threading.CancellationToken)">
            <summary>
            Resolves a specific schedule conflict
            </summary>
            <param name="localSchedule">Local schedule data</param>
            <param name="apiSchedule">API schedule data</param>
            <param name="strategy">Resolution strategy to use</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conflict resolution result</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ApplyConflictResolutionsAsync(System.Collections.Generic.List{IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution},System.Threading.CancellationToken)">
            <summary>
            Resolves multiple conflicts using specified resolutions
            </summary>
            <param name="resolutions">List of conflict resolutions to apply</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of resolution results</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.AutoResolveConflictsAsync(System.Collections.Generic.List{IPIS.WindowsApp.Models.DTOs.Railway.DataConflict},System.Threading.CancellationToken)">
            <summary>
            Automatically resolves conflicts using configured strategies
            </summary>
            <param name="conflicts">Conflicts to resolve</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of resolution results</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetUnresolvedConflictsAsync(System.Threading.CancellationToken)">
            <summary>
            Gets all unresolved conflicts
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of unresolved conflicts</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetConflictsBySeverityAsync(IPIS.WindowsApp.Models.DTOs.Railway.ConflictSeverity,System.Threading.CancellationToken)">
            <summary>
            Gets conflicts by severity level
            </summary>
            <param name="severity">Severity level to filter by</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of conflicts with specified severity</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetEntityConflictsAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Gets conflicts for a specific entity
            </summary>
            <param name="entityType">Type of entity</param>
            <param name="entityId">Entity identifier</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of conflicts for the entity</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.MarkConflictResolvedAsync(System.String,IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionResult,System.Threading.CancellationToken)">
            <summary>
            Marks a conflict as resolved
            </summary>
            <param name="conflictId">Conflict identifier</param>
            <param name="resolutionResult">Resolution result</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the mark operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetDefaultStrategy(IPIS.WindowsApp.Models.DTOs.Railway.ConflictType,IPIS.WindowsApp.Models.DTOs.Railway.ConflictSeverity)">
            <summary>
            Gets the default resolution strategy for a conflict type
            </summary>
            <param name="conflictType">Type of conflict</param>
            <param name="severity">Conflict severity</param>
            <returns>Default resolution strategy</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.SetDefaultStrategyAsync(IPIS.WindowsApp.Models.DTOs.Railway.ConflictType,IPIS.WindowsApp.Models.DTOs.Railway.ConflictSeverity,IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolutionStrategy,System.Threading.CancellationToken)">
            <summary>
            Sets the default resolution strategy for a conflict type
            </summary>
            <param name="conflictType">Type of conflict</param>
            <param name="severity">Conflict severity</param>
            <param name="strategy">Resolution strategy</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the set operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetApplicableStrategies(IPIS.WindowsApp.Models.DTOs.Railway.DataConflict)">
            <summary>
            Gets available resolution strategies for a conflict
            </summary>
            <param name="conflict">Conflict to get strategies for</param>
            <returns>List of applicable resolution strategies</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ApplyBusinessRulesAsync(IPIS.WindowsApp.Models.DTOs.Railway.DataConflict,System.Threading.CancellationToken)">
            <summary>
            Applies business rules to determine conflict resolution
            </summary>
            <param name="conflict">Conflict to apply rules to</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Recommended resolution strategy</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ValidateResolutionAsync(IPIS.WindowsApp.Models.DTOs.Railway.DataConflict,IPIS.WindowsApp.Models.DTOs.Railway.ConflictResolution,System.Threading.CancellationToken)">
            <summary>
            Validates a proposed resolution against business rules
            </summary>
            <param name="conflict">Conflict being resolved</param>
            <param name="proposedResolution">Proposed resolution</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetConflictStatisticsAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets conflict resolution statistics
            </summary>
            <param name="startDate">Start date for statistics</param>
            <param name="endDate">End date for statistics</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conflict resolution statistics</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.GetResolutionHistoryAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets the conflict resolution history
            </summary>
            <param name="limit">Maximum number of records to return</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of resolution history records</returns>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ConflictDetected">
            <summary>
            Event raised when a new conflict is detected
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ConflictResolved">
            <summary>
            Event raised when a conflict is resolved
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.ConflictResolutionFailed">
            <summary>
            Event raised when conflict resolution fails
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IDataConflictResolver.CriticalConflictDetected">
            <summary>
            Event raised when a critical conflict requires immediate attention
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ValidationResult">
            <summary>
            Validation result for conflict resolution
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ConflictStatistics">
            <summary>
            Conflict resolution statistics
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ConflictResolutionHistory">
            <summary>
            Conflict resolution history record
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ConflictDetectedEventArgs">
            <summary>
            Event arguments for conflict detected events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ConflictResolvedEventArgs">
            <summary>
            Event arguments for conflict resolved events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ConflictResolutionFailedEventArgs">
            <summary>
            Event arguments for conflict resolution failed events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.CriticalConflictEventArgs">
            <summary>
            Event arguments for critical conflict events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService">
            <summary>
            Interface for offline mode service operations
            Manages graceful degradation when Railway API is unavailable
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.IsOfflineModeActiveAsync(System.Threading.CancellationToken)">
            <summary>
            Indicates if the system is currently in offline mode
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if in offline mode</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetOfflineReasonAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the reason why the system is in offline mode
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Offline reason or null if online</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetOfflineModeStartTimeAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the timestamp when offline mode was activated
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Offline mode start time or null if online</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetOfflineDurationAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the duration the system has been offline
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Offline duration or null if online</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetCachedSchedulesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets cached schedules for a specific station
            </summary>
            <param name="stationCode">Station code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of cached schedules</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetCachedSchedulesAsync(System.String,System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets cached schedules for a specific date range
            </summary>
            <param name="stationCode">Station code</param>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of cached schedules</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.CacheScheduleDataAsync(System.Collections.Generic.List{IPIS.WindowsApp.Models.Entities.Schedule},System.Threading.CancellationToken)">
            <summary>
            Caches schedule data for offline use
            </summary>
            <param name="schedules">Schedules to cache</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the cache operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetCachedTrainStatusAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets cached train status information
            </summary>
            <param name="trainNumber">Train number</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Cached train status or null if not found</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.CacheTrainStatusDataAsync(System.Collections.Generic.List{IPIS.WindowsApp.Models.DTOs.Railway.TrainStatusDto},System.Threading.CancellationToken)">
            <summary>
            Caches train status data for offline use
            </summary>
            <param name="trainStatuses">Train statuses to cache</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the cache operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetLastSyncTimeAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the timestamp of the last successful API synchronization
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Last sync time or null if never synced</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetCachedDataAgeAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the age of cached data for a specific station
            </summary>
            <param name="stationCode">Station code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Data age or null if no cached data</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.IsCachedDataStaleAsync(System.String,System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Indicates if cached data is considered stale
            </summary>
            <param name="stationCode">Station code</param>
            <param name="maxAge">Maximum acceptable age</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if data is stale</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.ActivateOfflineModeAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Manually activates offline mode
            </summary>
            <param name="reason">Reason for activating offline mode</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the activation operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.TryReturnToOnlineModeAsync(System.Threading.CancellationToken)">
            <summary>
            Attempts to deactivate offline mode and return to online operation
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if successfully returned to online mode</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.ForceOnlineModeAsync(System.Threading.CancellationToken)">
            <summary>
            Forces the system back to online mode (bypasses connectivity checks)
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the force online operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.ClearAllCacheAsync(System.Threading.CancellationToken)">
            <summary>
            Clears all cached data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the clear operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.ClearStaleDataAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Clears cached data older than the specified age
            </summary>
            <param name="maxAge">Maximum age of data to keep</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Number of records cleared</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetCacheSizeAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the total size of cached data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Cache size information</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.OptimizeCacheAsync(System.Threading.CancellationToken)">
            <summary>
            Optimizes the cache by removing redundant or outdated data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Cache optimization result</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.GetFallbackSchedulesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets fallback schedule data when no cached data is available
            </summary>
            <param name="stationCode">Station code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of fallback schedules</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.SetFallbackSchedulesAsync(System.String,System.Collections.Generic.List{IPIS.WindowsApp.Models.Entities.Schedule},System.Threading.CancellationToken)">
            <summary>
            Sets fallback schedule data for emergency use
            </summary>
            <param name="stationCode">Station code</param>
            <param name="fallbackSchedules">Fallback schedules</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the set operation</returns>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.OfflineModeActivated">
            <summary>
            Event raised when offline mode is activated
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.OnlineModeRestored">
            <summary>
            Event raised when the system returns to online mode
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.CachedDataStale">
            <summary>
            Event raised when cached data becomes stale
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IOfflineModeService.CacheCleanup">
            <summary>
            Event raised when cache cleanup is performed
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.CacheSizeInfo">
            <summary>
            Information about cache size
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.CacheOptimizationResult">
            <summary>
            Result of cache optimization operation
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.OfflineModeActivatedEventArgs">
            <summary>
            Event arguments for offline mode activated events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.OnlineModeRestoredEventArgs">
            <summary>
            Event arguments for online mode restored events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.CachedDataStaleEventArgs">
            <summary>
            Event arguments for cached data stale events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.CacheCleanupEventArgs">
            <summary>
            Event arguments for cache cleanup events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService">
            <summary>
            Interface for Railway API service operations
            Provides comprehensive integration with external Railway APIs for real-time data synchronization
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainSchedulesAsync(System.String,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets train schedules for a specific station and date from Railway API
            </summary>
            <param name="stationCode">Station code to get schedules for</param>
            <param name="date">Date to get schedules for</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of train schedules</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainSchedulesAsync(System.Collections.Generic.List{System.String},System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets train schedules for multiple stations
            </summary>
            <param name="stationCodes">List of station codes</param>
            <param name="date">Date to get schedules for</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Dictionary of station codes and their schedules</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainScheduleAsync(System.String,System.String,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets schedule for a specific train at a specific station
            </summary>
            <param name="trainNumber">Train number</param>
            <param name="stationCode">Station code</param>
            <param name="date">Date to get schedule for</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Train schedule or null if not found</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainStatusAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets current status of a specific train from Railway API
            </summary>
            <param name="trainNumber">Train number to get status for</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Train status information</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainStatusesAsync(System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Gets status for multiple trains
            </summary>
            <param name="trainNumbers">List of train numbers</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Dictionary of train numbers and their status</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetLiveTrainPositionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets live train positions for trains in a specific region
            </summary>
            <param name="regionCode">Region or zone code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of train statuses in the region</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetDelayUpdatesAsync(System.Threading.CancellationToken)">
            <summary>
            Gets current delay information from Railway API
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of delay information</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetStationDelayUpdatesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets delay information for a specific station
            </summary>
            <param name="stationCode">Station code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of delays affecting the station</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainDelayUpdatesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets delay information for a specific train
            </summary>
            <param name="trainNumber">Train number</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of delays for the train</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetPlatformAssignmentsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets platform assignments for a specific station from Railway API
            </summary>
            <param name="stationCode">Station code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of platform assignments</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetTrainPlatformAssignmentAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Gets platform assignment for a specific train at a station
            </summary>
            <param name="trainNumber">Train number</param>
            <param name="stationCode">Station code</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Platform assignment or null if not found</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.SyncScheduleDataAsync(System.Threading.CancellationToken)">
            <summary>
            Performs full synchronization of schedule data from Railway API
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if synchronization was successful</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.SyncTrainStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Performs incremental synchronization of train status data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if synchronization was successful</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.SyncDelayInformationAsync(System.Threading.CancellationToken)">
            <summary>
            Performs synchronization of delay information
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if synchronization was successful</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.SyncPlatformAssignmentsAsync(System.Threading.CancellationToken)">
            <summary>
            Performs synchronization of platform assignments
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if synchronization was successful</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetApiHealthAsync(System.Threading.CancellationToken)">
            <summary>
            Gets health status of the Railway API
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>API health information</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.TestConnectionAsync(System.Threading.CancellationToken)">
            <summary>
            Tests connection to the Railway API
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if connection is successful</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.GetApiVersionAsync(System.Threading.CancellationToken)">
            <summary>
            Gets API version information
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>API version string</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.PerformBatchUpdateAsync(System.Collections.Generic.List{System.String},System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Performs batch update of multiple data types
            </summary>
            <param name="stationCodes">Station codes to update</param>
            <param name="includeSchedules">Include schedule updates</param>
            <param name="includeStatus">Include status updates</param>
            <param name="includeDelays">Include delay updates</param>
            <param name="includePlatforms">Include platform updates</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Batch update result</returns>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.DataReceived">
            <summary>
            Event raised when new data is received from the API
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.ApiError">
            <summary>
            Event raised when an API error occurs
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IRailwayApiService.ConnectionStatusChanged">
            <summary>
            Event raised when API connection status changes
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.BatchUpdateResult">
            <summary>
            Result of batch update operations
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ApiDataReceivedEventArgs">
            <summary>
            Event arguments for API data received events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ApiErrorEventArgs">
            <summary>
            Event arguments for API error events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.ApiConnectionStatusEventArgs">
            <summary>
            Event arguments for API connection status events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService">
            <summary>
            Interface for background schedule synchronization service
            Manages continuous synchronization between local system and Railway API
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.StartSynchronizationAsync(System.Threading.CancellationToken)">
            <summary>
            Starts the background synchronization process
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the start operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.StopSynchronizationAsync(System.Threading.CancellationToken)">
            <summary>
            Stops the background synchronization process
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the stop operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.PauseSynchronizationAsync(System.Threading.CancellationToken)">
            <summary>
            Pauses the synchronization process temporarily
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the pause operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.ResumeSynchronizationAsync(System.Threading.CancellationToken)">
            <summary>
            Resumes the paused synchronization process
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task representing the resume operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.GetSyncStatusAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the current synchronization status
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Current sync status information</returns>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.IsActive">
            <summary>
            Indicates if synchronization is currently active
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.IsPaused">
            <summary>
            Indicates if synchronization is currently paused
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.LastSyncTime">
            <summary>
            Gets the last synchronization timestamp
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.NextSyncTime">
            <summary>
            Gets the next scheduled synchronization timestamp
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.ForceFullSyncAsync(System.Threading.CancellationToken)">
            <summary>
            Forces a full synchronization of all data
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Sync status after completion</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.ForceIncrementalSyncAsync(System.Threading.CancellationToken)">
            <summary>
            Forces an incremental synchronization
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Sync status after completion</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SyncStationsAsync(System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Synchronizes data for specific stations only
            </summary>
            <param name="stationCodes">Station codes to synchronize</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Sync status after completion</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SyncTrainsAsync(System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Synchronizes data for specific trains only
            </summary>
            <param name="trainNumbers">Train numbers to synchronize</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Sync status after completion</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.UpdateSyncIntervalAsync(System.Int32)">
            <summary>
            Updates the synchronization interval
            </summary>
            <param name="intervalMinutes">New interval in minutes</param>
            <returns>Task representing the update operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SetRealTimeSyncAsync(System.Boolean)">
            <summary>
            Enables or disables real-time synchronization
            </summary>
            <param name="enabled">True to enable, false to disable</param>
            <returns>Task representing the update operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SetMaxConcurrentOperationsAsync(System.Int32)">
            <summary>
            Sets the maximum number of concurrent sync operations
            </summary>
            <param name="maxConcurrent">Maximum concurrent operations</param>
            <returns>Task representing the update operation</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.GetDailyStatisticsAsync(System.Threading.CancellationToken)">
            <summary>
            Gets synchronization statistics for the current day
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Daily sync statistics</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.GetStatisticsAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets synchronization statistics for a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Sync statistics for the period</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.GetSyncHistoryAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets the synchronization history
            </summary>
            <param name="limit">Maximum number of records to return</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of sync history records</returns>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SyncStarted">
            <summary>
            Event raised when synchronization starts
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SyncCompleted">
            <summary>
            Event raised when synchronization completes
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SyncFailed">
            <summary>
            Event raised when synchronization fails
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.SyncProgress">
            <summary>
            Event raised during synchronization to report progress
            </summary>
        </member>
        <member name="E:IPIS.WindowsApp.Services.Interfaces.IScheduleSyncService.StatusChanged">
            <summary>
            Event raised when sync status changes
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncStatistics">
            <summary>
            Synchronization statistics
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncHistoryRecord">
            <summary>
            Synchronization history record
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncStartedEventArgs">
            <summary>
            Event arguments for sync started events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncCompletedEventArgs">
            <summary>
            Event arguments for sync completed events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncFailedEventArgs">
            <summary>
            Event arguments for sync failed events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncProgressEventArgs">
            <summary>
            Event arguments for sync progress events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.SyncStatusChangedEventArgs">
            <summary>
            Event arguments for sync status changed events
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.IStationService">
            <summary>
            Service interface for managing railway stations
            Provides CRUD operations and business logic for station management
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetAllStationsAsync">
            <summary>
            Gets all stations from the database
            </summary>
            <returns>Collection of all stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetStationByIdAsync(System.Int32)">
            <summary>
            Gets a station by its unique identifier
            </summary>
            <param name="id">Station ID</param>
            <returns>Station if found, null otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetStationByCodeAsync(System.String)">
            <summary>
            Gets a station by its code
            </summary>
            <param name="code">Station code</param>
            <returns>Station if found, null otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.CreateStationAsync(IPIS.WindowsApp.Models.Station)">
            <summary>
            Creates a new station
            </summary>
            <param name="station">Station to create</param>
            <returns>Created station with assigned ID</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.UpdateStationAsync(IPIS.WindowsApp.Models.Station)">
            <summary>
            Updates an existing station
            </summary>
            <param name="station">Station to update</param>
            <returns>Updated station</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.DeleteStationAsync(System.Int32)">
            <summary>
            Deletes a station by ID
            </summary>
            <param name="id">Station ID to delete</param>
            <returns>True if deleted successfully, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.StationCodeExistsAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Checks if a station code already exists
            </summary>
            <param name="code">Station code to check</param>
            <param name="excludeId">Station ID to exclude from check (for updates)</param>
            <returns>True if code exists, false otherwise</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.SearchStationsAsync(System.String)">
            <summary>
            Searches stations by name or code
            </summary>
            <param name="searchTerm">Search term</param>
            <returns>Collection of matching stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetStationsByTypeAsync(IPIS.WindowsApp.Models.Enums.StationType)">
            <summary>
            Gets stations by type
            </summary>
            <param name="stationType">Type of station</param>
            <returns>Collection of stations of the specified type</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.ValidateStationAsync(IPIS.WindowsApp.Models.Station)">
            <summary>
            Validates station data
            </summary>
            <param name="station">Station to validate</param>
            <returns>Validation result with any errors</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetStationCountAsync">
            <summary>
            Gets the total count of stations
            </summary>
            <returns>Total number of stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetStationsPagedAsync(System.Int32,System.Int32)">
            <summary>
            Gets stations with pagination
            </summary>
            <param name="pageNumber">Page number (1-based)</param>
            <param name="pageSize">Number of items per page</param>
            <returns>Paginated result of stations</returns>
        </member>
        <member name="M:IPIS.WindowsApp.Services.Interfaces.IStationService.GetActiveStationsAsync">
            <summary>
            Gets all active stations
            </summary>
            <returns>Collection of active stations</returns>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.StationValidationResult">
            <summary>
            Validation result for station operations
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.StationValidationResult.IsValid">
            <summary>
            Gets or sets whether the validation was successful
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.StationValidationResult.Errors">
            <summary>
            Gets or sets the validation error messages
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.StationValidationResult.Warnings">
            <summary>
            Gets or sets the validation warning messages
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1">
            <summary>
            Paginated result for station queries
            </summary>
            <typeparam name="T">Type of items in the result</typeparam>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.Items">
            <summary>
            Gets or sets the items in the current page
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.PageNumber">
            <summary>
            Gets or sets the current page number
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.PageSize">
            <summary>
            Gets or sets the page size
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.TotalCount">
            <summary>
            Gets or sets the total number of items
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.TotalPages">
            <summary>
            Gets the total number of pages
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.HasPreviousPage">
            <summary>
            Gets whether there is a previous page
            </summary>
        </member>
        <member name="P:IPIS.WindowsApp.Services.Interfaces.PaginatedResult`1.HasNextPage">
            <summary>
            Gets whether there is a next page
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests">
            <summary>
            Comprehensive integration tests for Railway API functionality
            Tests all Railway API services and their integration with the IPIS system
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestRailwayApiServiceInitialization">
            <summary>
            Tests Railway API service initialization and configuration
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestTrainScheduleRetrieval">
            <summary>
            Tests train schedule retrieval functionality
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestTrainStatusMonitoring">
            <summary>
            Tests train status monitoring functionality
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestDelayInformationRetrieval">
            <summary>
            Tests delay information retrieval functionality
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestPlatformAssignments">
            <summary>
            Tests platform assignment functionality
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestScheduleSynchronization">
            <summary>
            Tests schedule synchronization functionality
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.TestErrorHandlingAndResilience">
            <summary>
            Tests error handling and resilience
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.RunAllTests">
            <summary>
            Runs all Railway API integration tests
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RailwayApiIntegrationTests.Dispose">
            <summary>
            Disposes test resources
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Tests.RunTests">
            <summary>
            Simple test execution program for Railway API integration tests
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.RunTests.Main(System.String[])">
            <summary>
            Executes the Railway API integration tests
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.Tests.TestRunner">
            <summary>
            Test runner for executing Railway API integration tests
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.Tests.TestRunner.Main(System.String[])">
            <summary>
            Main entry point for running Railway API integration tests
            </summary>
        </member>
        <member name="T:IPIS.WindowsApp.ApplicationConfiguration">
            <summary>
             Bootstrap the application configuration.
            </summary>
        </member>
        <member name="M:IPIS.WindowsApp.ApplicationConfiguration.Initialize">
            <summary>
             Bootstrap the application as follows:
             <code>
             global::System.Windows.Forms.Application.EnableVisualStyles();
             global::System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(false);
             global::System.Windows.Forms.Application.SetHighDpiMode(HighDpiMode.SystemAware);
            </code>
            </summary>
        </member>
    </members>
</doc>
