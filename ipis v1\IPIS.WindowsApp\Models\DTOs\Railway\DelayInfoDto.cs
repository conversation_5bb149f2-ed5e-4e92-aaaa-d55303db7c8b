using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// Data Transfer Object for delay information from Railway API
    /// Provides detailed delay tracking and recovery information
    /// </summary>
    public class DelayInfoDto
    {
        /// <summary>
        /// Train number experiencing the delay
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Station code where delay is reported
        /// </summary>
        [Required]
        [StringLength(10)]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Delay amount in minutes
        /// </summary>
        [Range(0, 1440)] // Max 24 hours
        public int DelayMinutes { get; set; }

        /// <summary>
        /// Reason for the delay
        /// </summary>
        [Required]
        [StringLength(200)]
        public string DelayReason { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when delay was reported
        /// </summary>
        public DateTime DelayReportedAt { get; set; }

        /// <summary>
        /// Category of delay (Technical, Weather, Operational, etc.)
        /// </summary>
        [StringLength(50)]
        public string DelayCategory { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the delay has been recovered
        /// </summary>
        public bool IsRecovered { get; set; }

        /// <summary>
        /// Time when delay was recovered (if applicable)
        /// </summary>
        public DateTime? RecoveryTime { get; set; }

        /// <summary>
        /// Train name
        /// </summary>
        [StringLength(100)]
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Station name where delay occurred
        /// </summary>
        [StringLength(100)]
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// Severity level of the delay
        /// </summary>
        [StringLength(20)]
        public string SeverityLevel { get; set; } = "Medium";

        /// <summary>
        /// Expected recovery time
        /// </summary>
        public DateTime? ExpectedRecoveryTime { get; set; }

        /// <summary>
        /// Impact on subsequent stations
        /// </summary>
        [StringLength(500)]
        public string ImpactDescription { get; set; } = string.Empty;

        /// <summary>
        /// Responsible department/authority
        /// </summary>
        [StringLength(100)]
        public string ResponsibleDepartment { get; set; } = string.Empty;

        /// <summary>
        /// Corrective actions taken
        /// </summary>
        [StringLength(500)]
        public string CorrectiveActions { get; set; } = string.Empty;

        /// <summary>
        /// Previous delay at this station (for comparison)
        /// </summary>
        public int? PreviousDelayMinutes { get; set; }

        /// <summary>
        /// Delay trend (Increasing, Decreasing, Stable)
        /// </summary>
        [StringLength(20)]
        public string DelayTrend { get; set; } = "Stable";

        /// <summary>
        /// Additional metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Computed property: Gets the delay description
        /// </summary>
        public string DelayDescription
        {
            get
            {
                if (DelayMinutes == 0) return "On Time";
                if (DelayMinutes < 60) return $"{DelayMinutes} min late";
                
                var hours = DelayMinutes / 60;
                var minutes = DelayMinutes % 60;
                
                if (minutes == 0) return $"{hours}h late";
                return $"{hours}h {minutes}m late";
            }
        }

        /// <summary>
        /// Computed property: Gets the severity color code
        /// </summary>
        public string SeverityColor => SeverityLevel.ToLowerInvariant() switch
        {
            "low" => "#28a745",      // Green
            "medium" => "#ffc107",   // Yellow
            "high" => "#fd7e14",     // Orange
            "critical" => "#dc3545", // Red
            _ => "#6c757d"           // Gray
        };

        /// <summary>
        /// Computed property: Time since delay was reported
        /// </summary>
        public TimeSpan TimeSinceReported => DateTime.UtcNow - DelayReportedAt;

        /// <summary>
        /// Computed property: Recovery duration (if recovered)
        /// </summary>
        public TimeSpan? RecoveryDuration => IsRecovered && RecoveryTime.HasValue 
            ? RecoveryTime.Value - DelayReportedAt 
            : null;

        /// <summary>
        /// Computed property: Indicates if delay is significant (>30 minutes)
        /// </summary>
        public bool IsSignificantDelay => DelayMinutes >= 30;

        /// <summary>
        /// Computed property: Indicates if delay is critical (>2 hours)
        /// </summary>
        public bool IsCriticalDelay => DelayMinutes >= 120;

        /// <summary>
        /// Validates the DTO data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TrainNumber) &&
                   !string.IsNullOrWhiteSpace(StationCode) &&
                   !string.IsNullOrWhiteSpace(DelayReason) &&
                   DelayReportedAt != default;
        }

        /// <summary>
        /// Gets the delay change compared to previous report
        /// </summary>
        /// <returns>Delay change in minutes (positive = increased, negative = decreased)</returns>
        public int GetDelayChange()
        {
            return PreviousDelayMinutes.HasValue 
                ? DelayMinutes - PreviousDelayMinutes.Value 
                : 0;
        }

        /// <summary>
        /// Gets a summary of the delay information
        /// </summary>
        /// <returns>Delay summary string</returns>
        public string GetSummary()
        {
            var summary = $"Train {TrainNumber}";
            if (!string.IsNullOrWhiteSpace(TrainName))
                summary += $" ({TrainName})";
            
            summary += $" delayed by {DelayDescription} at {StationCode}";
            
            if (!string.IsNullOrWhiteSpace(StationName))
                summary += $" ({StationName})";
            
            if (!string.IsNullOrWhiteSpace(DelayReason))
                summary += $" - {DelayReason}";
            
            if (IsRecovered)
                summary += " [RECOVERED]";
            
            return summary;
        }

        /// <summary>
        /// Gets detailed delay information for reporting
        /// </summary>
        /// <returns>Detailed delay report</returns>
        public string GetDetailedReport()
        {
            var report = GetSummary();
            report += $"\nReported: {DelayReportedAt:yyyy-MM-dd HH:mm:ss}";
            report += $"\nCategory: {DelayCategory}";
            report += $"\nSeverity: {SeverityLevel}";
            
            if (!string.IsNullOrWhiteSpace(ResponsibleDepartment))
                report += $"\nResponsible: {ResponsibleDepartment}";
            
            if (!string.IsNullOrWhiteSpace(CorrectiveActions))
                report += $"\nActions: {CorrectiveActions}";
            
            if (IsRecovered && RecoveryTime.HasValue)
                report += $"\nRecovered: {RecoveryTime:yyyy-MM-dd HH:mm:ss}";
            else if (ExpectedRecoveryTime.HasValue)
                report += $"\nExpected Recovery: {ExpectedRecoveryTime:yyyy-MM-dd HH:mm:ss}";
            
            return report;
        }

        /// <summary>
        /// Returns a string representation of the delay info
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }

        /// <summary>
        /// Creates a copy of the DTO
        /// </summary>
        /// <returns>Cloned DTO</returns>
        public DelayInfoDto Clone()
        {
            return new DelayInfoDto
            {
                TrainNumber = TrainNumber,
                StationCode = StationCode,
                DelayMinutes = DelayMinutes,
                DelayReason = DelayReason,
                DelayReportedAt = DelayReportedAt,
                DelayCategory = DelayCategory,
                IsRecovered = IsRecovered,
                RecoveryTime = RecoveryTime,
                TrainName = TrainName,
                StationName = StationName,
                SeverityLevel = SeverityLevel,
                ExpectedRecoveryTime = ExpectedRecoveryTime,
                ImpactDescription = ImpactDescription,
                ResponsibleDepartment = ResponsibleDepartment,
                CorrectiveActions = CorrectiveActions,
                PreviousDelayMinutes = PreviousDelayMinutes,
                DelayTrend = DelayTrend,
                Metadata = new Dictionary<string, object>(Metadata)
            };
        }
    }

    /// <summary>
    /// Enumeration for delay severity levels
    /// </summary>
    public enum DelaySeverity
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// Enumeration for delay categories
    /// </summary>
    public enum DelayCategory
    {
        Technical,
        Weather,
        Operational,
        Infrastructure,
        Security,
        Passenger,
        External,
        Unknown
    }

    /// <summary>
    /// Enumeration for delay trends
    /// </summary>
    public enum DelayTrend
    {
        Increasing,
        Decreasing,
        Stable,
        Recovering,
        Worsening
    }
}
