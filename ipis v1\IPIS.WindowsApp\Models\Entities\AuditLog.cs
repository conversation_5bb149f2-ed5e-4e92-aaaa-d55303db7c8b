using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents an audit log entry for compliance and tracking
    /// Based on legacy system analysis - comprehensive audit trail for railway operations
    /// </summary>
    [Table("AuditLogs")]
    public class AuditLog
    {
        /// <summary>
        /// Primary key for the audit log entry
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the user who performed the action
        /// </summary>
        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        /// <summary>
        /// Type of action performed
        /// </summary>
        [Required]
        public AuditAction Action { get; set; }

        /// <summary>
        /// Name of the entity that was affected
        /// </summary>
        [Required(ErrorMessage = "Entity name is required")]
        [StringLength(100, ErrorMessage = "Entity name cannot exceed 100 characters")]
        [Column("EntityName")]
        public string EntityName { get; set; } = string.Empty;

        /// <summary>
        /// ID of the entity that was affected
        /// </summary>
        [StringLength(50)]
        public string? EntityId { get; set; }

        /// <summary>
        /// Description of the action performed
        /// </summary>
        [Required(ErrorMessage = "Action description is required")]
        [StringLength(500, ErrorMessage = "Action description cannot exceed 500 characters")]
        [Column("ActionDescription")]
        public string ActionDescription { get; set; } = string.Empty;

        /// <summary>
        /// Detailed description or notes about the action
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Details { get; set; }

        /// <summary>
        /// Old values before the change (JSON format)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? OldValues { get; set; }

        /// <summary>
        /// New values after the change (JSON format)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? NewValues { get; set; }

        /// <summary>
        /// IP address from which the action was performed
        /// </summary>
        [StringLength(45)] // IPv6 support
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent or application information
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// Session ID when the action was performed
        /// </summary>
        [StringLength(255)]
        public string? SessionId { get; set; }

        /// <summary>
        /// Module or feature where the action occurred
        /// </summary>
        [StringLength(100)]
        public string? Module { get; set; }

        /// <summary>
        /// Function or method that was called
        /// </summary>
        [StringLength(100)]
        public string? Function { get; set; }

        /// <summary>
        /// Severity level of the action (Info, Warning, Error, Critical)
        /// </summary>
        [StringLength(20)]
        public string Severity { get; set; } = "Info";

        /// <summary>
        /// Result of the action (Success, Failed, Partial)
        /// </summary>
        [StringLength(20)]
        public string Result { get; set; } = "Success";

        /// <summary>
        /// Error message if the action failed
        /// </summary>
        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Duration of the action in milliseconds
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Duration must be non-negative")]
        public int? DurationMs { get; set; }

        /// <summary>
        /// Risk level associated with the action (Low, Medium, High, Critical)
        /// </summary>
        [StringLength(20)]
        public string RiskLevel { get; set; } = "Low";

        /// <summary>
        /// Compliance category (Security, Safety, Operational, Financial)
        /// </summary>
        [StringLength(50)]
        public string? ComplianceCategory { get; set; }

        /// <summary>
        /// Reference to external system or transaction ID
        /// </summary>
        [StringLength(100)]
        public string? ExternalReference { get; set; }

        /// <summary>
        /// Tags for categorization and search
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Additional metadata in JSON format
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Metadata { get; set; }

        /// <summary>
        /// Indicates if this entry requires review
        /// </summary>
        public bool RequiresReview { get; set; } = false;

        /// <summary>
        /// Indicates if this entry has been reviewed
        /// </summary>
        public bool IsReviewed { get; set; } = false;

        /// <summary>
        /// User who reviewed this entry
        /// </summary>
        [StringLength(100)]
        public string? ReviewedBy { get; set; }

        /// <summary>
        /// Timestamp when this entry was reviewed
        /// </summary>
        public DateTime? ReviewedAt { get; set; }

        /// <summary>
        /// Review notes or comments
        /// </summary>
        [StringLength(1000)]
        public string? ReviewNotes { get; set; }

        /// <summary>
        /// Indicates if this entry is archived
        /// </summary>
        public bool IsArchived { get; set; } = false;

        /// <summary>
        /// Timestamp when this entry was archived
        /// </summary>
        public DateTime? ArchivedAt { get; set; }

        /// <summary>
        /// Retention period in days (0 = indefinite)
        /// </summary>
        [Range(0, 36500, ErrorMessage = "Retention days must be between 0 and 36500")]
        public int RetentionDays { get; set; } = 2555; // 7 years default

        /// <summary>
        /// Timestamp when the action occurred
        /// </summary>
        [Required]
        [Column("Timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        // Navigation Properties

        /// <summary>
        /// The user who performed the action
        /// </summary>
        public virtual User User { get; set; } = null!;

        // Computed Properties

        /// <summary>
        /// Indicates if this entry should be deleted based on retention policy
        /// </summary>
        [NotMapped]
        public bool ShouldBeDeleted
        {
            get
            {
                if (RetentionDays == 0) return false; // Indefinite retention
                return DateTime.UtcNow > Timestamp.AddDays(RetentionDays);
            }
        }

        /// <summary>
        /// Gets the age of this audit entry in days
        /// </summary>
        [NotMapped]
        public int AgeDays => (DateTime.UtcNow - Timestamp).Days;

        /// <summary>
        /// Gets the remaining retention days
        /// </summary>
        [NotMapped]
        public int RemainingRetentionDays
        {
            get
            {
                if (RetentionDays == 0) return int.MaxValue; // Indefinite
                var remaining = RetentionDays - AgeDays;
                return Math.Max(0, remaining);
            }
        }

        /// <summary>
        /// Gets the duration in a human-readable format
        /// </summary>
        [NotMapped]
        public string DurationFormatted
        {
            get
            {
                if (!DurationMs.HasValue) return "N/A";
                
                if (DurationMs < 1000) return $"{DurationMs}ms";
                if (DurationMs < 60000) return $"{DurationMs / 1000.0:F1}s";
                
                var minutes = DurationMs / 60000;
                var seconds = (DurationMs % 60000) / 1000.0;
                return $"{minutes}m {seconds:F1}s";
            }
        }

        /// <summary>
        /// Gets the tags as a list
        /// </summary>
        [NotMapped]
        public List<string> TagList => string.IsNullOrWhiteSpace(Tags) 
            ? new List<string>() 
            : Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                  .Select(tag => tag.Trim())
                  .ToList();

        /// <summary>
        /// Gets a formatted summary of the audit entry
        /// </summary>
        [NotMapped]
        public string Summary => $"{User?.Username ?? "Unknown"} {Action} {EntityName} " +
                                $"({Result}) at {Timestamp:yyyy-MM-dd HH:mm:ss}";

        // Methods

        /// <summary>
        /// Marks this entry as reviewed
        /// </summary>
        /// <param name="reviewedBy">User who reviewed the entry</param>
        /// <param name="notes">Review notes</param>
        public void MarkAsReviewed(string reviewedBy, string? notes = null)
        {
            IsReviewed = true;
            ReviewedBy = reviewedBy;
            ReviewedAt = DateTime.UtcNow;
            ReviewNotes = notes;
        }

        /// <summary>
        /// Archives this entry
        /// </summary>
        public void Archive()
        {
            IsArchived = true;
            ArchivedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Adds a tag to the audit entry
        /// </summary>
        /// <param name="tag">Tag to add</param>
        public void AddTag(string tag)
        {
            if (string.IsNullOrWhiteSpace(tag)) return;

            var tags = TagList;
            if (!tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
            {
                tags.Add(tag.Trim());
                Tags = string.Join(", ", tags);
            }
        }

        /// <summary>
        /// Removes a tag from the audit entry
        /// </summary>
        /// <param name="tag">Tag to remove</param>
        public void RemoveTag(string tag)
        {
            if (string.IsNullOrWhiteSpace(tag)) return;

            var tags = TagList;
            if (tags.RemoveAll(t => t.Equals(tag, StringComparison.OrdinalIgnoreCase)) > 0)
            {
                Tags = string.Join(", ", tags);
            }
        }

        /// <summary>
        /// Sets the old and new values for change tracking
        /// </summary>
        /// <param name="oldValues">Old values object</param>
        /// <param name="newValues">New values object</param>
        public void SetChangeValues(object? oldValues, object? newValues)
        {
            if (oldValues != null)
            {
                OldValues = System.Text.Json.JsonSerializer.Serialize(oldValues);
            }
            
            if (newValues != null)
            {
                NewValues = System.Text.Json.JsonSerializer.Serialize(newValues);
            }
        }

        /// <summary>
        /// Gets the old values as a typed object
        /// </summary>
        /// <typeparam name="T">Type to deserialize to</typeparam>
        /// <returns>Deserialized old values or default</returns>
        public T? GetOldValues<T>() where T : class
        {
            if (string.IsNullOrWhiteSpace(OldValues)) return null;
            
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<T>(OldValues);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Gets the new values as a typed object
        /// </summary>
        /// <typeparam name="T">Type to deserialize to</typeparam>
        /// <returns>Deserialized new values or default</returns>
        public T? GetNewValues<T>() where T : class
        {
            if (string.IsNullOrWhiteSpace(NewValues)) return null;
            
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<T>(NewValues);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Validates if the audit log has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return UserId > 0 && 
                   !string.IsNullOrWhiteSpace(EntityName) && 
                   !string.IsNullOrWhiteSpace(ActionDescription);
        }

        /// <summary>
        /// Gets a detailed description of the audit entry
        /// </summary>
        /// <returns>Detailed audit description</returns>
        public string GetDetailedDescription()
        {
            var description = $"{ActionDescription}";
            
            if (!string.IsNullOrWhiteSpace(Details))
            {
                description += $" - {Details}";
            }
            
            if (!string.IsNullOrWhiteSpace(ErrorMessage))
            {
                description += $" (Error: {ErrorMessage})";
            }
            
            return description;
        }

        /// <summary>
        /// Returns a string representation of the audit log
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return Summary;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current audit log
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is AuditLog other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the audit log
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
