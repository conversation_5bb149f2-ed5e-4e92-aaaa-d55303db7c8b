using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using IPIS.WindowsApp.Data;
using IPIS.WindowsApp.Configuration;
using IPIS.WindowsApp.Services.Interfaces;
using IPIS.WindowsApp.Services.Implementations;
using IPIS.WindowsApp.Forms;

namespace IPIS.WindowsApp
{
    /// <summary>
    /// Main program entry point for IPIS Windows Forms Application
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configure Serilog early
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/ipis-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                Log.Information("Starting IPIS Windows Application");

                // To customize application configuration such as set high DPI settings or default font,
                // see https://aka.ms/applicationconfiguration.
                ApplicationConfiguration.Initialize();

                // Build configuration
                var configuration = BuildConfiguration();

                // Build service provider
                var serviceProvider = BuildServiceProvider(configuration);

                // Initialize database
                InitializeDatabase(serviceProvider);

                // Start the application
                var mainForm = serviceProvider.GetRequiredService<MainForm>();
                Application.Run(mainForm);

                Log.Information("IPIS Windows Application stopped");
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "IPIS Windows Application terminated unexpectedly");
                MessageBox.Show($"A fatal error occurred: {ex.Message}", "IPIS Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// Builds the application configuration
        /// </summary>
        /// <returns>Configuration root</returns>
        private static IConfiguration BuildConfiguration()
        {
            return new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json", 
                    optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();
        }

        /// <summary>
        /// Builds the service provider with dependency injection
        /// </summary>
        /// <param name="configuration">Application configuration</param>
        /// <returns>Service provider</returns>
        private static ServiceProvider BuildServiceProvider(IConfiguration configuration)
        {
            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton<IConfiguration>(configuration);

            // Configure options
            services.Configure<RailwayApiConfiguration>(configuration.GetSection("RailwayApi"));
            services.Configure<SyncConfiguration>(configuration.GetSection("Synchronization"));

            // Add logging
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // Add Entity Framework
            services.AddDbContext<IPISDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                options.UseSqlite(connectionString);
                options.EnableSensitiveDataLogging(false);
                options.EnableDetailedErrors(true);
            });

            // Add HTTP client for Railway API
            services.AddHttpClient<IRailwayApiService, RailwayApiService>(client =>
            {
                var apiConfig = configuration.GetSection("RailwayApi").Get<RailwayApiConfiguration>();
                if (apiConfig != null)
                {
                    client.BaseAddress = new Uri(apiConfig.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(apiConfig.TimeoutSeconds);
                }
            });

            // Add application services
            services.AddScoped<IRailwayApiService, RailwayApiService>();
            services.AddSingleton<IScheduleSyncService, ScheduleSyncService>();

            // Add Windows Forms
            services.AddScoped<MainForm>();

            // Add hosted services
            services.AddHostedService<ScheduleSyncService>();

            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Initializes the database with migrations
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        private static void InitializeDatabase(ServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<IPISDbContext>();
                var configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();

                // Check if auto-migration is enabled
                var autoMigrate = configuration.GetValue<bool>("Application:DatabaseAutoMigrate", true);
                
                if (autoMigrate)
                {
                    Log.Information("Applying database migrations");
                    context.Database.Migrate();
                    Log.Information("Database migrations completed");
                }
                else
                {
                    // Just ensure database is created
                    context.Database.EnsureCreated();
                    Log.Information("Database ensured created");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error initializing database");
                throw;
            }
        }
    }
}
