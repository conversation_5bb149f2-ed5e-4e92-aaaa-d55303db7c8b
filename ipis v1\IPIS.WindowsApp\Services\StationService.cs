using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using IPIS.WindowsApp.Data;
using IPIS.WindowsApp.Models;

namespace IPIS.WindowsApp.Services
{
    /// <summary>
    /// Service class for station-related operations
    /// </summary>
    public class StationService : IStationService
    {
        private readonly IPISDbContext _context;
        private readonly ILogger<StationService> _logger;

        /// <summary>
        /// Initializes a new instance of the StationService class
        /// </summary>
        /// <param name="context">The database context</param>
        /// <param name="logger">The logger instance</param>
        public StationService(IPISDbContext context, ILogger<StationService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets all stations asynchronously
        /// </summary>
        /// <returns>A collection of all stations</returns>
        public async Task<IEnumerable<Station>> GetAllStationsAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving all stations");
                return await _context.Stations
                    .Include(s => s.Platforms)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all stations");
                throw;
            }
        }

        /// <summary>
        /// Gets a station by its ID asynchronously
        /// </summary>
        /// <param name="id">The station ID</param>
        /// <returns>The station if found, null otherwise</returns>
        public async Task<Station?> GetStationByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Retrieving station with ID: {StationId}", id);
                return await _context.Stations
                    .Include(s => s.Platforms)
                    .Include(s => s.DisplayBoards)
                    .FirstOrDefaultAsync(s => s.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving station with ID: {StationId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets a station by its code asynchronously
        /// </summary>
        /// <param name="code">The station code</param>
        /// <returns>The station if found, null otherwise</returns>
        public async Task<Station?> GetStationByCodeAsync(string code)
        {
            try
            {
                _logger.LogInformation("Retrieving station with code: {StationCode}", code);
                return await _context.Stations
                    .Include(s => s.Platforms)
                    .Include(s => s.DisplayBoards)
                    .FirstOrDefaultAsync(s => s.Code == code);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving station with code: {StationCode}", code);
                throw;
            }
        }

        /// <summary>
        /// Creates a new station asynchronously
        /// </summary>
        /// <param name="station">The station to create</param>
        /// <returns>The created station</returns>
        public async Task<Station> CreateStationAsync(Station station)
        {
            try
            {
                _logger.LogInformation("Creating new station: {StationName} ({StationCode})", 
                    station.Name, station.Code);

                // Check if station code already exists
                var existingStation = await _context.Stations
                    .FirstOrDefaultAsync(s => s.Code == station.Code);

                if (existingStation != null)
                {
                    throw new InvalidOperationException($"Station with code '{station.Code}' already exists");
                }

                station.CreatedAt = DateTime.UtcNow;
                station.UpdatedAt = DateTime.UtcNow;

                _context.Stations.Add(station);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully created station with ID: {StationId}", station.Id);
                return station;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating station: {StationName} ({StationCode})", 
                    station.Name, station.Code);
                throw;
            }
        }

        /// <summary>
        /// Updates an existing station asynchronously
        /// </summary>
        /// <param name="station">The station to update</param>
        /// <returns>The updated station</returns>
        public async Task<Station> UpdateStationAsync(Station station)
        {
            try
            {
                _logger.LogInformation("Updating station with ID: {StationId}", station.Id);

                var existingStation = await _context.Stations.FindAsync(station.Id);
                if (existingStation == null)
                {
                    throw new InvalidOperationException($"Station with ID '{station.Id}' not found");
                }

                // Check if station code is being changed and if it conflicts with another station
                if (existingStation.Code != station.Code)
                {
                    var conflictingStation = await _context.Stations
                        .FirstOrDefaultAsync(s => s.Code == station.Code && s.Id != station.Id);

                    if (conflictingStation != null)
                    {
                        throw new InvalidOperationException($"Station with code '{station.Code}' already exists");
                    }
                }

                existingStation.Code = station.Code;
                existingStation.Name = station.Name;
                existingStation.Location = station.Location;
                existingStation.IsActive = station.IsActive;
                existingStation.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully updated station with ID: {StationId}", station.Id);
                return existingStation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating station with ID: {StationId}", station.Id);
                throw;
            }
        }

        /// <summary>
        /// Deletes a station asynchronously
        /// </summary>
        /// <param name="id">The ID of the station to delete</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        public async Task<bool> DeleteStationAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting station with ID: {StationId}", id);

                var station = await _context.Stations.FindAsync(id);
                if (station == null)
                {
                    _logger.LogWarning("Station with ID: {StationId} not found for deletion", id);
                    return false;
                }

                _context.Stations.Remove(station);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully deleted station with ID: {StationId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting station with ID: {StationId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all active stations asynchronously
        /// </summary>
        /// <returns>A collection of active stations</returns>
        public async Task<IEnumerable<Station>> GetActiveStationsAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving all active stations");
                return await _context.Stations
                    .Where(s => s.IsActive)
                    .Include(s => s.Platforms)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active stations");
                throw;
            }
        }
    }
}
