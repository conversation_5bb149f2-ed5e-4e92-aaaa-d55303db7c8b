# PowerShell script to fix all remaining ref parameter issues comprehensively

Write-Host "=== FIXING ALL REMAINING REF PARAMETER ISSUES ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix cgdb_api method calls
    $content = $content -replace 'cgdb_api\.cgdb_set_cfg_send_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'cgdb_api.cgdb_set_cfg_send_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'cgdb_api\.cgdb_set_cfg_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'cgdb_api.cgdb_set_cfg_res_pkt($1, $2, $3, ref $4, ref $5)'
    
    # Fix mldb_api method calls
    $content = $content -replace 'mldb_api\.mldb_link_check\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mldb_api.mldb_link_check($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'mldb_api\.mldb_link_check_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mldb_api.mldb_link_check_res_pkt($1, $2, $3, ref $4, ref $5, ref $6)'
    $content = $content -replace 'mldb_api\.mldb_set_cfg_send_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mldb_api.mldb_set_cfg_send_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'mldb_api\.mldb_set_cfg_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mldb_api.mldb_set_cfg_res_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'mldb_api\.mldb_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mldb_api.mldb_msg($1, $2, $3, ref $4, $5, $6, $7)'
    
    # Fix mdch_api method calls
    $content = $content -replace 'mdch_api\.mdch_link_check\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mdch_api.mdch_link_check($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'mdch_api\.mdch_link_check_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mdch_api.mdch_link_check_res_pkt($1, $2, $3, ref $4, ref $5, ref $6)'
    $content = $content -replace 'mdch_api\.mdch_set_cfg_send_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mdch_api.mdch_set_cfg_send_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'mdch_api\.mdch_set_cfg_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mdch_api.mdch_set_cfg_res_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'mdch_api\.mdch_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'mdch_api.mdch_msg($1, $2, $3, ref $4, $5, $6, $7)'
    
    # Fix pdb_api method calls
    $content = $content -replace 'pdb_api\.pdb_link_check\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdb_api.pdb_link_check($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'pdb_api\.pdb_link_check_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdb_api.pdb_link_check_res_pkt($1, $2, $3, ref $4, ref $5, ref $6)'
    $content = $content -replace 'pdb_api\.pdb_set_cfg_send_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdb_api.pdb_set_cfg_send_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'pdb_api\.pdb_set_cfg_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdb_api.pdb_set_cfg_res_pkt($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'pdb_api\.pdb_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdb_api.pdb_msg($1, $2, $3, ref $4, $5, $6, $7)'
    
    # Fix pdch_api method calls
    $content = $content -replace 'pdch_api\.pdch_link_check\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdch_api.pdch_link_check($1, $2, $3, ref $4, ref $5)'
    $content = $content -replace 'pdch_api\.pdch_link_check_res_pkt\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'pdch_api.pdch_link_check_res_pkt($1, $2, $3, ref $4, ref $5, ref $6)'
    
    # Fix double ref issues
    $content = $content -replace 'ref\s+ref\s+', 'ref '
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed ref parameter issues"
    }
}

Write-Host "All remaining ref parameter fixes complete!"
