# PowerShell script to comprehensively fix all remaining compilation issues

Write-Host "=== COMPREHENSIVE FIX FOR ALL REMAINING ISSUES ==="

# Get all C# files
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix 1: Remove all nameof() calls - replace with empty string
    $content = $content -replace 'nameof\([^)]+\)', '""'
    
    # Fix 2: Fix standalone ref expressions that are causing syntax errors
    # Remove invalid ref expressions like "ref variable;"
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;', ' $1;'
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*,', ' $1,'
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\)', ' $1)'
    
    # Fix 3: Initialize unassigned variables
    $content = $content -replace 'int num2;', 'int num2 = 0;'
    $content = $content -replace 'int num1;', 'int num1 = 0;'
    $content = $content -replace 'byte byteColon;', 'byte byteColon = 0;'
    $content = $content -replace 'int num;', 'int num = 0;'
    $content = $content -replace 'int length;', 'int length = 0;'
    $content = $content -replace 'int index1;', 'int index1 = 0;'
    $content = $content -replace 'int index3;', 'int index3 = 0;'
    $content = $content -replace 'short int16_1;', 'short int16_1 = 0;'
    $content = $content -replace 'short int16_2;', 'short int16_2 = 0;'
    $content = $content -replace 'short int16_3;', 'short int16_3 = 0;'
    
    # Fix 4: Add ref keywords to method calls that need them
    $content = $content -replace '\.get_BaseStn\(([^)]+)\)', '.get_BaseStn(ref $1)'
    $content = $content -replace '\.get_config\(([^)]+)\)', '.get_config(ref $1)'
    $content = $content -replace '\.delete_pwd\(([^,]+),\s*([^)]+)\)', '.delete_pwd($1, ref $2)'
    $content = $content -replace '\.dec_pwd\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.dec_pwd($1, ref $2, $3)'
    $content = $content -replace '\.enc_pwd\(([^,]+),\s*([^)]+)\)', '.enc_pwd($1, ref $2)'
    $content = $content -replace '\.set_user_pwd\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.set_user_pwd($1, $2, $3, ref $4)'
    $content = $content -replace '\.set_voice_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.set_voice_msg($1, $2, $3, $4, $5, ref $6)'
    $content = $content -replace '\.update_voice_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.update_voice_msg($1, $2, $3, $4, $5, ref $6)'
    $content = $content -replace '\.train_name_voice\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.train_name_voice($1, $2, ref $3)'
    $content = $content -replace '\.name_byte_default_msg\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.name_byte_default_msg($1, ref $2, ref $3)'
    $content = $content -replace 'Monitor\.Enter\(([^,]+),\s*([^)]+)\)', 'Monitor.Enter($1, ref $2)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed compilation issues"
    }
}

Write-Host "Comprehensive fix complete!"
