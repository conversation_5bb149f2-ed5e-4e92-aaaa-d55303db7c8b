namespace IPIS.WindowsApp.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuStrip1 = new MenuStrip();
            this.fileToolStripMenuItem = new ToolStripMenuItem();
            this.exitToolStripMenuItem = new ToolStripMenuItem();
            this.stationsToolStripMenuItem = new ToolStripMenuItem();
            this.addStationToolStripMenuItem = new ToolStripMenuItem();
            this.manageStationsToolStripMenuItem = new ToolStripMenuItem();
            this.trainsToolStripMenuItem = new ToolStripMenuItem();
            this.schedulesToolStripMenuItem = new ToolStripMenuItem();
            this.displayBoardsToolStripMenuItem = new ToolStripMenuItem();
            this.helpToolStripMenuItem = new ToolStripMenuItem();
            this.aboutToolStripMenuItem = new ToolStripMenuItem();
            this.toolStrip1 = new ToolStrip();
            this.addStationButton = new ToolStripButton();
            this.toolStripSeparator1 = new ToolStripSeparator();
            this.refreshButton = new ToolStripButton();
            this.statusStrip1 = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.splitContainer1 = new SplitContainer();
            this.groupBox1 = new GroupBox();
            this.stationsDataGridView = new DataGridView();
            this.groupBox2 = new GroupBox();
            this.infoLabel = new Label();
            this.menuStrip1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.stationsDataGridView)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // menuStrip1
            // 
            this.menuStrip1.ImageScalingSize = new Size(20, 20);
            this.menuStrip1.Items.AddRange(new ToolStripItem[] {
            this.fileToolStripMenuItem,
            this.stationsToolStripMenuItem,
            this.trainsToolStripMenuItem,
            this.schedulesToolStripMenuItem,
            this.displayBoardsToolStripMenuItem,
            this.helpToolStripMenuItem});
            this.menuStrip1.Location = new Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new Size(1200, 28);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            this.fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
            this.exitToolStripMenuItem});
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new Size(46, 24);
            this.fileToolStripMenuItem.Text = "&File";
            // 
            // exitToolStripMenuItem
            // 
            this.exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            this.exitToolStripMenuItem.Size = new Size(116, 26);
            this.exitToolStripMenuItem.Text = "E&xit";
            this.exitToolStripMenuItem.Click += new EventHandler(this.exitToolStripMenuItem_Click);
            // 
            // stationsToolStripMenuItem
            // 
            this.stationsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
            this.addStationToolStripMenuItem,
            this.manageStationsToolStripMenuItem});
            this.stationsToolStripMenuItem.Name = "stationsToolStripMenuItem";
            this.stationsToolStripMenuItem.Size = new Size(76, 24);
            this.stationsToolStripMenuItem.Text = "&Stations";
            // 
            // addStationToolStripMenuItem
            // 
            this.addStationToolStripMenuItem.Name = "addStationToolStripMenuItem";
            this.addStationToolStripMenuItem.Size = new Size(191, 26);
            this.addStationToolStripMenuItem.Text = "&Add Station";
            this.addStationToolStripMenuItem.Click += new EventHandler(this.addStationButton_Click);
            // 
            // manageStationsToolStripMenuItem
            // 
            this.manageStationsToolStripMenuItem.Name = "manageStationsToolStripMenuItem";
            this.manageStationsToolStripMenuItem.Size = new Size(191, 26);
            this.manageStationsToolStripMenuItem.Text = "&Manage Stations";
            // 
            // trainsToolStripMenuItem
            // 
            this.trainsToolStripMenuItem.Name = "trainsToolStripMenuItem";
            this.trainsToolStripMenuItem.Size = new Size(60, 24);
            this.trainsToolStripMenuItem.Text = "&Trains";
            // 
            // schedulesToolStripMenuItem
            // 
            this.schedulesToolStripMenuItem.Name = "schedulesToolStripMenuItem";
            this.schedulesToolStripMenuItem.Size = new Size(88, 24);
            this.schedulesToolStripMenuItem.Text = "S&chedules";
            // 
            // displayBoardsToolStripMenuItem
            // 
            this.displayBoardsToolStripMenuItem.Name = "displayBoardsToolStripMenuItem";
            this.displayBoardsToolStripMenuItem.Size = new Size(118, 24);
            this.displayBoardsToolStripMenuItem.Text = "&Display Boards";
            // 
            // helpToolStripMenuItem
            // 
            this.helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
            this.aboutToolStripMenuItem});
            this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            this.helpToolStripMenuItem.Size = new Size(55, 24);
            this.helpToolStripMenuItem.Text = "&Help";
            // 
            // aboutToolStripMenuItem
            // 
            this.aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            this.aboutToolStripMenuItem.Size = new Size(133, 26);
            this.aboutToolStripMenuItem.Text = "&About";
            // 
            // toolStrip1
            // 
            this.toolStrip1.ImageScalingSize = new Size(20, 20);
            this.toolStrip1.Items.AddRange(new ToolStripItem[] {
            this.addStationButton,
            this.toolStripSeparator1,
            this.refreshButton});
            this.toolStrip1.Location = new Point(0, 28);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new Size(1200, 27);
            this.toolStrip1.TabIndex = 1;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // addStationButton
            // 
            this.addStationButton.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.addStationButton.Name = "addStationButton";
            this.addStationButton.Size = new Size(87, 24);
            this.addStationButton.Text = "Add Station";
            this.addStationButton.Click += new EventHandler(this.addStationButton_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new Size(6, 27);
            // 
            // refreshButton
            // 
            this.refreshButton.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.refreshButton.Name = "refreshButton";
            this.refreshButton.Size = new Size(60, 24);
            this.refreshButton.Text = "Refresh";
            this.refreshButton.Click += new EventHandler(this.refreshButton_Click);
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new Size(20, 20);
            this.statusStrip1.Items.AddRange(new ToolStripItem[] {
            this.statusLabel});
            this.statusStrip1.Location = new Point(0, 728);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new Size(1200, 22);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // statusLabel
            // 
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new Size(49, 20);
            this.statusLabel.Text = "Ready";
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = DockStyle.Fill;
            this.splitContainer1.Location = new Point(0, 55);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.groupBox1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.groupBox2);
            this.splitContainer1.Size = new Size(1200, 673);
            this.splitContainer1.SplitterDistance = 500;
            this.splitContainer1.TabIndex = 3;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.stationsDataGridView);
            this.groupBox1.Dock = DockStyle.Fill;
            this.groupBox1.Location = new Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new Size(1200, 500);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Stations";
            // 
            // stationsDataGridView
            // 
            this.stationsDataGridView.AllowUserToAddRows = false;
            this.stationsDataGridView.AllowUserToDeleteRows = false;
            this.stationsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.stationsDataGridView.Dock = DockStyle.Fill;
            this.stationsDataGridView.Location = new Point(3, 23);
            this.stationsDataGridView.Name = "stationsDataGridView";
            this.stationsDataGridView.ReadOnly = true;
            this.stationsDataGridView.RowHeadersWidth = 51;
            this.stationsDataGridView.RowTemplate.Height = 29;
            this.stationsDataGridView.Size = new Size(1194, 474);
            this.stationsDataGridView.TabIndex = 0;
            this.stationsDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.stationsDataGridView_CellDoubleClick);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.infoLabel);
            this.groupBox2.Dock = DockStyle.Fill;
            this.groupBox2.Location = new Point(0, 0);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new Size(1200, 169);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Information";
            // 
            // infoLabel
            // 
            this.infoLabel.Dock = DockStyle.Fill;
            this.infoLabel.Location = new Point(3, 23);
            this.infoLabel.Name = "infoLabel";
            this.infoLabel.Size = new Size(1194, 143);
            this.infoLabel.TabIndex = 0;
            this.infoLabel.Text = "Welcome to IPIS Windows Application\r\n\r\nThis application manages railway station" +
    " information, train schedules, and display boards.\r\n\r\nDouble-click on a station" +
    " to edit its details.";
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 750);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.menuStrip1);
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "MainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "IPIS - Integrated Passenger Information System";
            this.FormClosing += new FormClosingEventHandler(this.MainForm_FormClosing);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.stationsDataGridView)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip1;
        private ToolStripMenuItem fileToolStripMenuItem;
        private ToolStripMenuItem exitToolStripMenuItem;
        private ToolStripMenuItem stationsToolStripMenuItem;
        private ToolStripMenuItem addStationToolStripMenuItem;
        private ToolStripMenuItem manageStationsToolStripMenuItem;
        private ToolStripMenuItem trainsToolStripMenuItem;
        private ToolStripMenuItem schedulesToolStripMenuItem;
        private ToolStripMenuItem displayBoardsToolStripMenuItem;
        private ToolStripMenuItem helpToolStripMenuItem;
        private ToolStripMenuItem aboutToolStripMenuItem;
        private ToolStrip toolStrip1;
        private ToolStripButton addStationButton;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripButton refreshButton;
        private StatusStrip statusStrip1;
        private ToolStripStatusLabel statusLabel;
        private SplitContainer splitContainer1;
        private GroupBox groupBox1;
        private DataGridView stationsDataGridView;
        private GroupBox groupBox2;
        private Label infoLabel;
    }
}
