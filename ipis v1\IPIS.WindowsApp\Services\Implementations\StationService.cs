using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using IPIS.WindowsApp.Data;
using IPIS.WindowsApp.Models;
using IPIS.WindowsApp.Models.Enums;
using IPIS.WindowsApp.Services.Interfaces;
using IPIS.WindowsApp.Configuration;

namespace IPIS.WindowsApp.Services.Implementations
{
    /// <summary>
    /// Service implementation for managing railway stations
    /// Provides CRUD operations and business logic for station management
    /// </summary>
    public class StationService : IStationService
    {
        private readonly IPISDbContext _context;
        private readonly ILogger<StationService> _logger;

        /// <summary>
        /// Initializes a new instance of the StationService
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="logger">Logger instance</param>
        public StationService(IPISDbContext context, ILogger<StationService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets all stations from the database
        /// </summary>
        /// <returns>Collection of all stations</returns>
        public async Task<IEnumerable<Station>> GetAllStationsAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all stations");
                return await _context.Stations
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all stations");
                throw;
            }
        }

        /// <summary>
        /// Gets a station by its unique identifier
        /// </summary>
        /// <param name="id">Station ID</param>
        /// <returns>Station if found, null otherwise</returns>
        public async Task<Station?> GetStationByIdAsync(int id)
        {
            try
            {
                _logger.LogDebug("Retrieving station with ID: {StationId}", id);
                return await _context.Stations
                    .FirstOrDefaultAsync(s => s.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving station with ID: {StationId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets a station by its code
        /// </summary>
        /// <param name="code">Station code</param>
        /// <returns>Station if found, null otherwise</returns>
        public async Task<Station?> GetStationByCodeAsync(string code)
        {
            try
            {
                _logger.LogDebug("Retrieving station with code: {StationCode}", code);
                return await _context.Stations
                    .FirstOrDefaultAsync(s => s.Code == code);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving station with code: {StationCode}", code);
                throw;
            }
        }

        /// <summary>
        /// Creates a new station
        /// </summary>
        /// <param name="station">Station to create</param>
        /// <returns>Created station with assigned ID</returns>
        public async Task<Station> CreateStationAsync(Station station)
        {
            try
            {
                _logger.LogDebug("Creating new station: {StationName} ({StationCode})", station.Name, station.Code);

                // Validate station
                var validationResult = await ValidateStationAsync(station);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"Station validation failed: {string.Join(", ", validationResult.Errors)}");
                }

                // Set creation timestamp
                station.CreatedAt = DateTime.UtcNow;
                station.UpdatedAt = DateTime.UtcNow;

                _context.Stations.Add(station);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created station: {StationName} ({StationCode}) with ID: {StationId}",
                    station.Name, station.Code, station.Id);

                return station;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating station: {StationName} ({StationCode})", station.Name, station.Code);
                throw;
            }
        }

        /// <summary>
        /// Updates an existing station
        /// </summary>
        /// <param name="station">Station to update</param>
        /// <returns>Updated station</returns>
        public async Task<Station> UpdateStationAsync(Station station)
        {
            try
            {
                _logger.LogDebug("Updating station: {StationId}", station.Id);

                // Validate station
                var validationResult = await ValidateStationAsync(station);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"Station validation failed: {string.Join(", ", validationResult.Errors)}");
                }

                // Check if station exists
                var existingStation = await GetStationByIdAsync(station.Id);
                if (existingStation == null)
                {
                    throw new InvalidOperationException($"Station with ID {station.Id} not found");
                }

                // Update properties
                existingStation.Name = station.Name;
                existingStation.Code = station.Code;
                existingStation.Type = station.Type;
                existingStation.IsActive = station.IsActive;
                existingStation.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated station: {StationName} ({StationCode}) with ID: {StationId}",
                    existingStation.Name, existingStation.Code, existingStation.Id);

                return existingStation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating station: {StationId}", station.Id);
                throw;
            }
        }

        /// <summary>
        /// Deletes a station by ID
        /// </summary>
        /// <param name="id">Station ID to delete</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        public async Task<bool> DeleteStationAsync(int id)
        {
            try
            {
                _logger.LogDebug("Deleting station with ID: {StationId}", id);

                var station = await GetStationByIdAsync(id);
                if (station == null)
                {
                    _logger.LogWarning("Station with ID {StationId} not found for deletion", id);
                    return false;
                }

                _context.Stations.Remove(station);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted station: {StationName} ({StationCode}) with ID: {StationId}",
                    station.Name, station.Code, station.Id);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting station with ID: {StationId}", id);
                throw;
            }
        }

        /// <summary>
        /// Checks if a station code already exists
        /// </summary>
        /// <param name="code">Station code to check</param>
        /// <param name="excludeId">Station ID to exclude from check (for updates)</param>
        /// <returns>True if code exists, false otherwise</returns>
        public async Task<bool> StationCodeExistsAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _context.Stations.Where(s => s.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if station code exists: {StationCode}", code);
                throw;
            }
        }

        /// <summary>
        /// Searches stations by name or code
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>Collection of matching stations</returns>
        public async Task<IEnumerable<Station>> SearchStationsAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return await GetAllStationsAsync();
                }

                _logger.LogDebug("Searching stations with term: {SearchTerm}", searchTerm);

                var lowerSearchTerm = searchTerm.ToLower();
                return await _context.Stations
                    .Where(s => s.Name.ToLower().Contains(lowerSearchTerm) ||
                               s.Code.ToLower().Contains(lowerSearchTerm))
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching stations with term: {SearchTerm}", searchTerm);
                throw;
            }
        }

        /// <summary>
        /// Gets stations by type
        /// </summary>
        /// <param name="stationType">Type of station</param>
        /// <returns>Collection of stations of the specified type</returns>
        public async Task<IEnumerable<Station>> GetStationsByTypeAsync(StationType stationType)
        {
            try
            {
                _logger.LogDebug("Retrieving stations of type: {StationType}", stationType);
                return await _context.Stations
                    .Where(s => s.Type == stationType)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving stations of type: {StationType}", stationType);
                throw;
            }
        }

        /// <summary>
        /// Validates station data
        /// </summary>
        /// <param name="station">Station to validate</param>
        /// <returns>Validation result with any errors</returns>
        public async Task<StationValidationResult> ValidateStationAsync(Station station)
        {
            var result = new StationValidationResult { IsValid = true };

            try
            {
                // Check required fields
                if (string.IsNullOrWhiteSpace(station.Name))
                {
                    result.Errors.Add("Station name is required");
                }

                if (string.IsNullOrWhiteSpace(station.Code))
                {
                    result.Errors.Add("Station code is required");
                }

                // Check code format (should be uppercase and alphanumeric)
                if (!string.IsNullOrWhiteSpace(station.Code))
                {
                    if (station.Code.Length < 2 || station.Code.Length > 10)
                    {
                        result.Errors.Add("Station code must be between 2 and 10 characters");
                    }

                    if (!station.Code.All(char.IsLetterOrDigit))
                    {
                        result.Errors.Add("Station code must contain only letters and numbers");
                    }
                }

                // Check for duplicate code
                if (!string.IsNullOrWhiteSpace(station.Code))
                {
                    var codeExists = await StationCodeExistsAsync(station.Code, station.Id == 0 ? null : station.Id);
                    if (codeExists)
                    {
                        result.Errors.Add($"Station code '{station.Code}' already exists");
                    }
                }

                result.IsValid = result.Errors.Count == 0;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating station");
                result.IsValid = false;
                result.Errors.Add("An error occurred during validation");
                return result;
            }
        }

        /// <summary>
        /// Gets the total count of stations
        /// </summary>
        /// <returns>Total number of stations</returns>
        public async Task<int> GetStationCountAsync()
        {
            try
            {
                return await _context.Stations.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting station count");
                throw;
            }
        }

        /// <summary>
        /// Gets stations with pagination
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paginated result of stations</returns>
        public async Task<PaginatedResult<Station>> GetStationsPagedAsync(int pageNumber, int pageSize)
        {
            try
            {
                _logger.LogDebug("Retrieving stations page {PageNumber} with size {PageSize}", pageNumber, pageSize);

                var totalCount = await GetStationCountAsync();
                var skip = (pageNumber - 1) * pageSize;

                var stations = await _context.Stations
                    .OrderBy(s => s.Name)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Station>
                {
                    Items = stations,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalCount = totalCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving stations page {PageNumber} with size {PageSize}", pageNumber, pageSize);
                throw;
            }
        }

        /// <summary>
        /// Gets all active stations
        /// </summary>
        /// <returns>Collection of active stations</returns>
        public async Task<IEnumerable<Station>> GetActiveStationsAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all active stations");
                return await _context.Stations
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active stations");
                throw;
            }
        }
    }
}
