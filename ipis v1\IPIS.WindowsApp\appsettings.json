{"ConnectionStrings": {"DefaultConnection": "Data Source=ipis.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/ipis-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7}}]}, "Application": {"Name": "IPIS Windows Application", "Version": "1.0.0", "DatabaseAutoMigrate": true}}