{"ConnectionStrings": {"DefaultConnection": "Data Source=ipis.db", "BackupConnection": "Data Source=backup/ipis_backup.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning", "IPIS.Communication": "Debug", "IPIS.Voice": "Information", "IPIS.Advertisement": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/ipis-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Application": {"Name": "IPIS Windows Application", "Version": "1.0.0", "DatabaseAutoMigrate": true, "BackupEnabled": true, "BackupIntervalHours": 24, "SessionTimeoutMinutes": 30, "MaxLoginAttempts": 5, "PasswordMinLength": 8, "EnableAuditLogging": true, "DataRetentionDays": 365}, "Communication": {"SerialPort": {"PortName": "COM1", "BaudRate": 9600, "DataBits": 8, "Parity": "None", "StopBits": "One", "Handshake": "None", "ReadTimeout": 5000, "WriteTimeout": 5000}, "Network": {"DefaultTimeout": 5000, "RetryAttempts": 3, "RetryDelay": 1000, "HeartbeatInterval": 30, "MaxConcurrentConnections": 50}, "DisplayBoards": {"AGDB": {"DefaultPort": 8001, "MaxMessageLength": 240, "RefreshInterval": 30}, "CGDB": {"DefaultPort": 8002, "MaxMessageLength": 200, "RefreshInterval": 15}, "MLDB": {"DefaultPort": 8003, "MaxLines": 4, "MaxMessageLength": 160, "RefreshInterval": 20}, "PDB": {"DefaultPort": 8004, "MaxMessageLength": 120, "RefreshInterval": 10}, "PDCH": {"DefaultPort": 8005, "MaxDevices": 16, "RefreshInterval": 5}}}, "Voice": {"DefaultLanguage": "en", "SupportedLanguages": ["en", "hi", "regional"], "VoiceFilePath": "Resources/Voice", "MaxAnnouncementLength": 300, "AnnouncementInterval": 5, "VolumeLevel": 80, "EnableMultiLanguage": true, "AnnouncementTypes": {"TrainArrival": true, "TrainDeparture": true, "PlatformChange": true, "SpecialMessages": true, "Advertisements": true}}, "Advertisement": {"Enabled": true, "ContentPath": "Resources/Advertisement", "DefaultDuration": 30, "MaxFileSize": 10485760, "SupportedFormats": [".mp3", ".wav", ".mp4", ".avi"], "PlaybackInterval": 300, "PriorityLevels": 5, "EnableScheduling": true, "RevenueTracking": true}, "Display": {"DefaultRefreshInterval": 30, "MaxMessagesPerBoard": 10, "DefaultLanguage": "en", "SupportedLanguages": ["en", "hi", "regional"], "FontSettings": {"English": {"FontName": "<PERSON><PERSON>", "FontSize": 12, "FontStyle": "Regular"}, "Hindi": {"FontName": "Mangal", "FontSize": 12, "FontStyle": "Regular"}, "Regional": {"FontName": "<PERSON><PERSON><PERSON>", "FontSize": 12, "FontStyle": "Regular"}}, "Intensity": {"DayIntensity": 80, "NightIntensity": 40, "DayStartTime": "06:00", "NightStartTime": "20:00"}}, "ExternalAPIs": {"RailwayAPI": {"BaseUrl": "https://api.railway.gov.in", "ApiKey": "", "Timeout": 30000, "EnableSync": true, "SyncInterval": 300, "RetryAttempts": 3}, "WeatherAPI": {"BaseUrl": "https://api.openweathermap.org", "ApiKey": "", "Timeout": 10000, "EnableWeatherDisplay": false}}, "Security": {"EncryptionKey": "IPIS-2024-SECURE-KEY-CHANGE-IN-PRODUCTION", "TokenExpiration": 480, "RequireHttps": false, "EnableAuditLog": true, "MaxSessionsPerUser": 3, "PasswordComplexity": {"RequireUppercase": true, "RequireLowercase": true, "RequireNumbers": true, "RequireSpecialChars": false, "MinLength": 8}}, "Performance": {"CacheEnabled": true, "CacheExpirationMinutes": 60, "MaxCacheSize": 100, "DatabaseConnectionPoolSize": 10, "EnablePerformanceCounters": true, "LogSlowQueries": true, "SlowQueryThreshold": 1000}, "Maintenance": {"AutoBackup": true, "BackupRetentionDays": 30, "LogCleanupDays": 90, "DatabaseOptimizationInterval": 7, "HealthCheckInterval": 60, "EnableMaintenanceMode": false}}