// Decompiled with JetBrains decompiler
// Type: ipis.struct_file
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Data;
using System.Data.OleDb;
using System.Diagnostics;
using System.IO;

namespace ipis
{

public class struct_file
{
  [DebuggerNonUserCode]
  public struct_file()
  {
  }

  public static void write_online_train_details_struct_file()
  {
    int index1 = 0;
    string empty = string.Empty;
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    if (frmMainFormIPIS.read_online_trains)
      return;
    frmMainFormIPIS.write_online_trains = true;
    try
    {
      if (connection_Database.con13.State == ConnectionState.Closed)
        connection_Database.con13.Open();
      OleDbTransaction oleDbTransaction = connection_Database.con13.BeginTransaction();
      new OleDbCommand()
      {
        CommandText = "DELETE FROM tbl_online_train_data",
        Connection = connection_Database.con13,
        Transaction = oleDbTransaction
      }.ExecuteNonQuery();
      while (index1 < frmMainFormIPIS.online_train_cnt)
      {
        int index2 = 0;
        string str = frmMainFormIPIS.online_train_data[index1].cgs_array_values[index2];
        int index3 = 1;
        while (index3 < 26)
        {
          str = "{str},{frmMainFormIPIS.online_train_data[index1].cgs_array_values[index3]}";
          checked { ++index3; }
        }
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[index1].train_status, "Train does not run on this day", false) != 0)
        {
          oleDbCommand.CommandText = " (insert into tbl_online_train_data(Trainno,TrainName,TrainNameReg,TrainNameHindi,Ad,AnnounceChk,DisplayChk,CgsChk,Sno,SchArr,SchDep,TrainStatus,Late,ExpArr,ExpDep,PfNo,CoachInfo,station_pos,CgsArrayModified) values ('{frmMainFormIPIS.online_train_data[index1].train_no}', '{frmMainFormIPIS.online_train_data[index1].train_name} ', '{frmMainFormIPIS.online_train_data[index1].train_name_reg} ' ,'{frmMainFormIPIS.online_train_data[index1].train_name_hin}', '{frmMainFormIPIS.online_train_data[index1].AD} ',  {Conversions.ToString(frmMainFormIPIS.online_train_data[index1].announce_checked)} , {Conversions.ToString(frmMainFormIPIS.online_train_data[index1].display_checked)} , {Conversions.ToString(frmMainFormIPIS.online_train_data[index1].cgs_checked)}, {Conversions.ToString(frmMainFormIPIS.online_train_data[index1].sno)}  , '{frmMainFormIPIS.online_train_data[index1].sch_arr_time} ','{frmMainFormIPIS.online_train_data[index1].sch_dep_time}','{frmMainFormIPIS.online_train_data[index1].train_status}','{frmMainFormIPIS.online_train_data[index1].late}','{frmMainFormIPIS.online_train_data[index1].exp_arr_time}','{frmMainFormIPIS.online_train_data[index1].exp_dep_time}','{frmMainFormIPIS.online_train_data[index1].pfno}','{str}','{frmMainFormIPIS.online_train_data[index1].station_pos}', {Conversions.ToString(frmMainFormIPIS.online_train_data[index1].cgs_array_modified)}) )";
          oleDbCommand.Connection = connection_Database.con13;
          oleDbCommand.Transaction = oleDbTransaction;
          oleDbDataAdapter.InsertCommand = oleDbCommand;
          oleDbDataAdapter.InsertCommand.ExecuteNonQuery();
        }
        checked { ++index1; }
      }
      try
      {
        oleDbTransaction.Commit();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        oleDbTransaction.Rollback();
        ProjectData.ClearProjectError();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con13.State == ConnectionState.Open)
      connection_Database.con13.Close();
    frmMainFormIPIS.write_online_trains = false;
  }

  public static void read_online_train_details_struct_file()
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    if (frmMainFormIPIS.write_online_trains)
      return;
    frmMainFormIPIS.read_online_trains = true;
    try
    {
      if (connection_Database.con13.State == ConnectionState.Closed)
        connection_Database.con13.Open();
      oleDbCommand1.CommandText = " (select * from tbl_online_train_data )";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con13;
      OleDbDataReader oleDbDataReader = oleDbCommand1.ExecuteReader();
      frmMainFormIPIS.online_train_cnt = 0;
      int index1 = 0;
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          frmMainFormIPIS.online_train_data[index1].train_no = Strings.Trim(Conversions.ToString(oleDbDataReader[1]));
          frmMainFormIPIS.online_train_data[index1].train_name = Strings.Trim(Conversions.ToString(oleDbDataReader[2]));
          frmMainFormIPIS.online_train_data[index1].train_name_hin = Strings.Trim(Conversions.ToString(oleDbDataReader[3]));
          frmMainFormIPIS.online_train_data[index1].train_name_reg = Strings.Trim(Conversions.ToString(oleDbDataReader[4]));
          frmMainFormIPIS.online_train_data[index1].AD = Strings.Trim(Conversions.ToString(oleDbDataReader[5]));
          frmMainFormIPIS.online_train_data[index1].announce_checked = Conversions.ToBoolean(Strings.Trim(Conversions.ToString(oleDbDataReader[6])));
          frmMainFormIPIS.online_train_data[index1].display_checked = Conversions.ToBoolean(Strings.Trim(Conversions.ToString(oleDbDataReader[7])));
          frmMainFormIPIS.online_train_data[index1].cgs_checked = Conversions.ToBoolean(Strings.Trim(Conversions.ToString(oleDbDataReader[8])));
          frmMainFormIPIS.online_train_data[index1].sno = Conversions.ToInteger(Strings.Trim(Conversions.ToString(oleDbDataReader[9])));
          frmMainFormIPIS.online_train_data[index1].sch_arr_time = Strings.Trim(Conversions.ToString(oleDbDataReader[10]));
          frmMainFormIPIS.online_train_data[index1].sch_dep_time = Strings.Trim(Conversions.ToString(oleDbDataReader[11]));
          frmMainFormIPIS.online_train_data[index1].train_status = Strings.Trim(Conversions.ToString(oleDbDataReader[12]));
          frmMainFormIPIS.online_train_data[index1].late = Strings.Trim(Conversions.ToString(oleDbDataReader[13]));
          frmMainFormIPIS.online_train_data[index1].exp_arr_time = Strings.Trim(Conversions.ToString(oleDbDataReader[14]));
          frmMainFormIPIS.online_train_data[index1].exp_dep_time = Strings.Trim(Conversions.ToString(oleDbDataReader[15]));
          frmMainFormIPIS.online_train_data[index1].pfno = Strings.Trim(Conversions.ToString(oleDbDataReader[16 /*0x10*/]));
          int index2 = 0;
          string[] strArray = Strings.Trim(Conversions.ToString(oleDbDataReader[17])).Split(',');
          while (index2 < strArray.Length)
          {
            frmMainFormIPIS.online_train_data[index1].cgs_array_values[index2] = strArray[index2];
            checked { ++index2; }
          }
          frmMainFormIPIS.online_train_data[index1].station_pos = Strings.Trim(Conversions.ToString(oleDbDataReader[18]));
          frmMainFormIPIS.online_train_data[index1].cgs_array_modified = Conversions.ToBoolean(Strings.Trim(Conversions.ToString(oleDbDataReader[19])));
          checked { ++frmMainFormIPIS.online_train_cnt; }
          checked { ++index1; }
        }
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con13.State == ConnectionState.Open)
      connection_Database.con13.Close();
    frmMainFormIPIS.read_online_trains = false;
  }

  public static void read_Train_log_file(string filepath, ref int cnt)
  {
    int num1 = 0;
    string[] strArray1 = new string[11];
    try
    {
      if (!File.Exists(filepath))
        return;
      FileStream fileStream1 = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader1 = new StreamReader((Stream) fileStream1);
      num1 = 0;
      int num2 = 0;
      while (streamReader1.Peek() >= 0)
      {
        streamReader1.ReadLine();
        checked { ++num2; }
      }
      frmMainFormIPIS.train_log_file = new frmMainFormIPIS.train_log_struct[checked (num2 + 1)];
      streamReader1.Close();
      fileStream1.Close();
      FileStream fileStream2 = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader2 = new StreamReader((Stream) fileStream2);
      int index = 0;
      while (streamReader2.Peek() >= 0)
      {
        string[] strArray2 = streamReader2.ReadLine().Split(',');
        frmMainFormIPIS.train_log_file[index].log_time = strArray2[0];
        frmMainFormIPIS.train_log_file[index].train_no = strArray2[1];
        frmMainFormIPIS.train_log_file[index].train_name = strArray2[2];
        frmMainFormIPIS.train_log_file[index].AD = strArray2[3];
        frmMainFormIPIS.train_log_file[index].ad_time = strArray2[4];
        frmMainFormIPIS.train_log_file[index].status = strArray2[5];
        frmMainFormIPIS.train_log_file[index].pfno = strArray2[6];
        frmMainFormIPIS.train_log_file[index].display_board = strArray2[7];
        frmMainFormIPIS.train_log_file[index].addr = Conversions.ToByte(strArray2[8]);
        frmMainFormIPIS.train_log_file[index].msg = strArray2[9];
        checked { ++index; }
      }
      cnt = index;
      fileStream2.Close();
      streamReader2.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void read_cgs_log_file(string path, ref int cnt)
  {
    string[] strArray1 = new string[11];
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream1 = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader1 = new StreamReader((Stream) fileStream1);
      int index = 0;
      int num = 0;
      while (streamReader1.Peek() >= 0)
      {
        streamReader1.ReadLine();
        checked { ++num; }
      }
      frmMainFormIPIS.cgs_log_file = new frmMainFormIPIS.cgs_log_struct[checked (num + 1)];
      streamReader1.Close();
      fileStream1.Close();
      FileStream fileStream2 = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader2 = new StreamReader((Stream) fileStream2);
      while (streamReader2.Peek() >= 0)
      {
        string[] strArray2 = streamReader2.ReadLine().Split(',');
        frmMainFormIPIS.cgs_log_file[index].log_time = strArray2[0];
        frmMainFormIPIS.cgs_log_file[index].train_no = strArray2[1];
        frmMainFormIPIS.cgs_log_file[index].train_name = strArray2[2];
        frmMainFormIPIS.cgs_log_file[index].AD = strArray2[3];
        frmMainFormIPIS.cgs_log_file[index].ad_time = strArray2[4];
        frmMainFormIPIS.cgs_log_file[index].status = strArray2[5];
        frmMainFormIPIS.cgs_log_file[index].pfno = strArray2[6];
        frmMainFormIPIS.cgs_log_file[index].display_board = strArray2[7];
        frmMainFormIPIS.cgs_log_file[index].addr = Conversions.ToByte(strArray2[8]);
        frmMainFormIPIS.cgs_log_file[index].msg = strArray2[9];
        frmMainFormIPIS.cgs_log_file[index].cgs_array = strArray2[10];
        checked { ++index; }
      }
      cnt = index;
      fileStream2.Close();
      streamReader2.Close();
      streamReader1.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void read_train_login_details_file(string path, ref int cnt)
  {
    int num = 0;
    string[] strArray1 = File.ReadAllLines(path);
    string[] strArray2 = new string[checked (strArray1.Length + 1)];
    string[] strArray3 = new string[5];
    int index1 = 0;
    string[] strArray4 = strArray1;
    int index2 = 0;
    while (index2 < strArray4.Length)
    {
      string str = strArray4[index2];
      strArray2[index1] = str;
      checked { ++index1; }
      checked { ++index2; }
    }
    cnt = index1;
    int index3 = 0;
    num = 0;
    while (index3 < cnt)
    {
      string[] strArray5 = strArray2[index3].Split(',');
      frmMainFormIPIS.user_login_data[index3].user_id = strArray5[0];
      frmMainFormIPIS.user_login_data[index3].user_name = strArray5[1];
      frmMainFormIPIS.user_login_data[index3].entry_time = Conversions.ToDate(strArray5[2]);
      frmMainFormIPIS.user_login_data[index3].exit_time = Conversions.ToDate(strArray5[3]);
      checked { ++index3; }
    }
    cnt = index3;
  }

  public static void write_file_pdb_mldb_def_msg()
  {
    int num1 = 0;
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    if (frmMainFormIPIS.read_file_pdb_mldb_msg)
      return;
    frmMainFormIPIS.write_file_pdb_mldb_msg = true;
    try
    {
      if (connection_Database.con13.State == ConnectionState.Closed)
        connection_Database.con13.Open();
      OleDbTransaction oleDbTransaction = connection_Database.con13.BeginTransaction();
      oleDbCommand2.CommandText = "DELETE FROM tbl_pdb_mldb_def_msg";
      oleDbCommand2.Connection = connection_Database.con13;
      oleDbCommand2.Transaction = oleDbTransaction;
      num1 = oleDbCommand2.ExecuteNonQuery();
      int index = 0;
      while (index < frmMainFormIPIS.addmsg_count)
      {
        oleDbCommand1.CommandText = " (insert into tbl_pdb_mldb_def_msg(PdbMldbCount,ChkArray,mldb_name,mldb_addr,pdb_pfno ) values ({Conversions.ToString(frmMainFormIPIS.addmsg_count)},{Conversions.ToString(frmMainFormIPIS.chk_array[index])},'{taddb_msg.def_mldb_name}',{Conversions.ToString(taddb_msg.def_mldb_addr)},'{frmMainFormIPIS.pdb_msg_platform_no}') )";
        oleDbCommand1.Connection = connection_Database.con13;
        oleDbCommand1.Transaction = oleDbTransaction;
        oleDbDataAdapter.InsertCommand = oleDbCommand1;
        oleDbDataAdapter.InsertCommand.ExecuteNonQuery();
        checked { ++index; }
      }
      try
      {
        oleDbTransaction.Commit();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        oleDbTransaction.Rollback();
        ProjectData.ClearProjectError();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con13.State == ConnectionState.Open)
      connection_Database.con13.Close();
    frmMainFormIPIS.write_file_pdb_mldb_msg = false;
  }

  public static void read_file_pdb_mldb_def_msg()
  {
    if (frmMainFormIPIS.write_file_pdb_mldb_msg)
      return;
    frmMainFormIPIS.read_file_pdb_mldb_msg = true;
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    try
    {
      if (connection_Database.con13.State == ConnectionState.Closed)
        connection_Database.con13.Open();
      oleDbCommand1.CommandText = "(select * from tbl_pdb_mldb_def_msg )";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con13;
      OleDbDataReader oleDbDataReader = oleDbCommand1.ExecuteReader();
      int index = 0;
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          Conversions.ToInteger(oleDbDataReader[1]);
          frmMainFormIPIS.chk_array[index] = Conversions.ToByte(oleDbDataReader[2]);
          taddb_msg.def_mldb_name = Strings.Trim(Conversions.ToString(oleDbDataReader[3]));
          taddb_msg.def_mldb_addr = Conversions.ToByte(oleDbDataReader[4]);
          frmMainFormIPIS.pdb_msg_platform_no = Conversions.ToString(oleDbDataReader[5]);
          checked { ++index; }
        }
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con13.State == ConnectionState.Open)
      connection_Database.con13.Close();
    frmMainFormIPIS.read_file_pdb_mldb_msg = false;
  }
}

}