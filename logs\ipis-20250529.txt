2025-05-29 00:28:31.722 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 00:28:31.777 +05:30 [FTL] IPIS Windows Application terminated unexpectedly
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'E:\_work\Rail\rail\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at IPIS.WindowsApp.Program.BuildConfiguration() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Program.cs:line 73
   at IPIS.WindowsApp.Program.Main() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Program.cs:line 41
2025-05-29 00:29:49.650 +05:30 [INF] Starting IPIS Windows Application
2025-05-29 00:29:49.702 +05:30 [FTL] IPIS Windows Application terminated unexpectedly
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'E:\_work\Rail\rail\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at IPIS.WindowsApp.Program.BuildConfiguration() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Program.cs:line 73
   at IPIS.WindowsApp.Program.Main() in E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\Program.cs:line 41
