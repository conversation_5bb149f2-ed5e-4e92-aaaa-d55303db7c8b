// Decompiled with JetBrains decompiler
// Type: ipis.frmAbout
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmAbout : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("RichTextBox1")]
  private RichTextBox _RichTextBox1;
  [AccessedThroughProperty("Button1")]
  private Button _Button1;

  [DebuggerNonUserCode]
  static frmAbout()
  {
  }

  [DebuggerNonUserCode]
  public frmAbout()
  {
    frmAbout.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmAbout.__ENCList)
    {
      if (frmAbout.__ENCList.Count == frmAbout.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmAbout.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmAbout.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmAbout.__ENCList[index1] = frmAbout.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmAbout.__ENCList.RemoveRange(index1, checked (frmAbout.__ENCList.Count - index1));
        frmAbout.__ENCList.Capacity = frmAbout.__ENCList.Count;
      }
      frmAbout.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.RichTextBox1 = new RichTextBox();
    this.Button1 = new Button();
    this.SuspendLayout();
    this.RichTextBox1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RichTextBox richTextBox1_1 = this.RichTextBox1;
    Point point1 = new Point(0, 2);
    Point point2 = point1;
    richTextBox1_1.Location = point2;
    this.RichTextBox1.Name = "RichTextBox1";
    RichTextBox richTextBox1_2 = this.RichTextBox1;
    Size size1 = new Size(301, 142);
    Size size2 = size1;
    richTextBox1_2.Size = size2;
    this.RichTextBox1.TabIndex = 0;
    this.RichTextBox1.Text = "Integrated Passenger Information Systems\n\n                 Version No: 1.0.2009\n\n                    Surya Electronics\n\n                    All rights reserved";
    this.Button1.DialogResult = DialogResult.Cancel;
    this.Button1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button button1_1 = this.Button1;
    point1 = new Point(118, 150);
    Point point3 = point1;
    button1_1.Location = point3;
    this.Button1.Name = "Button1";
    Button button1_2 = this.Button1;
    size1 = new Size(56, 23);
    Size size3 = size1;
    button1_2.Size = size3;
    this.Button1.TabIndex = 1;
    this.Button1.Text = "Ok";
    this.Button1.UseVisualStyleBackColor = true;
    this.AcceptButton = (IButtonControl) this.Button1;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.Button1;
    size1 = new Size(297, 183);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Button1);
    this.Controls.Add((Control) this.RichTextBox1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmAbout";
    this.Text = "About";
    this.ResumeLayout(false);
  }

  internal virtual RichTextBox RichTextBox1
  {
    [DebuggerNonUserCode] get { return this._RichTextBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._RichTextBox1 = value;
    }
  }

  internal virtual Button Button1
  {
    [DebuggerNonUserCode] get { return this._Button1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button1_Click);
      if (this._Button1 != null)
        this._Button1.Click -= eventHandler;
      this._Button1 = value;
      if (this._Button1 == null)
        return;
      this._Button1.Click += eventHandler;
    }
  }

  private void Button1_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}