using IPIS.WindowsApp.Models;
using IPIS.WindowsApp.Models.Enums;
using IPIS.WindowsApp.Configuration;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Service interface for managing railway stations
    /// Provides CRUD operations and business logic for station management
    /// </summary>
    public interface IStationService
    {
        /// <summary>
        /// Gets all stations from the database
        /// </summary>
        /// <returns>Collection of all stations</returns>
        Task<IEnumerable<Station>> GetAllStationsAsync();

        /// <summary>
        /// Gets a station by its unique identifier
        /// </summary>
        /// <param name="id">Station ID</param>
        /// <returns>Station if found, null otherwise</returns>
        Task<Station?> GetStationByIdAsync(int id);

        /// <summary>
        /// Gets a station by its code
        /// </summary>
        /// <param name="code">Station code</param>
        /// <returns>Station if found, null otherwise</returns>
        Task<Station?> GetStationByCodeAsync(string code);

        /// <summary>
        /// Creates a new station
        /// </summary>
        /// <param name="station">Station to create</param>
        /// <returns>Created station with assigned ID</returns>
        Task<Station> CreateStationAsync(Station station);

        /// <summary>
        /// Updates an existing station
        /// </summary>
        /// <param name="station">Station to update</param>
        /// <returns>Updated station</returns>
        Task<Station> UpdateStationAsync(Station station);

        /// <summary>
        /// Deletes a station by ID
        /// </summary>
        /// <param name="id">Station ID to delete</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        Task<bool> DeleteStationAsync(int id);

        /// <summary>
        /// Checks if a station code already exists
        /// </summary>
        /// <param name="code">Station code to check</param>
        /// <param name="excludeId">Station ID to exclude from check (for updates)</param>
        /// <returns>True if code exists, false otherwise</returns>
        Task<bool> StationCodeExistsAsync(string code, int? excludeId = null);

        /// <summary>
        /// Searches stations by name or code
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>Collection of matching stations</returns>
        Task<IEnumerable<Station>> SearchStationsAsync(string searchTerm);

        /// <summary>
        /// Gets stations by type
        /// </summary>
        /// <param name="stationType">Type of station</param>
        /// <returns>Collection of stations of the specified type</returns>
        Task<IEnumerable<Station>> GetStationsByTypeAsync(StationType stationType);

        /// <summary>
        /// Validates station data
        /// </summary>
        /// <param name="station">Station to validate</param>
        /// <returns>Validation result with any errors</returns>
        Task<StationValidationResult> ValidateStationAsync(Station station);

        /// <summary>
        /// Gets the total count of stations
        /// </summary>
        /// <returns>Total number of stations</returns>
        Task<int> GetStationCountAsync();

        /// <summary>
        /// Gets stations with pagination
        /// </summary>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paginated result of stations</returns>
        Task<PaginatedResult<Station>> GetStationsPagedAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Gets all active stations
        /// </summary>
        /// <returns>Collection of active stations</returns>
        Task<IEnumerable<Station>> GetActiveStationsAsync();
    }

    /// <summary>
    /// Validation result for station operations
    /// </summary>
    public class StationValidationResult
    {
        /// <summary>
        /// Gets or sets whether the validation was successful
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Gets or sets the validation error messages
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the validation warning messages
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Paginated result for station queries
    /// </summary>
    /// <typeparam name="T">Type of items in the result</typeparam>
    public class PaginatedResult<T>
    {
        /// <summary>
        /// Gets or sets the items in the current page
        /// </summary>
        public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();

        /// <summary>
        /// Gets or sets the current page number
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Gets or sets the page size
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Gets or sets the total number of items
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Gets the total number of pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// Gets whether there is a previous page
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// Gets whether there is a next page
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
