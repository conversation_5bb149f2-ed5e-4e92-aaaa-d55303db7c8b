# PowerShell script to fix all remaining ref parameter issues

Write-Host "=== FIXING ALL REF PARAMETER ISSUES ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix agdb_byte_construct method calls
    $content = $content -replace '\.name_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.name_byte($1, ref $2, ref $3)'
    $content = $content -replace '\.platform_number_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.platform_number_byte($1, ref $2, ref $3)'
    $content = $content -replace '\.time_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.time_byte($1, ref $2, ref $3)'
    $content = $content -replace '\.status_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.status_byte($1, ref $2, ref $3)'
    
    # Fix voice_xml_files method calls - remove ref from XmlNodeList parameters
    $content = $content -replace '\.platform_no_voice\(([^,]+),\s*ref\s+([^,]+),\s*([^)]+)\)', '.platform_no_voice($1, $2, $3)'
    $content = $content -replace '\.train_status_voice\(([^,]+),\s*ref\s+([^)]+)\)', '.train_status_voice($1, $2)'
    $content = $content -replace '\.reg_time_hour_voice\(([^,]+),\s*ref\s+([^,]+),\s*([^,]+),\s*([^)]+)\)', '.reg_time_hour_voice($1, $2, $3, $4)'
    $content = $content -replace '\.reg_arr_dep_time_voice\(([^,]+),\s*ref\s+([^,]+),\s*([^,]+),\s*([^)]+)\)', '.reg_arr_dep_time_voice($1, $2, $3, $4)'
    $content = $content -replace '\.hindi_arr_dep_time_voice\(([^,]+),\s*ref\s+([^,]+),\s*([^,]+),\s*([^)]+)\)', '.hindi_arr_dep_time_voice($1, $2, $3, $4)'
    $content = $content -replace '\.station_name_voice\(([^,]+),\s*ref\s+([^)]+)\)', '.station_name_voice($1, $2)'
    
    # Fix invalid ref expressions that cause syntax errors
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;\s*\n', "`n    $1;`n"
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*,', ' $1,'
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\)', ' $1)'
    
    # Fix array access with ref
    $content = $content -replace 'ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\[([^\]]+)\]', '$1[$2]'
    
    # Fix method calls that should not have ref on certain parameters
    $content = $content -replace '\.Write\(ref\s+([^,)]+)\)', '.Write($1)'
    $content = $content -replace '\.WriteLine\(ref\s+([^,)]+)\)', '.WriteLine($1)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed ref parameter issues"
    }
}

Write-Host "All ref parameter fixes complete!"
