using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models
{
    /// <summary>
    /// Represents a railway station in the IPIS system
    /// </summary>
    [Table("Stations")]
    public class Station
    {
        /// <summary>
        /// Gets or sets the unique identifier for the station
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the station code (e.g., "NYC", "BOS")
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the full name of the station
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the location description of the station
        /// </summary>
        [StringLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// Gets or sets the type of the station
        /// </summary>
        public StationType Type { get; set; } = StationType.Regular;

        /// <summary>
        /// Gets or sets whether the station is currently active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Gets or sets the date and time when the station was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gets or sets the date and time when the station was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for platforms associated with this station
        /// </summary>
        public virtual ICollection<Platform> Platforms { get; set; } = new List<Platform>();

        /// <summary>
        /// Navigation property for schedules associated with this station
        /// </summary>
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();

        /// <summary>
        /// Navigation property for display boards associated with this station
        /// </summary>
        public virtual ICollection<DisplayBoard> DisplayBoards { get; set; } = new List<DisplayBoard>();
    }
}
