using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// Data Transfer Object for platform assignment information from Railway API
    /// Manages dynamic platform allocations and changes
    /// </summary>
    public class PlatformAssignmentDto
    {
        /// <summary>
        /// Train number for platform assignment
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Station code where platform is assigned
        /// </summary>
        [Required]
        [StringLength(10)]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Assigned platform number
        /// </summary>
        [Required]
        [StringLength(10)]
        public string PlatformNumber { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when platform was assigned
        /// </summary>
        public DateTime AssignedAt { get; set; }

        /// <summary>
        /// Indicates if the assignment is confirmed
        /// </summary>
        public bool IsConfirmed { get; set; }

        /// <summary>
        /// Reason for platform assignment
        /// </summary>
        [StringLength(200)]
        public string AssignmentReason { get; set; } = string.Empty;

        /// <summary>
        /// Train name
        /// </summary>
        [StringLength(100)]
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Station name
        /// </summary>
        [StringLength(100)]
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// Previous platform number (if changed)
        /// </summary>
        [StringLength(10)]
        public string? PreviousPlatformNumber { get; set; }

        /// <summary>
        /// Scheduled arrival time
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Scheduled departure time
        /// </summary>
        public DateTime? ScheduledDeparture { get; set; }

        /// <summary>
        /// Assignment type (Automatic, Manual, Emergency)
        /// </summary>
        [StringLength(20)]
        public string AssignmentType { get; set; } = "Automatic";

        /// <summary>
        /// User who made the assignment (for manual assignments)
        /// </summary>
        [StringLength(100)]
        public string? AssignedBy { get; set; }

        /// <summary>
        /// Priority level of the assignment
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Expected duration of platform occupation in minutes
        /// </summary>
        [Range(1, 1440)]
        public int? ExpectedOccupationMinutes { get; set; }

        /// <summary>
        /// Platform capacity utilization percentage
        /// </summary>
        [Range(0, 100)]
        public int? CapacityUtilization { get; set; }

        /// <summary>
        /// Indicates if this is a temporary assignment
        /// </summary>
        public bool IsTemporary { get; set; }

        /// <summary>
        /// Expiry time for temporary assignments
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// Conflict resolution information
        /// </summary>
        [StringLength(500)]
        public string? ConflictResolution { get; set; }

        /// <summary>
        /// Additional notes about the assignment
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Additional metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Computed property: Indicates if platform was changed
        /// </summary>
        public bool IsPlatformChanged => !string.IsNullOrWhiteSpace(PreviousPlatformNumber) &&
                                        !PreviousPlatformNumber.Equals(PlatformNumber, StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Computed property: Time since assignment
        /// </summary>
        public TimeSpan TimeSinceAssignment => DateTime.UtcNow - AssignedAt;

        /// <summary>
        /// Computed property: Indicates if assignment is recent (within last hour)
        /// </summary>
        public bool IsRecentAssignment => TimeSinceAssignment.TotalHours <= 1;

        /// <summary>
        /// Computed property: Indicates if assignment has expired
        /// </summary>
        public bool IsExpired => IsTemporary && ExpiryTime.HasValue && DateTime.UtcNow > ExpiryTime.Value;

        /// <summary>
        /// Computed property: Gets the assignment status
        /// </summary>
        public string AssignmentStatus
        {
            get
            {
                if (IsExpired) return "Expired";
                if (!IsConfirmed) return "Pending";
                if (IsTemporary) return "Temporary";
                return "Confirmed";
            }
        }

        /// <summary>
        /// Computed property: Gets the priority description
        /// </summary>
        public string PriorityDescription => Priority switch
        {
            1 => "Critical",
            2 => "High",
            3 => "Medium-High",
            4 => "Medium",
            5 => "Normal",
            6 => "Medium-Low",
            7 => "Low",
            8 => "Very Low",
            9 => "Minimal",
            10 => "Background",
            _ => "Unknown"
        };

        /// <summary>
        /// Validates the DTO data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TrainNumber) &&
                   !string.IsNullOrWhiteSpace(StationCode) &&
                   !string.IsNullOrWhiteSpace(PlatformNumber) &&
                   AssignedAt != default;
        }

        /// <summary>
        /// Gets the expected occupation duration
        /// </summary>
        /// <returns>Occupation duration or calculated duration</returns>
        public TimeSpan GetExpectedOccupationDuration()
        {
            if (ExpectedOccupationMinutes.HasValue)
                return TimeSpan.FromMinutes(ExpectedOccupationMinutes.Value);

            // Calculate from arrival/departure times if available
            if (ScheduledArrival.HasValue && ScheduledDeparture.HasValue)
                return ScheduledDeparture.Value - ScheduledArrival.Value;

            // Default occupation time
            return TimeSpan.FromMinutes(10);
        }

        /// <summary>
        /// Gets a summary of the platform assignment
        /// </summary>
        /// <returns>Assignment summary string</returns>
        public string GetSummary()
        {
            var summary = $"Train {TrainNumber}";
            if (!string.IsNullOrWhiteSpace(TrainName))
                summary += $" ({TrainName})";
            
            summary += $" assigned to Platform {PlatformNumber} at {StationCode}";
            
            if (!string.IsNullOrWhiteSpace(StationName))
                summary += $" ({StationName})";
            
            if (IsPlatformChanged)
                summary += $" (changed from Platform {PreviousPlatformNumber})";
            
            if (!IsConfirmed)
                summary += " [PENDING]";
            else if (IsTemporary)
                summary += " [TEMPORARY]";
            
            return summary;
        }

        /// <summary>
        /// Gets detailed assignment information
        /// </summary>
        /// <returns>Detailed assignment report</returns>
        public string GetDetailedInfo()
        {
            var info = GetSummary();
            info += $"\nAssigned: {AssignedAt:yyyy-MM-dd HH:mm:ss}";
            info += $"\nType: {AssignmentType}";
            info += $"\nPriority: {PriorityDescription}";
            info += $"\nStatus: {AssignmentStatus}";
            
            if (!string.IsNullOrWhiteSpace(AssignmentReason))
                info += $"\nReason: {AssignmentReason}";
            
            if (!string.IsNullOrWhiteSpace(AssignedBy))
                info += $"\nAssigned By: {AssignedBy}";
            
            if (ScheduledArrival.HasValue)
                info += $"\nScheduled Arrival: {ScheduledArrival:HH:mm}";
            
            if (ScheduledDeparture.HasValue)
                info += $"\nScheduled Departure: {ScheduledDeparture:HH:mm}";
            
            if (IsTemporary && ExpiryTime.HasValue)
                info += $"\nExpires: {ExpiryTime:yyyy-MM-dd HH:mm:ss}";
            
            return info;
        }

        /// <summary>
        /// Returns a string representation of the platform assignment
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }

        /// <summary>
        /// Creates a copy of the DTO
        /// </summary>
        /// <returns>Cloned DTO</returns>
        public PlatformAssignmentDto Clone()
        {
            return new PlatformAssignmentDto
            {
                TrainNumber = TrainNumber,
                StationCode = StationCode,
                PlatformNumber = PlatformNumber,
                AssignedAt = AssignedAt,
                IsConfirmed = IsConfirmed,
                AssignmentReason = AssignmentReason,
                TrainName = TrainName,
                StationName = StationName,
                PreviousPlatformNumber = PreviousPlatformNumber,
                ScheduledArrival = ScheduledArrival,
                ScheduledDeparture = ScheduledDeparture,
                AssignmentType = AssignmentType,
                AssignedBy = AssignedBy,
                Priority = Priority,
                ExpectedOccupationMinutes = ExpectedOccupationMinutes,
                CapacityUtilization = CapacityUtilization,
                IsTemporary = IsTemporary,
                ExpiryTime = ExpiryTime,
                ConflictResolution = ConflictResolution,
                Notes = Notes,
                Metadata = new Dictionary<string, object>(Metadata)
            };
        }
    }

    /// <summary>
    /// Enumeration for platform assignment types
    /// </summary>
    public enum PlatformAssignmentType
    {
        Automatic,
        Manual,
        Emergency,
        Temporary,
        Override
    }
}
