// Decompiled with JetBrains decompiler
// Type: ipis.frmNetworkMLDB
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmNetworkMLDB : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("txtMldbMsgSwDly")]
  private TextBox _txtMldbMsgSwDly;
  [AccessedThroughProperty("lblMsgSwDly")]
  private Label _lblMsgSwDly;
  [AccessedThroughProperty("cmbMldbType")]
  private ComboBox _cmbMldbType;
  [AccessedThroughProperty("lblType")]
  private Label _lblType;
  [AccessedThroughProperty("numMldbNoLines")]
  private NumericUpDown _numMldbNoLines;
  [AccessedThroughProperty("lblNoLines")]
  private Label _lblNoLines;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtMldbAddress")]
  private TextBox _txtMldbAddress;
  [AccessedThroughProperty("txtMldbName")]
  private TextBox _txtMldbName;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  public static string mldb_name;
  public static byte mldb_addr;
  public static byte mldb_no_lines;
  public static string mldb_type;
  public static byte mldb_msg_sw_dly;

  [DebuggerNonUserCode]
  static frmNetworkMLDB()
  {
  }

  [DebuggerNonUserCode]
  public frmNetworkMLDB()
  {
    frmNetworkMLDB.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmNetworkMLDB.__ENCList)
    {
      if (frmNetworkMLDB.__ENCList.Count == frmNetworkMLDB.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmNetworkMLDB.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmNetworkMLDB.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmNetworkMLDB.__ENCList[index1] = frmNetworkMLDB.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmNetworkMLDB.__ENCList.RemoveRange(index1, checked (frmNetworkMLDB.__ENCList.Count - index1));
        frmNetworkMLDB.__ENCList.Capacity = frmNetworkMLDB.__ENCList.Count;
      }
      frmNetworkMLDB.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.txtMldbMsgSwDly = new TextBox();
    this.lblMsgSwDly = new Label();
    this.cmbMldbType = new ComboBox();
    this.lblType = new Label();
    this.numMldbNoLines = new NumericUpDown();
    this.lblNoLines = new Label();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtMldbAddress = new TextBox();
    this.txtMldbName = new TextBox();
    this.lblAddress = new Label();
    this.lblName = new Label();
    this.numMldbNoLines.BeginInit();
    this.SuspendLayout();
    this.txtMldbMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMldbMsgSwDly1 = this.txtMldbMsgSwDly;
    Point point1 = new Point(253, 235);
    Point point2 = point1;
    txtMldbMsgSwDly1.Location = point2;
    this.txtMldbMsgSwDly.MaxLength = 2;
    this.txtMldbMsgSwDly.Name = "txtMldbMsgSwDly";
    TextBox txtMldbMsgSwDly2 = this.txtMldbMsgSwDly;
    Size size1 = new Size(45, 22);
    Size size2 = size1;
    txtMldbMsgSwDly2.Size = size2;
    this.txtMldbMsgSwDly.TabIndex = 5;
    this.txtMldbMsgSwDly.TextAlign = HorizontalAlignment.Center;
    this.lblMsgSwDly.AutoSize = true;
    this.lblMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMsgSwDly1 = this.lblMsgSwDly;
    point1 = new Point(22, 238);
    Point point3 = point1;
    lblMsgSwDly1.Location = point3;
    this.lblMsgSwDly.Name = "lblMsgSwDly";
    Label lblMsgSwDly2 = this.lblMsgSwDly;
    size1 = new Size(186, 16 /*0x10*/);
    Size size3 = size1;
    lblMsgSwDly2.Size = size3;
    this.lblMsgSwDly.TabIndex = 66;
    this.lblMsgSwDly.Text = "Message Switching Delay";
    this.cmbMldbType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbMldbType.FormattingEnabled = true;
    this.cmbMldbType.Items.AddRange(new object[3]
    {
      (object) "Arrival",
      (object) "Departure",
      (object) "Both"
    });
    ComboBox cmbMldbType1 = this.cmbMldbType;
    point1 = new Point(253, 185);
    Point point4 = point1;
    cmbMldbType1.Location = point4;
    this.cmbMldbType.Name = "cmbMldbType";
    ComboBox cmbMldbType2 = this.cmbMldbType;
    size1 = new Size(100, 24);
    Size size4 = size1;
    cmbMldbType2.Size = size4;
    this.cmbMldbType.TabIndex = 4;
    this.lblType.AutoSize = true;
    this.lblType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblType1 = this.lblType;
    point1 = new Point(164, 188);
    Point point5 = point1;
    lblType1.Location = point5;
    this.lblType.Name = "lblType";
    Label lblType2 = this.lblType;
    size1 = new Size(44, 16 /*0x10*/);
    Size size5 = size1;
    lblType2.Size = size5;
    this.lblType.TabIndex = 65;
    this.lblType.Text = "Type";
    this.numMldbNoLines.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    NumericUpDown numMldbNoLines1 = this.numMldbNoLines;
    point1 = new Point(253, 134);
    Point point6 = point1;
    numMldbNoLines1.Location = point6;
    this.numMldbNoLines.Maximum = new Decimal(new int[4]
    {
      10,
      0,
      0,
      0
    });
    this.numMldbNoLines.Name = "numMldbNoLines";
    NumericUpDown numMldbNoLines2 = this.numMldbNoLines;
    size1 = new Size(40, 22);
    Size size6 = size1;
    numMldbNoLines2.Size = size6;
    this.numMldbNoLines.TabIndex = 3;
    this.numMldbNoLines.TextAlign = HorizontalAlignment.Center;
    this.lblNoLines.AutoSize = true;
    this.lblNoLines.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblNoLines1 = this.lblNoLines;
    point1 = new Point(122, 140);
    Point point7 = point1;
    lblNoLines1.Location = point7;
    this.lblNoLines.Name = "lblNoLines";
    Label lblNoLines2 = this.lblNoLines;
    size1 = new Size(86, 16 /*0x10*/);
    Size size7 = size1;
    lblNoLines2.Size = size7;
    this.lblNoLines.TabIndex = 64 /*0x40*/;
    this.lblNoLines.Text = "No of Lines";
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(205, 299);
    Point point8 = point1;
    btnExit1.Location = point8;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(50, 25);
    Size size8 = size1;
    btnExit2.Size = size8;
    this.btnExit.TabIndex = 7;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(112 /*0x70*/, 299);
    Point point9 = point1;
    btnOk1.Location = point9;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(50, 25);
    Size size9 = size1;
    btnOk2.Size = size9;
    this.btnOk.TabIndex = 6;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtMldbAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMldbAddress1 = this.txtMldbAddress;
    point1 = new Point(253, 81);
    Point point10 = point1;
    txtMldbAddress1.Location = point10;
    this.txtMldbAddress.MaxLength = 3;
    this.txtMldbAddress.Name = "txtMldbAddress";
    TextBox txtMldbAddress2 = this.txtMldbAddress;
    size1 = new Size(100, 22);
    Size size10 = size1;
    txtMldbAddress2.Size = size10;
    this.txtMldbAddress.TabIndex = 2;
    this.txtMldbName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMldbName1 = this.txtMldbName;
    point1 = new Point(253, 24);
    Point point11 = point1;
    txtMldbName1.Location = point11;
    this.txtMldbName.MaxLength = 15;
    this.txtMldbName.Name = "txtMldbName";
    TextBox txtMldbName2 = this.txtMldbName;
    size1 = new Size(100, 22);
    Size size11 = size1;
    txtMldbName2.Size = size11;
    this.txtMldbName.TabIndex = 1;
    this.lblAddress.AutoSize = true;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(142, 87);
    Point point12 = point1;
    lblAddress1.Location = point12;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size12 = size1;
    lblAddress2.Size = size12;
    this.lblAddress.TabIndex = 63 /*0x3F*/;
    this.lblAddress.Text = "Address";
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblName1 = this.lblName;
    point1 = new Point(159, 24);
    Point point13 = point1;
    lblName1.Location = point13;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size13 = size1;
    lblName2.Size = size13;
    this.lblName.TabIndex = 62;
    this.lblName.Text = "Name";
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(376, 336);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.txtMldbMsgSwDly);
    this.Controls.Add((Control) this.lblMsgSwDly);
    this.Controls.Add((Control) this.cmbMldbType);
    this.Controls.Add((Control) this.lblType);
    this.Controls.Add((Control) this.numMldbNoLines);
    this.Controls.Add((Control) this.lblNoLines);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtMldbAddress);
    this.Controls.Add((Control) this.txtMldbName);
    this.Controls.Add((Control) this.lblAddress);
    this.Controls.Add((Control) this.lblName);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmNetworkMLDB";
    this.Text = "MLDB";
    this.numMldbNoLines.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual TextBox txtMldbMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._txtMldbMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMldbMsgSwDly = value;
    }
  }

  internal virtual Label lblMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._lblMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMsgSwDly = value;
    }
  }

  internal virtual ComboBox cmbMldbType
  {
    [DebuggerNonUserCode] get { return this._cmbMldbType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbMldbType = value;
    }
  }

  internal virtual Label lblType
  {
    [DebuggerNonUserCode] get { return this._lblType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblType = value; }
  }

  internal virtual NumericUpDown numMldbNoLines
  {
    [DebuggerNonUserCode] get { return this._numMldbNoLines; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._numMldbNoLines = value;
    }
  }

  internal virtual Label lblNoLines
  {
    [DebuggerNonUserCode] get { return this._lblNoLines; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblNoLines = value;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtMldbAddress
  {
    [DebuggerNonUserCode] get { return this._txtMldbAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMldbAddress = value;
    }
  }

  internal virtual TextBox txtMldbName
  {
    [DebuggerNonUserCode] get { return this._txtMldbName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMldbName = value;
    }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOk_Click(object sender, EventArgs e)
  {
    try
    {
      if (Operators.CompareString(this.txtMldbName.Text, "", false) == 0)
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Name", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtMldbAddress.Text, "", false) == 0)
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Address", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtMldbMsgSwDly.Text, "", false) == 0)
      {
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Message switching Delay", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.cmbMldbType.Text, "", false) == 0)
      {
        int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the MLDB Type", "Msg Box", 0, 0, 0);
      }
      else if (Decimal.Compare(this.numMldbNoLines.Value, 0M) == 0)
      {
        int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the No of Lines", "Msg Box", 0, 0, 0);
      }
      else
      {
        if (frmNetworkMDCH.hub_type)
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].system_type[(int) frmNetworkMDCH.mdch_system_num] = "MLDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_addr = Conversions.ToByte(this.txtMldbAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_name = this.txtMldbName.Text;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_type = "MLDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].mldb_no_of_lines = Convert.ToByte(this.numMldbNoLines.Value);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].mldb_type = this.cmbMldbType.Text;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].switching_time = Conversions.ToByte(this.txtMldbMsgSwDly.Text);
        }
        this.Close();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}