using Bogus;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Tests.Helpers
{
    /// <summary>
    /// Helper class for generating test data using Bogus library
    /// Provides realistic test data for comprehensive testing
    /// </summary>
    public static class TestDataHelper
    {
        private static readonly Faker _faker = new Faker();

        /// <summary>
        /// Creates a valid station with realistic data
        /// </summary>
        /// <returns>Station entity with test data</returns>
        public static Station CreateValidStation()
        {
            return new Faker<Station>()
                .RuleFor(s => s.Code, f => f.Random.String2(3, 5).ToUpper())
                .RuleFor(s => s.Name, f => f.Address.City() + " Station")
                .RuleFor(s => s.Location, f => f.Address.FullAddress())
                .RuleFor(s => s.StationType, f => f.PickRandom<StationType>())
                .RuleFor(s => s.TimeZone, f => f.Pick<PERSON>and<PERSON>("UTC", "Asia/Kolkata", "America/New_York"))
                .RuleFor(s => s.IsActive, f => f.Random.Bool(0.9f)) // 90% chance of being active
                .RuleFor(s => s.HasPassengerFacilities, f => f.Random.Bool(0.8f))
                .RuleFor(s => s.HasFreightFacilities, f => f.Random.Bool(0.3f))
                .RuleFor(s => s.Elevation, f => f.Random.Decimal(0, 3000))
                .RuleFor(s => s.Latitude, f => f.Address.Latitude())
                .RuleFor(s => s.Longitude, f => f.Address.Longitude())
                .RuleFor(s => s.ContactPhone, f => f.Phone.PhoneNumber())
                .RuleFor(s => s.ContactEmail, f => f.Internet.Email())
                .RuleFor(s => s.StationMaster, f => f.Name.FullName())
                .RuleFor(s => s.OperatingHours, f => "24/7")
                .RuleFor(s => s.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(s => s.UpdatedAt, f => f.Date.Recent(7))
                .RuleFor(s => s.CreatedBy, f => f.Internet.UserName())
                .Generate();
        }

        /// <summary>
        /// Creates a valid platform with realistic data
        /// </summary>
        /// <param name="stationId">Station ID for the platform</param>
        /// <returns>Platform entity with test data</returns>
        public static Platform CreateValidPlatform(int stationId = 1)
        {
            return new Faker<Platform>()
                .RuleFor(p => p.StationId, stationId)
                .RuleFor(p => p.PlatformNumber, f => f.Random.String2(1, 3))
                .RuleFor(p => p.PlatformName, f => f.Random.Bool(0.5f) ? f.Lorem.Word() + " Platform" : null)
                .RuleFor(p => p.PlatformType, f => f.PickRandom<PlatformType>())
                .RuleFor(p => p.TrackNumber, f => f.Random.String2(1, 2))
                .RuleFor(p => p.Length, f => f.Random.Decimal(100, 500))
                .RuleFor(p => p.Width, f => f.Random.Decimal(3, 15))
                .RuleFor(p => p.Height, f => f.Random.Decimal(0.5m, 2.0m))
                .RuleFor(p => p.IsActive, f => f.Random.Bool(0.9f))
                .RuleFor(p => p.HasCover, f => f.Random.Bool(0.6f))
                .RuleFor(p => p.IsAccessible, f => f.Random.Bool(0.4f))
                .RuleFor(p => p.HasLighting, f => f.Random.Bool(0.9f))
                .RuleFor(p => p.HasSeating, f => f.Random.Bool(0.7f))
                .RuleFor(p => p.HasWaterFacility, f => f.Random.Bool(0.5f))
                .RuleFor(p => p.HasRestroom, f => f.Random.Bool(0.3f))
                .RuleFor(p => p.MaxCoaches, f => f.Random.Int(8, 24))
                .RuleFor(p => p.Capacity, f => f.Random.Int(200, 1000))
                .RuleFor(p => p.PlatformSide, f => f.PickRandom("Left", "Right", "Both"))
                .RuleFor(p => p.SurfaceType, f => f.PickRandom("Concrete", "Asphalt", "Stone"))
                .RuleFor(p => p.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(p => p.UpdatedAt, f => f.Date.Recent(7))
                .Generate();
        }

        /// <summary>
        /// Creates a valid train with realistic data
        /// </summary>
        /// <returns>Train entity with test data</returns>
        public static Train CreateValidTrain()
        {
            return new Faker<Train>()
                .RuleFor(t => t.TrainNumber, f => f.Random.String2(4, 6, "0123456789"))
                .RuleFor(t => t.TrainName, f => f.Random.Bool(0.8f) ? f.Lorem.Words(2).Join(" ") + " Express" : null)
                .RuleFor(t => t.TrainType, f => f.PickRandom<TrainType>())
                .RuleFor(t => t.OperatorCode, f => f.Random.String2(2, 4).ToUpper())
                .RuleFor(t => t.MaxSpeed, f => f.Random.Int(60, 200))
                .RuleFor(t => t.CoachCount, f => f.Random.Int(8, 24))
                .RuleFor(t => t.SeatingCapacity, f => f.Random.Int(500, 2000))
                .RuleFor(t => t.SourceStation, f => f.Random.String2(3, 4).ToUpper())
                .RuleFor(t => t.DestinationStation, f => f.Random.String2(3, 4).ToUpper())
                .RuleFor(t => t.Route, f => f.Lorem.Sentence(5))
                .RuleFor(t => t.OperatingDays, f => f.PickRandom("Daily", "Mon,Wed,Fri", "Tue,Thu,Sat", "Sunday"))
                .RuleFor(t => t.IsActive, f => f.Random.Bool(0.9f))
                .RuleFor(t => t.HasPantryCar, f => f.Random.Bool(0.6f))
                .RuleFor(t => t.HasACCoaches, f => f.Random.Bool(0.7f))
                .RuleFor(t => t.HasSleeperCoaches, f => f.Random.Bool(0.8f))
                .RuleFor(t => t.HasGeneralCoaches, f => f.Random.Bool(0.9f))
                .RuleFor(t => t.HasLadiesCompartment, f => f.Random.Bool(0.8f))
                .RuleFor(t => t.HasDisabledFacilities, f => f.Random.Bool(0.4f))
                .RuleFor(t => t.EngineType, f => f.PickRandom("Electric", "Diesel", "Steam"))
                .RuleFor(t => t.GaugeType, f => f.PickRandom("Broad", "Meter", "Narrow"))
                .RuleFor(t => t.Distance, f => f.Random.Decimal(50, 3000))
                .RuleFor(t => t.JourneyTime, f => f.Random.Int(60, 2400)) // 1 hour to 40 hours
                .RuleFor(t => t.Frequency, f => f.PickRandom("Daily", "Weekly", "Bi-weekly"))
                .RuleFor(t => t.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(t => t.UpdatedAt, f => f.Date.Recent(7))
                .Generate();
        }

        /// <summary>
        /// Creates a valid schedule with realistic data
        /// </summary>
        /// <param name="trainId">Train ID for the schedule</param>
        /// <param name="stationId">Station ID for the schedule</param>
        /// <param name="platformId">Platform ID for the schedule (optional)</param>
        /// <returns>Schedule entity with test data</returns>
        public static Schedule CreateValidSchedule(int trainId = 1, int stationId = 1, int? platformId = null)
        {
            var baseTime = _faker.Date.Between(DateTime.Today, DateTime.Today.AddDays(7));
            
            return new Faker<Schedule>()
                .RuleFor(s => s.TrainId, trainId)
                .RuleFor(s => s.StationId, stationId)
                .RuleFor(s => s.PlatformId, platformId)
                .RuleFor(s => s.ScheduledArrival, f => f.Random.Bool(0.8f) ? baseTime : null)
                .RuleFor(s => s.ScheduledDeparture, f => f.Random.Bool(0.8f) ? baseTime.AddMinutes(f.Random.Int(2, 30)) : null)
                .RuleFor(s => s.Status, f => f.PickRandom<ScheduleStatus>())
                .RuleFor(s => s.DelayMinutes, f => f.Random.Int(0, 120))
                .RuleFor(s => s.DelayReason, f => f.Random.Bool(0.3f) ? f.Lorem.Sentence(3) : null)
                .RuleFor(s => s.IsPublished, f => f.Random.Bool(0.7f))
                .RuleFor(s => s.Priority, f => f.Random.Int(1, 10))
                .RuleFor(s => s.SequenceNumber, f => f.Random.Int(1, 50))
                .RuleFor(s => s.DistanceFromOrigin, f => f.Random.Decimal(0, 2000))
                .RuleFor(s => s.HaltTime, f => f.Random.Int(1, 15))
                .RuleFor(s => s.IsOrigin, f => f.Random.Bool(0.1f))
                .RuleFor(s => s.IsDestination, f => f.Random.Bool(0.1f))
                .RuleFor(s => s.IsTechnicalHalt, f => f.Random.Bool(0.2f))
                .RuleFor(s => s.ExpectedLoad, f => f.PickRandom("Light", "Medium", "Heavy"))
                .RuleFor(s => s.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(s => s.UpdatedAt, f => f.Date.Recent(7))
                .Generate();
        }

        /// <summary>
        /// Creates a valid display board with realistic data
        /// </summary>
        /// <param name="stationId">Station ID for the display board</param>
        /// <param name="platformId">Platform ID for the display board (optional)</param>
        /// <returns>DisplayBoard entity with test data</returns>
        public static DisplayBoard CreateValidDisplayBoard(int stationId = 1, int? platformId = null)
        {
            return new Faker<DisplayBoard>()
                .RuleFor(db => db.StationId, stationId)
                .RuleFor(db => db.PlatformId, platformId)
                .RuleFor(db => db.BoardType, f => f.PickRandom<DisplayBoardType>())
                .RuleFor(db => db.BoardName, f => f.Lorem.Word() + " Board")
                .RuleFor(db => db.BoardLocation, f => f.Lorem.Words(2).Join(" "))
                .RuleFor(db => db.IpAddress, f => f.Internet.Ip())
                .RuleFor(db => db.Port, f => f.Random.Int(8000, 9000))
                .RuleFor(db => db.Protocol, f => f.PickRandom<CommunicationProtocol>())
                .RuleFor(db => db.IsOnline, f => f.Random.Bool(0.8f))
                .RuleFor(db => db.LastHeartbeat, f => f.Random.Bool(0.8f) ? f.Date.Recent(1) : null)
                .RuleFor(db => db.DisplayLanguages, f => f.PickRandom("en", "en,hi", "en,hi,regional"))
                .RuleFor(db => db.RefreshInterval, f => f.Random.Int(10, 60))
                .RuleFor(db => db.MaxMessageLength, f => f.Random.Int(100, 300))
                .RuleFor(db => db.DisplayLines, f => f.Random.Int(1, 4))
                .RuleFor(db => db.Brightness, f => f.Random.Int(50, 100))
                .RuleFor(db => db.SupportsScrolling, f => f.Random.Bool(0.8f))
                .RuleFor(db => db.ScrollSpeed, f => f.Random.Int(1, 10))
                .RuleFor(db => db.FontSize, f => f.Random.Int(10, 16))
                .RuleFor(db => db.FontName, f => f.PickRandom("Arial", "Helvetica", "Times New Roman"))
                .RuleFor(db => db.IsActive, f => f.Random.Bool(0.9f))
                .RuleFor(db => db.FirmwareVersion, f => f.System.Version().ToString())
                .RuleFor(db => db.HardwareModel, f => f.Lorem.Word() + "-" + f.Random.String2(3))
                .RuleFor(db => db.SerialNumber, f => f.Random.String2(10))
                .RuleFor(db => db.InstallationDate, f => f.Date.Past(2))
                .RuleFor(db => db.WarrantyExpiry, f => f.Date.Future(1))
                .RuleFor(db => db.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(db => db.UpdatedAt, f => f.Date.Recent(7))
                .Generate();
        }

        /// <summary>
        /// Creates a valid message with realistic data
        /// </summary>
        /// <param name="displayBoardId">Display board ID for the message (optional)</param>
        /// <param name="stationId">Station ID for the message (optional)</param>
        /// <returns>Message entity with test data</returns>
        public static Message CreateValidMessage(int? displayBoardId = null, int? stationId = null)
        {
            return new Faker<Message>()
                .RuleFor(m => m.DisplayBoardId, displayBoardId)
                .RuleFor(m => m.StationId, stationId)
                .RuleFor(m => m.MessageType, f => f.PickRandom<MessageType>())
                .RuleFor(m => m.Content, f => f.Lorem.Sentence(10))
                .RuleFor(m => m.Language, f => f.PickRandom("en", "hi", "regional"))
                .RuleFor(m => m.Priority, f => f.Random.Int(1, 10))
                .RuleFor(m => m.IsActive, f => f.Random.Bool(0.8f))
                .RuleFor(m => m.IsScrolling, f => f.Random.Bool(0.3f))
                .RuleFor(m => m.ScrollSpeed, f => f.Random.Int(1, 10))
                .RuleFor(m => m.ValidFrom, f => f.Date.Recent(1))
                .RuleFor(m => m.ValidTo, f => f.Random.Bool(0.7f) ? f.Date.Future(7) : null)
                .RuleFor(m => m.RepeatCount, f => f.Random.Int(1, 10))
                .RuleFor(m => m.AudioEnabled, f => f.Random.Bool(0.4f))
                .RuleFor(m => m.DisplayDuration, f => f.Random.Int(30, 300))
                .RuleFor(m => m.FontSize, f => f.Random.Int(10, 16))
                .RuleFor(m => m.FontColor, f => f.Internet.Color())
                .RuleFor(m => m.TextAlignment, f => f.PickRandom("Left", "Center", "Right"))
                .RuleFor(m => m.IsPublished, f => f.Random.Bool(0.6f))
                .RuleFor(m => m.ApprovalStatus, f => f.PickRandom("Pending", "Approved", "Rejected"))
                .RuleFor(m => m.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(m => m.UpdatedAt, f => f.Date.Recent(7))
                .Generate();
        }

        /// <summary>
        /// Creates a valid user with realistic data
        /// </summary>
        /// <returns>User entity with test data</returns>
        public static User CreateValidUser()
        {
            return new Faker<User>()
                .RuleFor(u => u.Username, f => f.Internet.UserName())
                .RuleFor(u => u.Email, f => f.Internet.Email())
                .RuleFor(u => u.PasswordHash, f => BCrypt.Net.BCrypt.HashPassword("TestPassword123"))
                .RuleFor(u => u.FirstName, f => f.Name.FirstName())
                .RuleFor(u => u.LastName, f => f.Name.LastName())
                .RuleFor(u => u.Role, f => f.PickRandom<UserRole>())
                .RuleFor(u => u.PhoneNumber, f => f.Phone.PhoneNumber())
                .RuleFor(u => u.EmployeeId, f => f.Random.String2(6))
                .RuleFor(u => u.Department, f => f.Commerce.Department())
                .RuleFor(u => u.JobTitle, f => f.Name.JobTitle())
                .RuleFor(u => u.IsActive, f => f.Random.Bool(0.9f))
                .RuleFor(u => u.IsEmailVerified, f => f.Random.Bool(0.8f))
                .RuleFor(u => u.LastLoginAt, f => f.Random.Bool(0.7f) ? f.Date.Recent(7) : null)
                .RuleFor(u => u.PreferredLanguage, f => f.PickRandom("en", "hi", "regional"))
                .RuleFor(u => u.TimeZone, f => f.PickRandom("UTC", "Asia/Kolkata", "America/New_York"))
                .RuleFor(u => u.CreatedAt, f => f.Date.Recent(30))
                .RuleFor(u => u.UpdatedAt, f => f.Date.Recent(7))
                .Generate();
        }

        /// <summary>
        /// Creates multiple stations with platforms and display boards
        /// </summary>
        /// <param name="stationCount">Number of stations to create</param>
        /// <param name="platformsPerStation">Number of platforms per station</param>
        /// <param name="boardsPerStation">Number of display boards per station</param>
        /// <returns>List of stations with related entities</returns>
        public static List<Station> CreateStationsWithRelatedEntities(int stationCount = 3, int platformsPerStation = 2, int boardsPerStation = 2)
        {
            var stations = new List<Station>();

            for (int i = 1; i <= stationCount; i++)
            {
                var station = CreateValidStation();
                station.Id = i;

                // Create platforms for the station
                var platforms = new List<Platform>();
                for (int j = 1; j <= platformsPerStation; j++)
                {
                    var platform = CreateValidPlatform(i);
                    platform.Id = (i - 1) * platformsPerStation + j;
                    platforms.Add(platform);
                }
                station.Platforms = platforms;

                // Create display boards for the station
                var displayBoards = new List<DisplayBoard>();
                for (int k = 1; k <= boardsPerStation; k++)
                {
                    var displayBoard = CreateValidDisplayBoard(i);
                    displayBoard.Id = (i - 1) * boardsPerStation + k;
                    displayBoards.Add(displayBoard);
                }
                station.DisplayBoards = displayBoards;

                stations.Add(station);
            }

            return stations;
        }

        /// <summary>
        /// Creates a complete schedule scenario with train, station, and platform
        /// </summary>
        /// <returns>Tuple containing train, station, platform, and schedule</returns>
        public static (Train train, Station station, Platform platform, Schedule schedule) CreateCompleteScheduleScenario()
        {
            var station = CreateValidStation();
            station.Id = 1;

            var platform = CreateValidPlatform(1);
            platform.Id = 1;
            platform.Station = station;

            var train = CreateValidTrain();
            train.Id = 1;

            var schedule = CreateValidSchedule(1, 1, 1);
            schedule.Id = 1;
            schedule.Train = train;
            schedule.Station = station;
            schedule.Platform = platform;

            return (train, station, platform, schedule);
        }

        /// <summary>
        /// Creates test data for performance testing
        /// </summary>
        /// <param name="recordCount">Number of records to create</param>
        /// <returns>Dictionary with entity type and count</returns>
        public static Dictionary<string, int> CreatePerformanceTestData(int recordCount = 1000)
        {
            var result = new Dictionary<string, int>();

            // Create stations
            var stations = new List<Station>();
            for (int i = 0; i < recordCount / 10; i++)
            {
                stations.Add(CreateValidStation());
            }
            result["Stations"] = stations.Count;

            // Create trains
            var trains = new List<Train>();
            for (int i = 0; i < recordCount / 5; i++)
            {
                trains.Add(CreateValidTrain());
            }
            result["Trains"] = trains.Count;

            // Create schedules
            var schedules = new List<Schedule>();
            for (int i = 0; i < recordCount; i++)
            {
                schedules.Add(CreateValidSchedule());
            }
            result["Schedules"] = schedules.Count;

            return result;
        }
    }
}
