// Decompiled with JetBrains decompiler
// Type: ipis.pkt_construct
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.Diagnostics;

namespace ipis
{

public class pkt_construct
{
  [DebuggerNonUserCode]
  public pkt_construct()
  {
  }

  public static void normal_pkt_eng_message_display_array(
    string trainno,
    string trainname,
    string ad,
    string adtime,
    string platform_no,
    ref byte[] msg_pkt1,
    byte[] hin_msg_bitmap,
    byte[] reg_msg_bitmap)
  {
    int index1 = 0;
    while (index1 < msg_pkt1.Length)
    {
      msg_pkt1[index1] = (byte) 0;
      checked { ++index1; }
    }
    msg_pkt1[0] = checked ((byte) trainno.Length);
    int index2 = 0;
    while (index2 < trainno.Length)
    {
      msg_pkt1[checked (index2 + 1)] = checked ((byte) trainno[index2]);
      checked { ++index2; }
    }
    msg_pkt1[6] = checked ((byte) trainname.Length);
    int index3 = 0;
    while (index3 < trainname.Length)
    {
      msg_pkt1[checked (index3 + 7)] = checked ((byte) trainname[index3]);
      checked { ++index3; }
    }
    int index4 = 0;
    int num = 0;
    while (index4 < 5)
    {
      if (Operators.CompareString(Conversions.ToString(adtime[index4]), ":", false) != 0)
      {
        msg_pkt1[checked (num + 25)] = checked ((byte) adtime[index4]);
        checked { ++num; }
      }
      checked { ++index4; }
    }
    msg_pkt1[29] = checked ((byte) Strings.AscW(ad));
    if (platform_no.Length == 1)
    {
      msg_pkt1[30] = (byte) 0;
      msg_pkt1[31 /*0x1F*/] = (byte) 0;
      msg_pkt1[32 /*0x20*/] = checked ((byte) platform_no[0]);
    }
    else if (platform_no.Length == 2)
    {
      msg_pkt1[30] = (byte) 0;
      msg_pkt1[31 /*0x1F*/] = checked ((byte) platform_no[0]);
      msg_pkt1[32 /*0x20*/] = checked ((byte) platform_no[1]);
    }
    else if (platform_no.Length == 3)
    {
      msg_pkt1[30] = checked ((byte) platform_no[0]);
      msg_pkt1[31 /*0x1F*/] = checked ((byte) platform_no[1]);
      msg_pkt1[32 /*0x20*/] = checked ((byte) platform_no[2]);
    }
    int index5 = 0;
    while (index5 < 368)
    {
      msg_pkt1[checked (index5 + 33)] = hin_msg_bitmap[index5];
      checked { ++index5; }
    }
    int index6 = 0;
    while (index6 < 368)
    {
      msg_pkt1[checked (index6 + 401)] = reg_msg_bitmap[index6];
      checked { ++index6; }
    }
  }

  public static void div_ter_pkt_message_display_array(
    string trainno,
    string trainname,
    string status,
    string station_name,
    ref byte[] msg_pkt,
    byte[] hin_train_name_bitmap,
    byte[] hin_status_bitmap,
    byte[] hindi_station_name_msg_bitmap,
    byte[] reg_lang_train_name_msg_bitmap,
    byte[] reg_lang_status_name_msg_bitmap,
    byte[] reg_lang_station_name_msg_bitmap)
  {
    int index1 = 0;
    while (index1 < msg_pkt.Length)
    {
      msg_pkt[index1] = (byte) 0;
      checked { ++index1; }
    }
    msg_pkt[0] = checked ((byte) trainno.Length);
    int index2 = 0;
    while (index2 < trainno.Length)
    {
      msg_pkt[checked (index2 + 1)] = checked ((byte) trainno[index2]);
      checked { ++index2; }
    }
    msg_pkt[6] = checked ((byte) trainname.Length);
    int index3 = 0;
    while (index3 < trainname.Length)
    {
      msg_pkt[checked (index3 + 7)] = checked ((byte) trainname[index3]);
      checked { ++index3; }
    }
    msg_pkt[33] = checked ((byte) status.Length);
    int index4 = 0;
    while (index4 < status.Length)
    {
      msg_pkt[checked (index4 + 34)] = checked ((byte) status[index4]);
      checked { ++index4; }
    }
    int length = Operators.CompareString(station_name, (string) null, false) != 0 ? station_name.Length : 0;
    msg_pkt[46] = checked ((byte) length);
    int index5 = 0;
    while (index5 < length)
    {
      msg_pkt[checked (index5 + 47)] = checked ((byte) station_name[index5]);
      checked { ++index5; }
    }
    int index6 = 0;
    while (index6 < 368)
    {
      msg_pkt[checked (index6 + 65)] = hin_train_name_bitmap[index6];
      checked { ++index6; }
    }
    int index7 = 0;
    while (index7 < 156)
    {
      msg_pkt[checked (index7 + 433)] = hin_status_bitmap[index7];
      checked { ++index7; }
    }
    int index8 = 0;
    while (index8 < 368)
    {
      msg_pkt[checked (index8 + 589)] = hindi_station_name_msg_bitmap[index8];
      checked { ++index8; }
    }
    int index9 = 0;
    while (index9 < 368)
    {
      msg_pkt[checked (index9 + 957)] = reg_lang_train_name_msg_bitmap[index9];
      checked { ++index9; }
    }
    int index10 = 0;
    while (index10 < 156)
    {
      msg_pkt[checked (index10 + 1325)] = reg_lang_status_name_msg_bitmap[index10];
      checked { ++index10; }
    }
    int index11 = 0;
    while (index11 < 368)
    {
      msg_pkt[checked (index11 + 1481)] = reg_lang_station_name_msg_bitmap[index11];
      checked { ++index11; }
    }
  }

  public static void arr_hasleft_message_display_array(
    string trainno,
    string trainname,
    string train_status,
    string platform_no,
    ref byte[] msg_pkt,
    byte[] hindi_train_name_msg_bitmap,
    byte[] hindi_status_name_msg_bitmap,
    byte[] reg_lang_train_name_msg_bitmap,
    byte[] reg_lang_status_name_msg_bitmap)
  {
    int index1 = 0;
    while (index1 < msg_pkt.Length)
    {
      msg_pkt[index1] = (byte) 0;
      checked { ++index1; }
    }
    msg_pkt[0] = checked ((byte) trainno.Length);
    int index2 = 0;
    while (index2 < trainno.Length)
    {
      msg_pkt[checked (index2 + 1)] = checked ((byte) trainno[index2]);
      checked { ++index2; }
    }
    msg_pkt[6] = checked ((byte) trainname.Length);
    int index3 = 0;
    while (index3 < trainname.Length)
    {
      msg_pkt[checked (index3 + 7)] = checked ((byte) trainname[index3]);
      checked { ++index3; }
    }
    if (platform_no.Length == 1)
    {
      msg_pkt[30] = (byte) 0;
      msg_pkt[31 /*0x1F*/] = (byte) 0;
      msg_pkt[32 /*0x20*/] = checked ((byte) platform_no[0]);
    }
    else if (platform_no.Length == 2)
    {
      msg_pkt[30] = (byte) 0;
      msg_pkt[31 /*0x1F*/] = checked ((byte) platform_no[0]);
      msg_pkt[32 /*0x20*/] = checked ((byte) platform_no[1]);
    }
    else if (platform_no.Length == 3)
    {
      msg_pkt[30] = checked ((byte) platform_no[0]);
      msg_pkt[31 /*0x1F*/] = checked ((byte) platform_no[1]);
      msg_pkt[32 /*0x20*/] = checked ((byte) platform_no[2]);
    }
    int index4 = 0;
    while (index4 < 368)
    {
      msg_pkt[checked (index4 + 33)] = hindi_train_name_msg_bitmap[index4];
      checked { ++index4; }
    }
    int index5 = 0;
    while (index5 < 368)
    {
      msg_pkt[checked (index5 + 401)] = reg_lang_train_name_msg_bitmap[index5];
      checked { ++index5; }
    }
    msg_pkt[769] = checked ((byte) train_status.Length);
    int index6 = 0;
    while (index6 < train_status.Length)
    {
      msg_pkt[checked (index6 + 770)] = checked ((byte) train_status[index6]);
      checked { ++index6; }
    }
    int index7 = 0;
    while (index7 < 156)
    {
      msg_pkt[checked (index7 + 782)] = hindi_status_name_msg_bitmap[index7];
      checked { ++index7; }
    }
    int index8 = 0;
    while (index8 < 156)
    {
      msg_pkt[checked (index8 + 938)] = reg_lang_status_name_msg_bitmap[index8];
      checked { ++index8; }
    }
  }

  public static void canc_indef_message_display_array(
    string trainno,
    string trainname,
    string train_status,
    ref byte[] msg_pkt,
    byte[] hindi_train_name_msg_bitmap,
    byte[] hindi_status_name_msg_bitmap,
    byte[] reg_lang_train_name_msg_bitmap,
    byte[] reg_lang_status_name_msg_bitmap)
  {
    int index1 = 0;
    while (index1 < msg_pkt.Length)
    {
      msg_pkt[index1] = (byte) 0;
      checked { ++index1; }
    }
    msg_pkt[0] = checked ((byte) trainno.Length);
    int index2 = 0;
    while (index2 < trainno.Length)
    {
      msg_pkt[checked (index2 + 1)] = checked ((byte) trainno[index2]);
      checked { ++index2; }
    }
    msg_pkt[6] = checked ((byte) trainname.Length);
    int index3 = 0;
    while (index3 < trainname.Length)
    {
      msg_pkt[checked (index3 + 7)] = checked ((byte) trainname[index3]);
      checked { ++index3; }
    }
    int index4 = 0;
    while (index4 < 368)
    {
      msg_pkt[checked (index4 + 33)] = hindi_train_name_msg_bitmap[index4];
      checked { ++index4; }
    }
    int index5 = 0;
    while (index5 < 368)
    {
      msg_pkt[checked (index5 + 401)] = reg_lang_train_name_msg_bitmap[index5];
      checked { ++index5; }
    }
    msg_pkt[769] = checked ((byte) train_status.Length);
    int index6 = 0;
    while (index6 < train_status.Length)
    {
      msg_pkt[checked (index6 + 770)] = checked ((byte) train_status[index6]);
      checked { ++index6; }
    }
    int index7 = 0;
    while (index7 < 156)
    {
      msg_pkt[checked (index7 + 782)] = hindi_status_name_msg_bitmap[index7];
      checked { ++index7; }
    }
    int index8 = 0;
    while (index8 < 156)
    {
      msg_pkt[checked (index8 + 938)] = reg_lang_status_name_msg_bitmap[index8];
      checked { ++index8; }
    }
  }

  public static void reschedule_pkt_message_display_array(
    string trainno,
    string trainname,
    string ad,
    string adtime,
    string status,
    string platform_no,
    ref byte[] msg_pkt,
    byte[] hindi_train_name_bitmap,
    byte[] hindi_status_name_bitmap,
    byte[] reg_lang_train_name_msg_bitmap,
    byte[] reg_lang_status_name_msg_bitmap)
  {
    int index1 = 0;
    while (index1 < msg_pkt.Length)
    {
      msg_pkt[index1] = (byte) 0;
      checked { ++index1; }
    }
    msg_pkt[0] = checked ((byte) trainno.Length);
    int index2 = 0;
    while (index2 < trainno.Length)
    {
      msg_pkt[checked (index2 + 1)] = checked ((byte) trainno[index2]);
      checked { ++index2; }
    }
    msg_pkt[6] = checked ((byte) trainname.Length);
    int index3 = 0;
    while (index3 < trainname.Length)
    {
      msg_pkt[checked (index3 + 7)] = checked ((byte) trainname[index3]);
      checked { ++index3; }
    }
    int index4 = 0;
    int num = 0;
    while (index4 < 5)
    {
      if (Operators.CompareString(Conversions.ToString(adtime[index4]), ":", false) != 0)
      {
        msg_pkt[checked (num + 25)] = checked ((byte) adtime[index4]);
        checked { ++num; }
      }
      checked { ++index4; }
    }
    msg_pkt[29] = checked ((byte) Strings.AscW(ad));
    if (platform_no.Length == 1)
    {
      msg_pkt[30] = (byte) 0;
      msg_pkt[31 /*0x1F*/] = (byte) 0;
      msg_pkt[32 /*0x20*/] = checked ((byte) platform_no[0]);
    }
    else if (platform_no.Length == 2)
    {
      msg_pkt[30] = (byte) 0;
      msg_pkt[31 /*0x1F*/] = checked ((byte) platform_no[0]);
      msg_pkt[32 /*0x20*/] = checked ((byte) platform_no[1]);
    }
    else if (platform_no.Length == 3)
    {
      msg_pkt[30] = checked ((byte) platform_no[0]);
      msg_pkt[31 /*0x1F*/] = checked ((byte) platform_no[1]);
      msg_pkt[32 /*0x20*/] = checked ((byte) platform_no[2]);
    }
    msg_pkt[33] = checked ((byte) status.Length);
    int index5 = 0;
    while (index5 < status.Length)
    {
      msg_pkt[checked (index5 + 34)] = checked ((byte) status[index5]);
      checked { ++index5; }
    }
    int index6 = 0;
    while (index6 < 368)
    {
      msg_pkt[checked (index6 + 46)] = hindi_train_name_bitmap[index6];
      checked { ++index6; }
    }
    int index7 = 0;
    while (index7 < 156)
    {
      msg_pkt[checked (index7 + 414)] = hindi_status_name_bitmap[index7];
      checked { ++index7; }
    }
    int index8 = 0;
    while (index8 < 368)
    {
      msg_pkt[checked (index8 + 570)] = reg_lang_train_name_msg_bitmap[index8];
      checked { ++index8; }
    }
    int index9 = 0;
    while (index9 < 156)
    {
      msg_pkt[checked (index9 + 938)] = reg_lang_status_name_msg_bitmap[index9];
      checked { ++index9; }
    }
  }

  public static void alpha_numeric_bitmap(
    string message,
    byte[] bitmap,
    ref int message_byte_length)
  {
    int num = 0;
    num = 0;
    string[,] strArray1 = new string[36, 20]
    {
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(248),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(248),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(24),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(12),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(28),
        Conversions.ToString(120),
        Conversions.ToString(30),
        Conversions.ToString(108),
        Conversions.ToString(6),
        Conversions.ToString(102),
        Conversions.ToString(6),
        Conversions.ToString(99),
        Conversions.ToString(6),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(124),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(12),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(99),
        Conversions.ToString(198),
        Conversions.ToString(62),
        Conversions.ToString(124),
        Conversions.ToString(28),
        Conversions.ToString(56),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(3),
        Conversions.ToString(254),
        Conversions.ToString(3),
        Conversions.ToString(254),
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(254),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(198),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(198),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(198),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(198),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(134),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(134),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(248),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(252),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(142),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(12),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(120),
        Conversions.ToString(6),
        Conversions.ToString(30),
        Conversions.ToString(6),
        Conversions.ToString(7),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(198),
        Conversions.ToString(0),
        Conversions.ToString(126),
        Conversions.ToString(0),
        Conversions.ToString(30),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(252),
        Conversions.ToString(113),
        Conversions.ToString(254),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(30),
        Conversions.ToString(120),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(30),
        Conversions.ToString(120),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(248),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(252),
        Conversions.ToString(3),
        Conversions.ToString(14),
        Conversions.ToString(3),
        Conversions.ToString(6),
        Conversions.ToString(3),
        Conversions.ToString(6),
        Conversions.ToString(3),
        Conversions.ToString(14),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(252),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(248),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(30),
        Conversions.ToString(120),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(248),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(14),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(12),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(14),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(248),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(142),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(12),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(120),
        Conversions.ToString(6),
        Conversions.ToString(120),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(3),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(7),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(126),
        Conversions.ToString(62),
        Conversions.ToString(120),
        Conversions.ToString(30),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(12),
        Conversions.ToString(1),
        Conversions.ToString(248),
        Conversions.ToString(1),
        Conversions.ToString(248),
        Conversions.ToString(0),
        Conversions.ToString(12),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(56),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(3),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(248),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(6),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(252),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(248),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(1),
        Conversions.ToString(134),
        Conversions.ToString(0),
        Conversions.ToString(252),
        Conversions.ToString(0),
        Conversions.ToString(120),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(15),
        Conversions.ToString(252),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(254),
        Conversions.ToString(24),
        Conversions.ToString(6),
        Conversions.ToString(24),
        Conversions.ToString(6),
        Conversions.ToString(24),
        Conversions.ToString(6),
        Conversions.ToString(24),
        Conversions.ToString(6),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(111),
        Conversions.ToString(252),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(3),
        Conversions.ToString(134),
        Conversions.ToString(7),
        Conversions.ToString(134),
        Conversions.ToString(13),
        Conversions.ToString(134),
        Conversions.ToString(25),
        Conversions.ToString(134),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(252),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(120),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(252),
        Conversions.ToString(113),
        Conversions.ToString(254),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(142),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(12),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(6),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(7),
        Conversions.ToString(254),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(254),
        Conversions.ToString(56),
        Conversions.ToString(0),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(0),
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(0),
        Conversions.ToString(56),
        Conversions.ToString(0),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(254),
        Conversions.ToString(7),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(254),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(0),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(0),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(0),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(254),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(254),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(124),
        Conversions.ToString(62),
        Conversions.ToString(126),
        Conversions.ToString(126),
        Conversions.ToString(3),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(3),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(126),
        Conversions.ToString(126),
        Conversions.ToString(124),
        Conversions.ToString(62),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(30),
        Conversions.ToString(0),
        Conversions.ToString(62),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(62),
        Conversions.ToString(0),
        Conversions.ToString(30),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(120),
        Conversions.ToString(6),
        Conversions.ToString(124),
        Conversions.ToString(6),
        Conversions.ToString(102),
        Conversions.ToString(6),
        Conversions.ToString(99),
        Conversions.ToString(6),
        Conversions.ToString(97),
        Conversions.ToString(134),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(198),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(126),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(30),
        Conversions.ToString(0),
        Conversions.ToString(0)
      }
    };
    string[,] strArray2 = new string[26, 18]
    {
      {
        Conversions.ToString(57),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(124),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(100),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(100),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString(99),
        Conversions.ToString(0),
        Conversions.ToString(99),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(0),
        Conversions.ToString(62),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(62),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(0),
        Conversions.ToString(99),
        Conversions.ToString(0),
        Conversions.ToString(99),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(108),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(108),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(111),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(103),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(224 /*0xE0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString(3),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(3),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(0),
        Conversions.ToString(48 /*0x30*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(55),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(111),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(108),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(108),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString(3),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(0),
        Conversions.ToString(126),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(176 /*0xB0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(176 /*0xB0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(216),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(216),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString(28),
        Conversions.ToString(0),
        Conversions.ToString(54),
        Conversions.ToString(0),
        Conversions.ToString(99),
        Conversions.ToString(0),
        Conversions.ToString(65),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(248),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(248),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(224 /*0xE0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(15),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(15),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(224 /*0xE0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(12),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(12),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(15),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(7),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(7),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(15),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(12),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(12),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(51),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(119),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(102),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(102),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(126),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(60),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(1),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(240 /*0xF0*/),
        Conversions.ToString(97),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(97),
        Conversions.ToString(128 /*0x80*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(31 /*0x1F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString(60),
        Conversions.ToString(0),
        Conversions.ToString(60),
        Conversions.ToString(0),
        Conversions.ToString(96 /*0x60*/),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(113),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(123),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(4),
        Conversions.ToString(0),
        Conversions.ToString(4),
        Conversions.ToString(0),
        Conversions.ToString(123),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(113),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(49),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(99),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(102),
        Conversions.ToString(0),
        Conversions.ToString(108),
        Conversions.ToString(0),
        Conversions.ToString((int) sbyte.MaxValue),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(63 /*0x3F*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      },
      {
        Conversions.ToString(112 /*0x70*/),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(120),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(108),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(102),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(99),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(97),
        Conversions.ToString(192 /*0xC0*/),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0),
        Conversions.ToString(0)
      }
    };
    int index1 = 0;
    int index2 = 0;
    while (index1 < message.Length)
    {
      int index3 = 0;
      if (char.IsNumber(message[index1]))
      {
        while (index3 < 20)
        {
          bitmap[index2] = Conversions.ToByte(strArray1[checked ((int) message[index1] - 48 /*0x30*/), index3]);
          checked { ++index2; }
          checked { ++index3; }
        }
      }
      else if (char.IsUpper(message[index1]))
      {
        while (index3 < 20)
        {
          bitmap[index2] = Conversions.ToByte(strArray1[checked ((int) message[index1] - 55), index3]);
          checked { ++index3; }
          checked { ++index2; }
        }
      }
      else if (char.IsLower(message[index1]))
      {
        if (Operators.CompareString(Conversions.ToString(message[index1]), "m", false) == 0 | Operators.CompareString(Conversions.ToString(message[index1]), "w", false) == 0)
        {
          while (index3 < 18)
          {
            bitmap[index2] = Conversions.ToByte(strArray2[checked ((int) message[index1] - 97), index3]);
            checked { ++index2; }
            checked { ++index3; }
          }
        }
        else if (Operators.CompareString(Conversions.ToString(message[index1]), "i", false) == 0 | Operators.CompareString(Conversions.ToString(message[index1]), "l", false) == 0)
        {
          while (index3 < 6)
          {
            bitmap[index2] = Conversions.ToByte(strArray2[checked ((int) message[index1] - 97), index3]);
            checked { ++index2; }
            checked { ++index3; }
          }
        }
        else
        {
          while (index3 < 14)
          {
            bitmap[index2] = Conversions.ToByte(strArray2[checked ((int) message[index1] - 97), index3]);
            checked { ++index2; }
            checked { ++index3; }
          }
        }
      }
      else if (Operators.CompareString(Conversions.ToString(message[index1]), " ", false) == 0)
      {
        while (index3 < 12)
        {
          bitmap[index2] = (byte) 0;
          checked { ++index2; }
          checked { ++index3; }
        }
      }
      checked { ++index1; }
    }
    message_byte_length = index2;
  }
}

}