# Fix all missing ref parameters for method calls

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix basMsgBoxEx method calls
    $content = $content -replace '\.FindWindow\(([^,]+),\s*([^)]+)\)', '.FindWindow(ref $1, ref $2)'
    $content = $content -replace '\.GetWindowRect\(([^,]+),\s*([^)]+)\)', '.GetWindowRect($1, ref $2)'
    $content = $content -replace '\.MessageBox\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.MessageBox($1, ref $2, ref $3, $4)'
    
    # Fix network_db_read method calls
    $content = $content -replace 'network_db_read\.set_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_msg($1, $2, $3, $4, ref $5)'
    $content = $content -replace 'network_db_read\.set_Update_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_Update_msg($1, $2, $3, $4, ref $5)'
    $content = $content -replace 'network_db_read\.delete_msg\(([^,]+),\s*([^)]+)\)', 'network_db_read.delete_msg($1, ref $2)'
    $content = $content -replace 'network_db_read\.set_voice_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_voice_msg($1, $2, $3, $4, $5, ref $6)'
    $content = $content -replace 'network_db_read\.update_voice_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.update_voice_msg($1, $2, $3, $4, $5, ref $6)'
    $content = $content -replace 'network_db_read\.set_user_group\(([^,]+),\s*([^)]+)\)', 'network_db_read.set_user_group(ref $1, $2)'
    $content = $content -replace 'network_db_read\.enc_pwd\(([^,]+),\s*([^)]+)\)', 'network_db_read.enc_pwd($1, ref $2)'
    $content = $content -replace 'network_db_read\.dec_pwd\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.dec_pwd($1, ref $2, $3)'
    $content = $content -replace 'network_db_read\.set_user_pwd\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_user_pwd($1, $2, $3, ref $4)'
    $content = $content -replace 'network_db_read\.set_user_name\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_user_name($1, $2, ref $3)'
    $content = $content -replace 'network_db_read\.delete_pwd\(([^,]+),\s*([^)]+)\)', 'network_db_read.delete_pwd($1, ref $2)'
    $content = $content -replace 'network_db_read\.set_com_baud_values\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'network_db_read.set_com_baud_values($1, $2, ref $3)'
    
    # Fix double ref issues
    $content = $content -replace 'ref\s+ref\s+', 'ref '
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed missing ref parameters"
    }
}

Write-Host "Missing ref parameter fixes complete!"
