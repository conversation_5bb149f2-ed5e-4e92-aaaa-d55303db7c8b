// Decompiled with JetBrains decompiler
// Type: ipis.defines
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Diagnostics;

namespace ipis
{

public class defines
{
  public const int HEADER1 = 170;
  public const int HEADER2 = 204;
  public const byte HEADER_BYTE1 = 0;
  public const byte HEADER_BYTE2 = 1;
  public const byte LEN_MSB = 2;
  public const byte LEN_LSB = 3;
  public const byte SRC_ADDR_MSB = 4;
  public const byte SRC_ADDR_LSB = 5;
  public const byte DES_ADDR_MSB = 6;
  public const byte DES_ADDR_LSB = 7;
  public const byte SER_NO = 8;
  public const byte FC = 9;
  public const int DATA_PKT_TYPE = 10;
  public const int DATA_PKT_MSG_SW_DLY = 11;
  public const int DATA_PKT_EFFECT = 12;
  public const int DATA_PKT_VIDEO_TYPE = 13;
  public const byte RES_PKT_LEN_MSB = 0;
  public const byte RES_PKT_LEN_LSB = 1;
  public const byte RES_PKT_SRC_ADDR_MSB = 2;
  public const byte RES_PKT_SRC_ADDR_LSB = 3;
  public const byte RES_PKT_DES_ADDR_MSB = 4;
  public const byte RES_PKT_DES_ADDR_LSB = 5;
  public const byte RES_PKT_SER_NO = 6;
  public const byte RES_PKT_FC = 7;
  public const byte RES_PKT_STATUS = 8;
  public const ushort MDCH_TX_BUFFER_SIZE = 1999;
  public const ushort MDCH_RX_BUFFER_SIZE = 1999;
  public const byte MDCH_LINKCHK_PKT_LEN_MSB = 0;
  public const byte MDCH_LINKCHK_PKT_LEN_LSB = 10;
  public const ushort MDCH_LINK_CHK_RES_PKT_LEN = 702;
  public const int MDCH_LNKCHK_LEN = 688;
  public const byte MDCH_SET_CFG_PKT_LEN_MSB = 1;
  public const byte MDCH_SET_CFG_PKT_LEN_LSB = 122;
  public const byte MDCH_SET_CFG_RES_PKT_LEN = 11;
  public const int MDCH_SET_CFG_LEN = 368;
  public const byte MDCH_GET_CFG_PKT_LEN_MSB = 0;
  public const byte MDCH_GET_CFG_PKT_LEN_LSB = 10;
  public const ushort MDCH_GET_CFG_RES_PKT_LEN = 380;
  public const byte MDCH_SOFT_RST_PKT_LEN_MSB = 0;
  public const byte MDCH_SOFT_RST_PKT_LEN_LSB = 10;
  public const byte MDCH_CLR_RST_PKT_LEN_MSB = 0;
  public const byte MDCH_CLR_RST_PKT_LEN_LSB = 10;
  public const byte MDCH_CLR_RST_RES_PKT_LEN = 11;
  public const byte MDCH_PRE_CMD_PKT_LEN_MSB = 0;
  public const byte MDCH_PRE_CMD_PKT_LEN_LSB = 10;
  public const byte MDCH_PRE_CMD_RES_PKT_LEN = 14;
  public const byte MDCH_DIAG_PKT_LEN_MSB = 0;
  public const byte MDCH_DIAG_PKT_LEN_LSB = 10;
  public const byte MDCH_DIA_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const byte MDCH_OPT_CMD_PKT_LEN_MSB = 0;
  public const byte MDCH_OPT_CMD_PKT_LEN_LSB = 10;
  public const byte MDCH_OPT_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const ushort PDCH_TX_BUFFER_SIZE = 1999;
  public const ushort PDCH_RX_BUFFER_SIZE = 1999;
  public const byte PDCH_LINKCHK_PKT_LEN_MSB = 0;
  public const byte PDCH_LINKCHK_PKT_LEN_LSB = 10;
  public const ushort PDCH_LINK_CHK_RES_PKT_LEN = 702;
  public const int PDCH_LNKCHK_LEN = 688;
  public const byte PDCH_SET_CFG_PKT_LEN_MSB = 1;
  public const byte PDCH_SET_CFG_PKT_LEN_LSB = 122;
  public const byte PDCH_SET_CFG_RES_PKT_LEN = 11;
  public const int PDCH_SET_CFG_LEN = 368;
  public const byte PDCH_GET_CFG_PKT_LEN_MSB = 0;
  public const byte PDCH_GET_CFG_PKT_LEN_LSB = 10;
  public const ushort PDCH_GET_CFG_RES_PKT_LEN = 380;
  public const byte PDCH_SOFT_RST_PKT_LEN_MSB = 0;
  public const byte PDCH_SOFT_RST_PKT_LEN_LSB = 10;
  public const byte PDCH_CLR_RST_PKT_LEN_MSB = 0;
  public const byte PDCH_CLR_RST_PKT_LEN_LSB = 10;
  public const byte PDCH_CLR_RST_RES_PKT_LEN = 11;
  public const byte PDCH_PRE_CMD_PKT_LEN_MSB = 0;
  public const byte PDCH_PRE_CMD_PKT_LEN_LSB = 10;
  public const byte PDCH_PRE_CMD_RES_PKT_LEN = 14;
  public const byte PDCH_DIAG_PKT_LEN_MSB = 0;
  public const byte PDCH_DIAG_PKT_LEN_LSB = 10;
  public const byte PDCH_DIA_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const byte PDCH_OPT_CMD_PKT_LEN_MSB = 0;
  public const byte PDCH_OPT_CMD_PKT_LEN_LSB = 10;
  public const byte PDCH_OPT_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const byte AGDB_LINK_CHK_PKT_LEN_MSB = 0;
  public const byte AGDB_LINK_CHK_PKT_LEN_LSB = 10;
  public const byte AGDB_LINK_CHK_RES_PKT_LEN = 12;
  public const byte AGDB_DATA_PKT_LEN = 14;
  public const byte AGDB_DATA_PKT_LEN_MSB = 2;
  public const byte AGDB_DATA_PKT_LEN_LSB = 222;
  public const byte AGDB_DATA_RES_PKT_LEN = 11;
  public const byte AGDB_SET_CFG_PKT_LEN_MSB = 0;
  public const byte AGDB_SET_CFG_PKT_LEN_LSB = 12;
  public const byte AGDB_SET_CFG_RES_PKT_LEN = 11;
  public const byte AGDB_GET_CFG_PKT_LEN_MSB = 0;
  public const byte AGDB_GET_CFG_PKT_LEN_LSB = 10;
  public const byte AGDB_GET_CFG_RES_PKT_LEN = 14;
  public const byte AGDB_SOFTRST_PKT_LEN_MSB = 0;
  public const byte AGDB_SOFTRST_PKT_LEN_LSB = 10;
  public const byte AGDB_CLRRST_PKT_LEN_MSB = 0;
  public const byte AGDB_CLRRST_PKT_LEN_LSB = 10;
  public const byte AGDB_CLRRST_RES_PKT_LEN = 11;
  public const byte AGDB_PRE_CMD_PKT_LEN_MSB = 0;
  public const byte AGDB_PRE_CMD_PKT_LEN_LSB = 10;
  public const byte AGDB_PRE_CMD_RES_PKT_LEN = 14;
  public const byte AGDB_DIAG_CMD_PKT_LEN_MSB = 0;
  public const byte AGDB_DIAG_CMD_PKt_LEN_LSB = 10;
  public const byte AGDB_DIAG_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const int AGDB_MSG_SIZE = 240 /*0xF0*/;
  public const int AGDB_TX_BUFFER_SIZE = 754;
  public const int AGDB_RX_BUFEER_SIZE = 2000;
  public const byte PDB_LINK_CHK_PKT_LEN_MSB = 0;
  public const byte PDB_LINK_CHK_PKT_LEN_LSB = 10;
  public const byte PDB_LINK_CHK_RES_PKT_LEN = 12;
  public const byte PDB_DATA_PKT_LEN = 14;
  public const byte PDB_DATA_RES_PKT_LEN = 11;
  public const byte PDB_SET_CFG_PKT_LEN_MSB = 0;
  public const byte PDB_SET_CFG_PKT_LEN_LSB = 12;
  public const byte PDB_SET_CFG_RES_PKT_LEN = 11;
  public const byte PDB_GET_CFG_PKT_LEN_MSB = 0;
  public const byte PDB_GET_CFG_PKT_LEN_LSB = 10;
  public const byte PDB_GET_CFG_RES_PKT_LEN = 14;
  public const byte PDB_SOFTRST_PKT_LEN_MSB = 0;
  public const byte PDB_SOFTRST_PKT_LEN_LSB = 10;
  public const byte PDB_CLRRST_PKT_LEN_MSB = 0;
  public const byte PDB_CLRRST_PKT_LEN_LSB = 10;
  public const byte PDB_CLRRST_RES_PKT_LEN = 11;
  public const byte PDB_PRE_CMD_PKT_LEN_MSB = 0;
  public const byte PDB_PRE_CMD_PKT_LEN_LSB = 10;
  public const byte PDB_PRE_CMD_RES_PKT_LEN = 14;
  public const byte PDB_DIAG_CMD_PKT_LEN_MSB = 0;
  public const byte PDB_DIAG_CMD_PKt_LEN_LSB = 10;
  public const byte PDB_DIAG_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const byte CGDB_LINK_CHK_PKT_LEN_MSB = 0;
  public const byte CGDB_LINK_CHK_PKT_LEN_LSB = 10;
  public const byte CGDB_LINK_CHK_RES_PKT_LEN = 12;
  public const byte CGDB_DATA_PKT_LEN_MSB = 0;
  public const byte CGDB_DATA_PKT_LEN_LSB = 14;
  public const byte CGDB_DATA_RES_PKT_LEN = 11;
  public const int TRAIN_NO_BYTE4 = 14;
  public const int TRAIN_NO_BYTE3 = 15;
  public const int TRAIN_NO_BYTE2 = 16 /*0x10*/;
  public const int TRAIN_NO_BYTE1 = 17;
  public const int COACH_NO_BYTE4 = 18;
  public const int COACH_NO_BYTE3 = 19;
  public const int COACH_NO_BYTE2 = 20;
  public const int COACH_NO_BYTE1 = 21;
  public const byte CGDB_START_PKT_LEN_MSB = 0;
  public const byte CGDB_START_PKT_LEN_LSB = 10;
  public const byte CGDB_STOP_PKT_LEN_MSB = 0;
  public const byte CGDB_STOP_PKT_LEN_LSB = 10;
  public const byte CGDB_SET_CFG_PKT_LEN_MSB = 0;
  public const byte CGDB_SET_CFG_PKT_LEN_LSB = 12;
  public const byte CGDB_SET_CFG_RES_PKT_LEN = 11;
  public const byte CGDB_GET_CFG_PKT_LEN_MSB = 0;
  public const byte CGDB_GET_CFG_PKT_LEN_LSB = 10;
  public const byte CGDB_GET_CFG_RES_PKT_LEN = 14;
  public const byte CGDB_SOFTRST_PKT_LEN_MSB = 0;
  public const byte CGDB_SOFTRST_PKT_LEN_LSB = 10;
  public const byte CGDB_CLRRST_PKT_LEN_MSB = 0;
  public const byte CGDB_CLRRST_PKT_LEN_LSB = 10;
  public const byte CGDB_CLRRST_RES_PKT_LEN = 11;
  public const byte CGDB_PRE_CMD_PKT_LEN_MSB = 0;
  public const byte CGDB_PRE_CMD_PKT_LEN_LSB = 10;
  public const byte CGDB_PRE_CMD_RES_PKT_LEN = 14;
  public const byte CGDB_DIAG_CMD_PKT_LEN_MSB = 0;
  public const byte CGDB_DIAG_CMD_PKt_LEN_LSB = 10;
  public const byte CGDB_DIAG_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const byte CGDB_TEST_CMD_RES_PKT_LEN = 11;
  public const byte BROADCAST_ADDR = 255 /*0xFF*/;
  public const byte MLDB_LINK_CHK_PKT_LEN_MSB = 0;
  public const byte MLDB_LINK_CHK_PKT_LEN_LSB = 10;
  public const byte MLDB_LINK_CHK_RES_PKT_LEN = 12;
  public const byte MLDB_DATA_PKT_LEN = 14;
  public const byte MLDB_DATA_RES_PKT_LEN = 11;
  public const byte MLDB_SET_CFG_PKT_LEN_MSB = 0;
  public const byte MLDB_SET_CFG_PKT_LEN_LSB = 12;
  public const byte MLDB_SET_CFG_RES_PKT_LEN = 11;
  public const byte MLDB_GET_CFG_PKT_LEN_MSB = 0;
  public const byte MLDB_GET_CFG_PKT_LEN_LSB = 10;
  public const byte MLDB_GET_CFG_RES_PKT_LEN = 14;
  public const byte MLDB_SOFTRST_PKT_LEN_MSB = 0;
  public const byte MLDB_SOFTRST_PKT_LEN_LSB = 10;
  public const byte MLDB_CLRRST_PKT_LEN_MSB = 0;
  public const byte MLDB_CLRRST_PKT_LEN_LSB = 10;
  public const byte MLDB_CLRRST_RES_PKT_LEN = 11;
  public const byte MLDB_PRE_CMD_PKT_LEN_MSB = 0;
  public const byte MLDB_PRE_CMD_PKT_LEN_LSB = 10;
  public const byte MLDB_PRE_CMD_RES_PKT_LEN = 14;
  public const byte MLDB_DIAG_CMD_PKT_LEN_MSB = 0;
  public const byte MLDB_DIAG_CMD_PKt_LEN_LSB = 10;
  public const byte MLDB_DIAG_CMD_RES_PKT_LEN = 16 /*0x10*/;
  public const byte LINK_CHECK_PKT = 128 /*0x80*/;
  public const byte DATA_TRANS_PKT = 129;
  public const byte STOP_PKT = 130;
  public const byte START_PKT = 131;
  public const byte SET_CFG_PKT = 132;
  public const byte GET_CFG_PKT = 133;
  public const byte SOFTRST_PKT = 134;
  public const byte CLR_RST_STATUS_PKT = 135;
  public const byte PRE_CMD_STATUS_PKT = 136;
  public const byte PROTOCOL_ERR_PKT = 137;
  public const byte DIAG_CMD_PKT = 138;
  public const byte OPT_CMD1_PKT = 139;
  public const byte OPT_CMD2_PKT = 140;
  public const byte PKT_SUCCESS = 0;
  public const byte CMD_SIZE_MORE = 1;
  public const byte CRC_FAIL = 2;
  public const byte INVALID_DES_ADDR = 3;
  public const byte INVALID_FROM_ADDR = 4;
  public const byte CCU_ID_CONFLICT = 5;
  public const byte INVALID_FC = 6;
  public const byte NO_BUFFER_FREE = 7;
  public const byte TRANS_FAIL_HUB = 8;
  public const byte PORT_TEST_IN_PROGRSS = 9;
  public const byte NO_CFG = 10;
  public const byte OPERATION_FAIL = 11;
  public const byte ABNORMAL_START_DATA_PKT = 32 /*0x20*/;
  public const byte SERIAL_NO_MISMATCH = 33;
  public const byte INTERNAL_BUF_OVERFLOW = 34;
  public const byte INVALID_DATA_LEN = 35;
  public const byte INVALID_DATA = 36;
  public const byte INTERNAL_WRITE_ERR = 37;
  public const byte DUE_TO_OTHER = 38;
  public const byte EFFECT_CLTR = 1;
  public const byte EFFECT_CTTB = 2;
  public const byte EFFECT_CBTT = 3;
  public const byte EFFECT_TLTR = 4;
  public const byte EFFECT_RRTL = 5;
  public const byte EFFECT_RTTB = 6;
  public const byte EFFECT_RBTT = 7;
  public const byte EFFECT_FLASH = 8;
  public const byte EFFECT_NORMAL = 9;
  public const byte NORMAL_PKT_TYPE = 0;
  public const byte CAN_INDEF_PKT_TYPE = 5;
  public const byte ARR_HASLEFT_PKT_TYPE = 4;
  public const byte RESCHEDULED_PKT_TYPE1 = 8;
  public const byte RESCHEDULED_PKT_TYPE2 = 9;
  public const byte MESSAGE_PKT_TYPE = 10;
  public const byte DIV_TER_PKT_TYPE1 = 6;
  public const byte DIV_TER_PKT_TYPE2 = 7;
  public const byte DELETE_PKT_TYPE = 2;
  public const byte TEST_PKT_TYPE = 3;
  public const byte DEF_MSG_PKT_TYPE = 1;

  [DebuggerNonUserCode]
  public defines()
  {
  }
}

}