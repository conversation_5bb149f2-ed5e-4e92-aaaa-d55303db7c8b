// Decompiled with JetBrains decompiler
// Type: ipis.frmUser
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmUser : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnPwd")]
  private Button _btnPwd;
  [AccessedThroughProperty("btnNewUser")]
  private Button _btnNewUser;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("event_chgPwd")]
  private frmChangeUserPwd _event_chgPwd;
  [AccessedThroughProperty("event_chgName")]
  private frmChangeName _event_chgName;
  [AccessedThroughProperty("event_changeaccount")]
  private frmChangeAccount _event_changeaccount;
  [AccessedThroughProperty("event_deleteuser")]
  private frmDeleteUser _event_deleteuser;

  [DebuggerNonUserCode]
  static frmUser()
  {
  }

  [DebuggerNonUserCode]
  public frmUser()
  {
    this.Load += new EventHandler(this.frmUser_Load);
    frmUser.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmUser.__ENCList)
    {
      if (frmUser.__ENCList.Count == frmUser.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmUser.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmUser.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmUser.__ENCList[index1] = frmUser.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmUser.__ENCList.RemoveRange(index1, checked (frmUser.__ENCList.Count - index1));
        frmUser.__ENCList.Capacity = frmUser.__ENCList.Count;
      }
      frmUser.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnPwd = new Button();
    this.btnNewUser = new Button();
    this.btnExit = new Button();
    this.SuspendLayout();
    this.btnPwd.BackColor = SystemColors.ButtonFace;
    this.btnPwd.FlatStyle = FlatStyle.Popup;
    this.btnPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnPwd1 = this.btnPwd;
    Point point1 = new Point(33, 28);
    Point point2 = point1;
    btnPwd1.Location = point2;
    this.btnPwd.Name = "btnPwd";
    Button btnPwd2 = this.btnPwd;
    Size size1 = new Size(207, 23);
    Size size2 = size1;
    btnPwd2.Size = size2;
    this.btnPwd.TabIndex = 1;
    this.btnPwd.Text = "&Change Password";
    this.btnPwd.UseVisualStyleBackColor = false;
    this.btnNewUser.BackColor = SystemColors.Control;
    this.btnNewUser.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnNewUser1 = this.btnNewUser;
    point1 = new Point(33, 85);
    Point point3 = point1;
    btnNewUser1.Location = point3;
    this.btnNewUser.Name = "btnNewUser";
    Button btnNewUser2 = this.btnNewUser;
    size1 = new Size(207, 23);
    Size size3 = size1;
    btnNewUser2.Size = size3;
    this.btnNewUser.TabIndex = 2;
    this.btnNewUser.Text = "&Manage another account";
    this.btnNewUser.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(88, 145);
    Point point4 = point1;
    btnExit1.Location = point4;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(82, 23);
    Size size4 = size1;
    btnExit2.Size = size4;
    this.btnExit.TabIndex = 3;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(266, 180);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnNewUser);
    this.Controls.Add((Control) this.btnPwd);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmUser";
    this.Text = "User";
    this.ResumeLayout(false);
  }

  internal virtual Button btnPwd
  {
    [DebuggerNonUserCode] get { return this._btnPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button1_Click);
      if (this._btnPwd != null)
        this._btnPwd.Click -= eventHandler;
      this._btnPwd = value;
      if (this._btnPwd == null)
        return;
      this._btnPwd.Click += eventHandler;
    }
  }

  internal virtual Button btnNewUser
  {
    [DebuggerNonUserCode] get { return this._btnNewUser; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnNewUser_Click);
      if (this._btnNewUser != null)
        this._btnNewUser.Click -= eventHandler;
      this._btnNewUser = value;
      if (this._btnNewUser == null)
        return;
      this._btnNewUser.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  protected virtual frmChangeUserPwd event_chgPwd
  {
    [DebuggerNonUserCode] get { return this._event_chgPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_chgPwd = value;
    }
  }

  protected virtual frmChangeName event_chgName
  {
    [DebuggerNonUserCode] get { return this._event_chgName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_chgName = value;
    }
  }

  protected virtual frmChangeAccount event_changeaccount
  {
    [DebuggerNonUserCode] get { return this._event_changeaccount; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_changeaccount = value;
    }
  }

  protected virtual frmDeleteUser event_deleteuser
  {
    [DebuggerNonUserCode] get { return this._event_deleteuser; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_deleteuser = value;
    }
  }

  private void btnName_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_chgName))
    {
      if (!this.event_chgName.IsDisposed)
      {
        this.event_chgName.WindowState = FormWindowState.Normal;
        this.event_chgName.BringToFront();
      }
      else
      {
        this.event_chgName = new frmChangeName();
        this.event_chgName.Show();
      }
    }
    else
    {
      this.event_chgName = new frmChangeName();
      this.event_chgName.Show();
    }
    this.Close();
  }

  private void Button1_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_chgPwd))
    {
      if (!this.event_chgPwd.IsDisposed)
      {
        this.event_chgPwd.WindowState = FormWindowState.Normal;
        this.event_chgPwd.BringToFront();
      }
      else
      {
        this.event_chgPwd = new frmChangeUserPwd();
        this.event_chgPwd.Show();
      }
    }
    else
    {
      this.event_chgPwd = new frmChangeUserPwd();
      this.event_chgPwd.Show();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    try
    {
      string str = "Z:\\Database\\logindb.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\logindb.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    this.Close();
  }

  private void btnNewUser_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_changeaccount))
    {
      if (!this.event_changeaccount.IsDisposed)
      {
        this.event_changeaccount.WindowState = FormWindowState.Normal;
        this.event_changeaccount.BringToFront();
      }
      else
      {
        this.event_changeaccount = new frmChangeAccount();
        this.event_changeaccount.Show();
      }
    }
    else
    {
      this.event_changeaccount = new frmChangeAccount();
      this.event_changeaccount.Show();
    }
    this.Close();
  }

  private void btnDeleteUser_Click(object sender, EventArgs e)
  {
    if (Interaction.MsgBox((object) "Do you Want to Delete User ( Y/N) ", MsgBoxStyle.YesNo) != MsgBoxResult.Yes)
      return;
    if (!Information.IsNothing((object) this.event_deleteuser))
    {
      if (!this.event_deleteuser.IsDisposed)
      {
        this.event_deleteuser.WindowState = FormWindowState.Normal;
        this.event_deleteuser.BringToFront();
      }
      else
      {
        this.event_deleteuser = new frmDeleteUser();
        this.event_deleteuser.Show();
      }
    }
    else
    {
      this.event_deleteuser = new frmDeleteUser();
      this.event_deleteuser.Show();
    }
    this.Close();
  }

  private void frmUser_Load(object sender, EventArgs e)
  {
    if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_login_details.group), "Admin", false) == 0)
      this.btnNewUser.Enabled = true;
    else
      this.btnNewUser.Enabled = false;
  }
}

}