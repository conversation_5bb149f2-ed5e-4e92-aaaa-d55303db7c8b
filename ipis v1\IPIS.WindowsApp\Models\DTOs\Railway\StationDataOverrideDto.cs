using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for manual station data override
    /// </summary>
    public class StationDataOverrideDto
    {
        /// <summary>
        /// Station code being overridden
        /// </summary>
        [Required]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Override station name
        /// </summary>
        public string? StationName { get; set; }

        /// <summary>
        /// Override station status
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Override platform information
        /// </summary>
        public Dictionary<string, string>? PlatformOverrides { get; set; }

        /// <summary>
        /// Override arrival information for specific trains
        /// </summary>
        public Dictionary<string, TrainArrivalOverride>? ArrivalOverrides { get; set; }

        /// <summary>
        /// Override departure information for specific trains
        /// </summary>
        public Dictionary<string, TrainDepartureOverride>? DepartureOverrides { get; set; }

        /// <summary>
        /// General station announcements
        /// </summary>
        public List<StationAnnouncementDto>? Announcements { get; set; }

        /// <summary>
        /// Override reason/source
        /// </summary>
        [Required]
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// User who created the override
        /// </summary>
        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Override expiry time (when to revert to API data)
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// Whether this override should persist until manually removed
        /// </summary>
        public bool IsPermanent { get; set; }

        /// <summary>
        /// Priority level (higher priority overrides lower priority)
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Whether to broadcast this override to all displays
        /// </summary>
        public bool BroadcastToAllDisplays { get; set; } = true;

        /// <summary>
        /// Custom announcement message for the station
        /// </summary>
        public string? AnnouncementMessage { get; set; }

        /// <summary>
        /// Languages for announcement (comma-separated)
        /// </summary>
        public string? AnnouncementLanguages { get; set; } = "en,hi";

        /// <summary>
        /// Whether to play audio announcement
        /// </summary>
        public bool PlayAudioAnnouncement { get; set; }

        /// <summary>
        /// Audio file path for custom announcement
        /// </summary>
        public string? CustomAudioFile { get; set; }

        /// <summary>
        /// Display color for this override
        /// </summary>
        public string? DisplayColor { get; set; }

        /// <summary>
        /// Whether to blink/flash the display
        /// </summary>
        public bool FlashDisplay { get; set; }

        /// <summary>
        /// Additional metadata as JSON
        /// </summary>
        public string? Metadata { get; set; }

        /// <summary>
        /// Override creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Whether the override is currently active
        /// </summary>
        public bool IsActive
        {
            get
            {
                if (IsPermanent) return true;
                if (ExpiryTime.HasValue)
                    return DateTime.UtcNow < ExpiryTime.Value;
                return true;
            }
        }

        /// <summary>
        /// Time remaining until expiry
        /// </summary>
        public TimeSpan? TimeToExpiry
        {
            get
            {
                if (IsPermanent) return null;
                if (ExpiryTime.HasValue)
                {
                    var remaining = ExpiryTime.Value - DateTime.UtcNow;
                    return remaining.TotalSeconds > 0 ? remaining : TimeSpan.Zero;
                }
                return null;
            }
        }

        /// <summary>
        /// Override status description
        /// </summary>
        public string StatusDescription
        {
            get
            {
                if (!IsActive) return "Expired";
                if (IsPermanent) return "Permanent Override";
                if (TimeToExpiry.HasValue)
                {
                    var remaining = TimeToExpiry.Value;
                    if (remaining.TotalHours >= 1)
                        return $"Expires in {(int)remaining.TotalHours}h {remaining.Minutes}m";
                    else
                        return $"Expires in {remaining.Minutes}m";
                }
                return "Active";
            }
        }
    }

    /// <summary>
    /// Train arrival override information
    /// </summary>
    public class TrainArrivalOverride
    {
        public string? TrainNumber { get; set; }
        public string? TrainName { get; set; }
        public DateTime? ExpectedArrival { get; set; }
        public DateTime? ActualArrival { get; set; }
        public string? Platform { get; set; }
        public string? Status { get; set; }
        public int DelayMinutes { get; set; }
        public string? Remarks { get; set; }
        public bool IsCancelled { get; set; }
        public bool IsDiverted { get; set; }
    }

    /// <summary>
    /// Train departure override information
    /// </summary>
    public class TrainDepartureOverride
    {
        public string? TrainNumber { get; set; }
        public string? TrainName { get; set; }
        public DateTime? ExpectedDeparture { get; set; }
        public DateTime? ActualDeparture { get; set; }
        public string? Platform { get; set; }
        public string? Status { get; set; }
        public int DelayMinutes { get; set; }
        public string? Remarks { get; set; }
        public bool IsCancelled { get; set; }
        public bool IsDiverted { get; set; }
    }

    /// <summary>
    /// Station announcement information
    /// </summary>
    public class StationAnnouncementDto
    {
        public string Message { get; set; } = string.Empty;
        public string Language { get; set; } = "en";
        public int Priority { get; set; } = 5;
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsActive { get; set; } = true;
        public string? AudioFile { get; set; }
        public bool RepeatAnnouncement { get; set; }
        public int RepeatInterval { get; set; } = 300; // seconds
    }
}
