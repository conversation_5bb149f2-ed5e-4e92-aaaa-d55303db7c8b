# PowerShell script to fix nameof expressions and invalid ref expressions

Write-Host "=== FIXING NAMEOF AND REF EXPRESSION ISSUES ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix nameof expressions - replace with string literals
    $content = $content -replace 'nameof\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\)', '"$1"'
    
    # Fix invalid standalone ref expressions
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;\s*\n', "`n    $1;`n"
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*,', ' $1,'
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\)', ' $1)'
    
    # Fix method calls that need ref parameters
    $content = $content -replace '\.set_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.set_msg($1, $2, $3, $4, ref $5)'
    $content = $content -replace '\.set_Update_msg\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.set_Update_msg($1, $2, $3, $4, ref $5)'
    $content = $content -replace '\.delete_msg\(([^,]+),\s*([^)]+)\)', '.delete_msg($1, ref $2)'
    $content = $content -replace '\.set_com_baud_values\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.set_com_baud_values($1, $2, ref $3)'
    $content = $content -replace '\.get_video_db\(([^,]+),\s*([^)]+)\)', '.get_video_db(ref $1, ref $2)'
    $content = $content -replace '\.get_video_messages\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.get_video_messages(ref $1, ref $2, ref $3)'
    $content = $content -replace '\.english_led_msg_display\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.english_led_msg_display($1, $2, ref $3, ref $4)'
    $content = $content -replace '\.hindi_led_msg_display\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.hindi_led_msg_display($1, $2, ref $3, ref $4)'
    $content = $content -replace '\.reglang_led_msg_display\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.reglang_led_msg_display($1, $2, ref $3, ref $4)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed nameof and ref expressions"
    }
}

Write-Host "Nameof and ref expression fixes complete!"
