using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a railway station entity
    /// Based on legacy system analysis - enhanced with comprehensive features
    /// </summary>
    [Table("Stations")]
    public class Station
    {
        /// <summary>
        /// Primary key for the station
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Unique station code (e.g., "NYC", "BOS", "DEL")
        /// Based on legacy system analysis - supports existing station codes
        /// </summary>
        [Required(ErrorMessage = "Station code is required")]
        [StringLength(10, ErrorMessage = "Station code cannot exceed 10 characters")]
        [RegularExpression(@"^[A-Z0-9]+$", ErrorMessage = "Station code must contain only uppercase letters and numbers")]
        [Column("StationCode")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Full name of the station
        /// </summary>
        [Required(ErrorMessage = "Station name is required")]
        [StringLength(100, ErrorMessage = "Station name cannot exceed 100 characters")]
        [Column("StationName")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Location or address of the station
        /// </summary>
        [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
        public string? Location { get; set; }

        /// <summary>
        /// Type of station (Terminal, Junction, Regular, etc.)
        /// </summary>
        [Required]
        public StationType StationType { get; set; } = StationType.Regular;

        /// <summary>
        /// Time zone for the station (important for scheduling)
        /// </summary>
        [StringLength(50)]
        [Column("TimeZone")]
        public string TimeZone { get; set; } = "UTC";

        /// <summary>
        /// Indicates if the station is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the station has passenger facilities
        /// </summary>
        public bool HasPassengerFacilities { get; set; } = true;

        /// <summary>
        /// Indicates if the station has freight facilities
        /// </summary>
        public bool HasFreightFacilities { get; set; } = false;

        /// <summary>
        /// Station elevation in meters (for operational purposes)
        /// </summary>
        public decimal? Elevation { get; set; }

        /// <summary>
        /// Latitude coordinate for GPS positioning
        /// </summary>
        [Column(TypeName = "decimal(10,8)")]
        public decimal? Latitude { get; set; }

        /// <summary>
        /// Longitude coordinate for GPS positioning
        /// </summary>
        [Column(TypeName = "decimal(11,8)")]
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Contact phone number for the station
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// Contact email for the station
        /// </summary>
        [StringLength(100)]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Station master or manager name
        /// </summary>
        [StringLength(100)]
        public string? StationMaster { get; set; }

        /// <summary>
        /// Operating hours for the station
        /// </summary>
        [StringLength(50)]
        public string? OperatingHours { get; set; }

        /// <summary>
        /// Additional remarks or notes about the station
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// Collection of platforms associated with this station
        /// </summary>
        public virtual ICollection<Platform> Platforms { get; set; } = new List<Platform>();

        /// <summary>
        /// Collection of schedules for trains at this station
        /// </summary>
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();

        /// <summary>
        /// Collection of display boards at this station
        /// </summary>
        public virtual ICollection<DisplayBoard> DisplayBoards { get; set; } = new List<DisplayBoard>();

        /// <summary>
        /// Collection of messages specific to this station
        /// </summary>
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        /// <summary>
        /// Collection of voice files specific to this station
        /// </summary>
        public virtual ICollection<VoiceFile> VoiceFiles { get; set; } = new List<VoiceFile>();

        /// <summary>
        /// Collection of advertisements displayed at this station
        /// </summary>
        public virtual ICollection<Advertisement> Advertisements { get; set; } = new List<Advertisement>();

        // Computed Properties

        /// <summary>
        /// Gets the total number of platforms at this station
        /// </summary>
        [NotMapped]
        public int PlatformCount => Platforms?.Count ?? 0;

        /// <summary>
        /// Gets the number of active platforms at this station
        /// </summary>
        [NotMapped]
        public int ActivePlatformCount => Platforms?.Count(p => p.IsActive) ?? 0;

        /// <summary>
        /// Gets the number of display boards at this station
        /// </summary>
        [NotMapped]
        public int DisplayBoardCount => DisplayBoards?.Count ?? 0;

        /// <summary>
        /// Gets the number of online display boards at this station
        /// </summary>
        [NotMapped]
        public int OnlineDisplayBoardCount => DisplayBoards?.Count(db => db.IsOnline) ?? 0;

        /// <summary>
        /// Gets a formatted display name for the station
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{Code} - {Name}";

        /// <summary>
        /// Gets the station status description
        /// </summary>
        [NotMapped]
        public string StatusDescription => IsActive ? "Active" : "Inactive";

        // Methods

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the station has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Code) && 
                   !string.IsNullOrWhiteSpace(Name) && 
                   Code.Length <= 10 && 
                   Name.Length <= 100;
        }

        /// <summary>
        /// Gets a summary of the station for display purposes
        /// </summary>
        /// <returns>Station summary string</returns>
        public string GetSummary()
        {
            return $"Station: {DisplayName}, Type: {StationType}, " +
                   $"Platforms: {PlatformCount}, Status: {StatusDescription}";
        }

        /// <summary>
        /// Returns a string representation of the station
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayName;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current station
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Station other)
            {
                return Id == other.Id && Code.Equals(other.Code, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the station
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Id, Code.ToUpperInvariant());
        }
    }
}
