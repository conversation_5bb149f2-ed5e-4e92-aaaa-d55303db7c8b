// Decompiled with JetBrains decompiler
// Type: ipis.pdb_api
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Threading;

namespace ipis
{

public class pdb_api
{
  private static byte[] pkt_buf = new byte[4000];
  private static byte[] rxbuf = new byte[2001];

  [DebuggerNonUserCode]
  public pdb_api()
  {
  }

  public static byte pdb_link_check(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    int index = 0;
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 128 /*0x80*/;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_link_check_res_pkt(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref short length,
    ref byte pdb_Sys_Cfg)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 12)
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 192 /*0xC0*/)
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully ");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK COMMAND PACKET STATUS: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        pdb_Sys_Cfg = pdb_api.rxbuf[9];
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK COMMAND IS UNSUCCESSFULL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr}LINK CHECK COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
        num1 = (byte) 2;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_message(
    byte pdb_addr,
    ref byte[] pdb_msg,
    int pdb_msg_len,
    byte serial_no,
    string platform_no,
    byte pdb_sw_dly,
    byte video_type,
    byte effect,
    byte packet_type,
    byte run_speed)
  {
    int index1 = 0;
    byte num1 = 0;
    try
    {
      while (index1 < 4000)
      {
        pdb_api.pkt_buf[index1] = (byte) 0;
        checked { ++index1; }
      }
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = serial_no;
      pdb_api.pkt_buf[9] = (byte) 129;
      pdb_api.pkt_buf[10] = packet_type;
      pdb_api.pkt_buf[11] = pdb_sw_dly;
      pdb_api.pkt_buf[12] = checked ((byte) (((int) effect & 15) << 4 | (int) video_type & 15));
      pdb_api.pkt_buf[13] = run_speed;
      int index2 = 0;
      while (index2 < pdb_msg_len)
      {
        pdb_api.pkt_buf[checked (14 + index2)] = pdb_msg[index2];
        checked { ++index2; }
      }
      ushort num2 = checked ((ushort) (pdb_msg_len + 14));
      pdb_api.pkt_buf[3] = checked ((byte) ((int) num2 & (int) byte.MaxValue));
      pdb_api.pkt_buf[2] = checked ((byte) (((int) num2 & 65280) >> 8));
      ushort length = checked ((ushort) ((int) num2 + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length);
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
      {
        Thread.Sleep(100);
        int num3 = 0;
        if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
        {
          ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
          if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
            num3 = 1;
          }
          if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
            num3 = 1;
          }
          if (Pkt_length != (ushort) 11)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
            num3 = 1;
          }
          if (pdb_api.rxbuf[7] != (byte) 193)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
            num3 = 1;
          }
          switch (pdb_api.rxbuf[8])
          {
            case 0:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
              break;
            case 2:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: CRC FAIL");
              num3 = 1;
              break;
            case 6:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
              num3 = 1;
              break;
            case 35:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
              num3 = 1;
              break;
          }
          if (num3 != 0)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET IS UNSUCCESSFULL");
            num1 = (byte) 0;
          }
          else
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET IS SUCCESSFUL");
            num1 = (byte) 1;
          }
        }
        else
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
          num1 = (byte) 2;
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_delete_msg(byte pdb_addr, string platform_no, byte serial_no)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      while (index < 4000)
      {
        pdb_api.pkt_buf[index] = (byte) 0;
        checked { ++index; }
      }
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = serial_no;
      pdb_api.pkt_buf[9] = (byte) 129;
      pdb_api.pkt_buf[10] = (byte) 2;
      pdb_api.pkt_buf[11] = (byte) 0;
      pdb_api.pkt_buf[12] = (byte) 0;
      pdb_api.pkt_buf[13] = (byte) 0;
      ushort num2 = 14;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 14;
      ushort length = checked ((ushort) ((int) num2 + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length);
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
      {
        Thread.Sleep(100);
        int num3 = 0;
        if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
        {
          ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
          if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
            num3 = 1;
          }
          if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
            num3 = 1;
          }
          if (Pkt_length != (ushort) 11)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
            num3 = 1;
          }
          if (pdb_api.rxbuf[7] != (byte) 193)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
            num3 = 1;
          }
          switch (pdb_api.rxbuf[8])
          {
            case 0:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
              break;
            case 2:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: CRC FAIL");
              num3 = 1;
              break;
            case 6:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
              num3 = 1;
              break;
            case 35:
              Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
              num3 = 1;
              break;
          }
          if (num3 != 0)
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET IS UNSUCCESSFULL");
            num1 = (byte) 0;
          }
          else
          {
            Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} DATA PACKET IS SUCCESSFUL");
            num1 = (byte) 1;
          }
        }
        else
        {
          Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
          num1 = (byte) 2;
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_set_cfg_send_pkt(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref byte length)
  {
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 12;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 132;
      pdb_api.pkt_buf[10] = frmMainFormIPIS.intensity;
      pdb_api.pkt_buf[11] = (byte) 0;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 12)) + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      length = checked ((byte) length1);
    int index = 0;
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_set_cfg_res_pkt(
    byte pdb_addr,
    string Platform_no,
    ref byte[] pdb_pkt,
    ref byte length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 11)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 196)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} SET CONFIGURATION COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
        num1 = (byte) 2;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_get_cfg_send_pkt(byte pdb_addr, ref byte[] pdb_pkt, ref byte length)
  {
    int index = 0;
    byte cfgSendPkt = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 133;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      length = checked ((byte) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        cfgSendPkt = (byte) 3;
      }
      else
        cfgSendPkt = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return cfgSendPkt;
  }

  public static byte pdb_get_cfg_res_pkt(
    byte pdb_addr,
    string Platform_no,
    ref byte[] pdb_pkt,
    ref short length,
    ref byte pdb_intensity,
    ref byte pdb_Sys_Cfg)
  {
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num = (byte) 1;
        }
        if (Pkt_length != (ushort) 14)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 197)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION COMMAND PACKET: CRC FAIL");
            num = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
            num = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
            num = (byte) 1;
            break;
        }
        pdb_Sys_Cfg = pdb_api.rxbuf[9];
        pdb_intensity = pdb_api.rxbuf[10];
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          return 9;
        }
        if (num != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION COMMAND IS UNSUCCESSFUL");
          return 0;
        }
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} GET CONFIGURATION COMMAND IS SUCCESSFUL");
        return 1;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return 2;
  }

  public static byte pdb_soft_reset(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    int index = 0;
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 134;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      Thread.Sleep(10);
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
      {
        Log_file.Log("PLATFORM NO:{platform_no} PDB Address:{pdb_addr} SOFT RESET COMMAND IS SUCCESSFUL");
        num = (byte) 1;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_clr_reset(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref byte length)
  {
    int index = 0;
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 135;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      length = checked ((byte) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_clr_reset_res_pkt(
    byte pdb_addr,
    string Platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 11)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 199)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAT RESET COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} CLEAR RESET COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_pre_cmd_status(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref byte length)
  {
    int index = 0;
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 136;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      length = checked ((byte) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_pre_cmd_res_pkt(
    byte pdb_addr,
    string Platform_no,
    ref byte pre_cmd_status,
    ref byte pre_cmd_serial_no,
    ref byte pre_cmd_fc,
    ref byte[] pdb_pkt,
    ref byte length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 14)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 200)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        pre_cmd_status = pdb_api.rxbuf[9];
        pre_cmd_serial_no = pdb_api.rxbuf[10];
        pre_cmd_fc = pdb_api.rxbuf[11];
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} PREVIOUS COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_diag_cmd(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    int index = 0;
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 138;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_diag_cmd_res_pkt(
    byte pdb_addr,
    string Platform_no,
    ref byte[] pdb_pkt,
    ref short length,
    ref byte[] manu)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 16 /*0x10*/)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 202)
        {
          Log_file.Log(string.Format("PLATFORM NO:{0} pdb Address:(1) DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE", (object) Platform_no, (object) pdb_addr));
          num2 = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        manu[0] = pdb_api.rxbuf[9];
        manu[1] = pdb_api.rxbuf[10];
        manu[2] = pdb_api.rxbuf[11];
        manu[3] = pdb_api.rxbuf[12];
        manu[4] = pdb_api.rxbuf[13];
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DIAGNOSTIC COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_optional_cmd(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    int index = 0;
    byte num = 0;
    try
    {
      pdb_api.pkt_buf[0] = (byte) 170;
      pdb_api.pkt_buf[1] = (byte) 204;
      pdb_api.pkt_buf[2] = (byte) 0;
      pdb_api.pkt_buf[3] = (byte) 10;
      pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      pdb_api.pkt_buf[5] = (byte) 0;
      pdb_api.pkt_buf[6] = pdb_addr;
      pdb_api.pkt_buf[7] = (byte) 0;
      pdb_api.pkt_buf[8] = (byte) 0;
      pdb_api.pkt_buf[9] = (byte) 139;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        pdb_pkt[index] = pdb_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte pdb_opt_cmd_res_pkt(
    byte pdb_addr,
    string Platform_no,
    ref byte[] pdb_pkt,
    ref short length,
    ref byte[] manu)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        pdb_pkt[0] = (byte) 170;
        pdb_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 16 /*0x10*/)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (pdb_api.rxbuf[7] != (byte) 203)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (pdb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        manu[0] = pdb_api.rxbuf[9];
        manu[1] = pdb_api.rxbuf[10];
        manu[2] = pdb_api.rxbuf[11];
        manu[3] = pdb_api.rxbuf[12];
        manu[4] = pdb_api.rxbuf[13];
        if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} OPTIONAL COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte pdb_send_test_pkt(
    byte pdb_addr,
    string platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    pdb_api.pkt_buf[0] = (byte) 170;
    pdb_api.pkt_buf[1] = (byte) 204;
    pdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    pdb_api.pkt_buf[5] = (byte) 0;
    pdb_api.pkt_buf[6] = pdb_addr;
    pdb_api.pkt_buf[7] = (byte) 0;
    pdb_api.pkt_buf[8] = (byte) 0;
    pdb_api.pkt_buf[9] = (byte) 129;
    pdb_api.pkt_buf[10] = (byte) 3;
    pdb_api.pkt_buf[11] = (byte) 0;
    pdb_api.pkt_buf[12] = (byte) 0;
    pdb_api.pkt_buf[13] = (byte) 0;
    ushort num1 = 14;
    pdb_api.pkt_buf[2] = (byte) 0;
    pdb_api.pkt_buf[3] = (byte) 14;
    ushort length1 = checked ((ushort) ((int) num1 + 2));
    Checksum.prepare_checksum(ref pdb_api.pkt_buf, length1);
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      pdb_pkt[index] = pdb_api.pkt_buf[index];
      checked { ++index; }
    }
    byte num2 = 0;
    if (RS232.Serial_Write(ref pdb_api.pkt_buf, (int) length1) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      num2 = (byte) 3;
    }
    else
      Thread.Sleep(100);
    return num2;
  }

  public static byte pdb_send_test_pkt_res(
    byte pdb_addr,
    string Platform_no,
    ref byte[] pdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref pdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) pdb_api.rxbuf[0] << 8) + (int) pdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      pdb_pkt[0] = (byte) 170;
      pdb_pkt[1] = (byte) 204;
      short index = 0;
      while ((int) index < (int) Pkt_length)
      {
        pdb_pkt[checked ((int) index + 2)] = pdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      short num = 0;
      if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num = (short) 1;
      }
      if ((int) pdb_api.rxbuf[2] != (int) pdb_addr & pdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (short) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (short) 1;
      }
      if (pdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = (short) 1;
      }
      switch (pdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: CRC FAIL");
          num = (short) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = (short) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = (short) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref pdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (short) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} PDB Address:{pdb_addr} LINK FAILURE or ADDRESSED PDB DOESN'T EXIST");
    return 2;
  }
}

}