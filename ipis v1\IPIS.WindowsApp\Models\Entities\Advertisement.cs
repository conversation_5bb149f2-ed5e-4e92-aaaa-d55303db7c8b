using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents an advertisement entity for commercial content management
    /// Based on legacy system analysis - supports advertisement scheduling and revenue tracking
    /// </summary>
    [Table("Advertisements")]
    public class Advertisement
    {
        /// <summary>
        /// Primary key for the advertisement
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the station (optional, for station-specific ads)
        /// </summary>
        [ForeignKey("Station")]
        public int? StationId { get; set; }

        /// <summary>
        /// Title of the advertisement
        /// </summary>
        [Required(ErrorMessage = "Advertisement title is required")]
        [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
        [Column("Title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Description of the advertisement
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        [Column("Description")]
        public string? Description { get; set; }

        /// <summary>
        /// Advertiser or client name
        /// </summary>
        [Required(ErrorMessage = "Advertiser name is required")]
        [StringLength(200, ErrorMessage = "Advertiser name cannot exceed 200 characters")]
        [Column("Advertiser")]
        public string Advertiser { get; set; } = string.Empty;

        /// <summary>
        /// Contact information for the advertiser
        /// </summary>
        [StringLength(500)]
        public string? AdvertiserContact { get; set; }

        /// <summary>
        /// Type of advertisement content (Audio, Video, Text, Image)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string ContentType { get; set; } = "Audio";

        /// <summary>
        /// File path to the advertisement content
        /// </summary>
        [StringLength(500)]
        public string? ContentFilePath { get; set; }

        /// <summary>
        /// Text content for text-based advertisements
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? TextContent { get; set; }

        /// <summary>
        /// Duration of the advertisement in seconds
        /// </summary>
        [Range(1, 3600, ErrorMessage = "Duration must be between 1 and 3600 seconds")]
        public int DurationSeconds { get; set; } = 30;

        /// <summary>
        /// File size in bytes
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "File size must be non-negative")]
        public long FileSizeBytes { get; set; } = 0;

        /// <summary>
        /// Priority level (1=Highest, 10=Lowest)
        /// </summary>
        [Required]
        [Range(1, 10, ErrorMessage = "Priority must be between 1 and 10")]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Category or classification of the advertisement
        /// </summary>
        [StringLength(50)]
        public string? Category { get; set; }

        /// <summary>
        /// Target audience description
        /// </summary>
        [StringLength(200)]
        public string? TargetAudience { get; set; }

        /// <summary>
        /// Languages supported by this advertisement
        /// </summary>
        [StringLength(50)]
        public string SupportedLanguages { get; set; } = "en";

        /// <summary>
        /// Start date for the advertisement campaign
        /// </summary>
        [Required]
        public DateTime StartDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// End date for the advertisement campaign
        /// </summary>
        [Required]
        public DateTime EndDate { get; set; } = DateTime.UtcNow.AddDays(30);

        /// <summary>
        /// Specific time slots when the ad should play (JSON format)
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? TimeSlots { get; set; }

        /// <summary>
        /// Days of the week when the ad should play (JSON format)
        /// </summary>
        [StringLength(50)]
        public string? PlayDays { get; set; }

        /// <summary>
        /// Frequency of playback (times per hour/day)
        /// </summary>
        [Range(1, 100, ErrorMessage = "Frequency must be between 1 and 100")]
        public int PlaybackFrequency { get; set; } = 1;

        /// <summary>
        /// Interval between playbacks in minutes
        /// </summary>
        [Range(1, 1440, ErrorMessage = "Interval must be between 1 and 1440 minutes")]
        public int PlaybackInterval { get; set; } = 60;

        /// <summary>
        /// Maximum number of plays per day
        /// </summary>
        [Range(1, 1000, ErrorMessage = "Max plays per day must be between 1 and 1000")]
        public int MaxPlaysPerDay { get; set; } = 24;

        /// <summary>
        /// Cost per play in the local currency
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        [Range(0, 999999.99, ErrorMessage = "Cost per play must be between 0 and 999999.99")]
        public decimal CostPerPlay { get; set; } = 0.00m;

        /// <summary>
        /// Total contract value
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        [Range(0, 9999999999.99, ErrorMessage = "Contract value must be between 0 and 9999999999.99")]
        public decimal ContractValue { get; set; } = 0.00m;

        /// <summary>
        /// Currency code (USD, INR, EUR, etc.)
        /// </summary>
        [StringLength(3)]
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Indicates if the advertisement is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the advertisement is approved for broadcast
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// Approval status (Pending, Approved, Rejected)
        /// </summary>
        [StringLength(20)]
        public string ApprovalStatus { get; set; } = "Pending";

        /// <summary>
        /// User who approved the advertisement
        /// </summary>
        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// Timestamp when the advertisement was approved
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Approval notes or comments
        /// </summary>
        [StringLength(500)]
        public string? ApprovalNotes { get; set; }

        /// <summary>
        /// Number of times the advertisement has been played
        /// </summary>
        public int PlayCount { get; set; } = 0;

        /// <summary>
        /// Total revenue generated from this advertisement
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalRevenue { get; set; } = 0.00m;

        /// <summary>
        /// Last time the advertisement was played
        /// </summary>
        public DateTime? LastPlayedAt { get; set; }

        /// <summary>
        /// Next scheduled play time
        /// </summary>
        public DateTime? NextScheduledPlay { get; set; }

        /// <summary>
        /// Performance rating (1-10 based on effectiveness)
        /// </summary>
        [Range(1, 10, ErrorMessage = "Performance rating must be between 1 and 10")]
        public int? PerformanceRating { get; set; }

        /// <summary>
        /// Click-through rate or engagement metrics
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal? EngagementRate { get; set; }

        /// <summary>
        /// Tags for categorization and search
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Additional metadata in JSON format
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Metadata { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// The station this advertisement is associated with (optional)
        /// </summary>
        public virtual Station? Station { get; set; }

        // Computed Properties

        /// <summary>
        /// Indicates if the advertisement is currently within its campaign period
        /// </summary>
        [NotMapped]
        public bool IsInCampaignPeriod
        {
            get
            {
                var now = DateTime.UtcNow;
                return now >= StartDate && now <= EndDate;
            }
        }

        /// <summary>
        /// Indicates if the advertisement should be played based on all criteria
        /// </summary>
        [NotMapped]
        public bool ShouldPlay => IsActive && IsApproved && IsInCampaignPeriod;

        /// <summary>
        /// Gets the campaign duration in days
        /// </summary>
        [NotMapped]
        public int CampaignDurationDays => (EndDate - StartDate).Days + 1;

        /// <summary>
        /// Gets the remaining campaign days
        /// </summary>
        [NotMapped]
        public int RemainingCampaignDays
        {
            get
            {
                var remaining = (EndDate - DateTime.UtcNow).Days;
                return Math.Max(0, remaining);
            }
        }

        /// <summary>
        /// Gets the file size in a human-readable format
        /// </summary>
        [NotMapped]
        public string FileSizeFormatted
        {
            get
            {
                if (FileSizeBytes < 1024) return $"{FileSizeBytes} B";
                if (FileSizeBytes < 1024 * 1024) return $"{FileSizeBytes / 1024.0:F1} KB";
                if (FileSizeBytes < 1024 * 1024 * 1024) return $"{FileSizeBytes / (1024.0 * 1024.0):F1} MB";
                return $"{FileSizeBytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
            }
        }

        /// <summary>
        /// Gets the duration in a human-readable format
        /// </summary>
        [NotMapped]
        public string DurationFormatted
        {
            get
            {
                var timeSpan = TimeSpan.FromSeconds(DurationSeconds);
                if (timeSpan.TotalHours >= 1)
                    return timeSpan.ToString(@"h\:mm\:ss");
                else
                    return timeSpan.ToString(@"m\:ss");
            }
        }

        /// <summary>
        /// Gets the revenue per play
        /// </summary>
        [NotMapped]
        public decimal RevenuePerPlay => PlayCount > 0 ? TotalRevenue / PlayCount : 0;

        /// <summary>
        /// Gets the priority description
        /// </summary>
        [NotMapped]
        public string PriorityDescription => Priority switch
        {
            1 => "Critical",
            2 => "High",
            3 => "Medium-High",
            4 => "Medium",
            5 => "Normal",
            6 => "Medium-Low",
            7 => "Low",
            8 => "Very Low",
            9 => "Minimal",
            10 => "Background",
            _ => "Unknown"
        };

        /// <summary>
        /// Indicates if the content file exists
        /// </summary>
        [NotMapped]
        public bool ContentFileExists => !string.IsNullOrWhiteSpace(ContentFilePath) && File.Exists(ContentFilePath);

        /// <summary>
        /// Gets the supported languages as a list
        /// </summary>
        [NotMapped]
        public List<string> SupportedLanguageList => SupportedLanguages
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(lang => lang.Trim())
            .ToList();

        // Methods

        /// <summary>
        /// Records a play event and updates revenue
        /// </summary>
        public void RecordPlay()
        {
            PlayCount++;
            LastPlayedAt = DateTime.UtcNow;
            TotalRevenue += CostPerPlay;
            CalculateNextScheduledPlay();
            UpdateTimestamp();
        }

        /// <summary>
        /// Calculates the next scheduled play time based on interval
        /// </summary>
        public void CalculateNextScheduledPlay()
        {
            if (ShouldPlay)
            {
                NextScheduledPlay = DateTime.UtcNow.AddMinutes(PlaybackInterval);
            }
            else
            {
                NextScheduledPlay = null;
            }
        }

        /// <summary>
        /// Approves the advertisement
        /// </summary>
        /// <param name="approvedBy">User who approved the advertisement</param>
        /// <param name="notes">Approval notes</param>
        public void Approve(string approvedBy, string? notes = null)
        {
            IsApproved = true;
            ApprovalStatus = "Approved";
            ApprovedBy = approvedBy;
            ApprovedAt = DateTime.UtcNow;
            ApprovalNotes = notes;
            CalculateNextScheduledPlay();
            UpdateTimestamp();
        }

        /// <summary>
        /// Rejects the advertisement
        /// </summary>
        /// <param name="rejectedBy">User who rejected the advertisement</param>
        /// <param name="reason">Rejection reason</param>
        public void Reject(string rejectedBy, string? reason = null)
        {
            IsApproved = false;
            ApprovalStatus = "Rejected";
            ApprovedBy = rejectedBy;
            ApprovedAt = DateTime.UtcNow;
            ApprovalNotes = reason;
            NextScheduledPlay = null;
            UpdateTimestamp();
        }

        /// <summary>
        /// Extends the campaign end date
        /// </summary>
        /// <param name="newEndDate">New end date</param>
        public void ExtendCampaign(DateTime newEndDate)
        {
            if (newEndDate > EndDate)
            {
                EndDate = newEndDate;
                CalculateNextScheduledPlay();
                UpdateTimestamp();
            }
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the advertisement has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Title) && 
                   !string.IsNullOrWhiteSpace(Advertiser) && 
                   StartDate <= EndDate && 
                   DurationSeconds > 0;
        }

        /// <summary>
        /// Gets a summary of the advertisement for display purposes
        /// </summary>
        /// <returns>Advertisement summary string</returns>
        public string GetSummary()
        {
            return $"Ad: {Title}, Advertiser: {Advertiser}, " +
                   $"Duration: {DurationFormatted}, Priority: {PriorityDescription}, " +
                   $"Plays: {PlayCount}, Revenue: {Currency} {TotalRevenue:F2}";
        }

        /// <summary>
        /// Returns a string representation of the advertisement
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{Title} - {Advertiser}";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current advertisement
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Advertisement other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the advertisement
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
