// Decompiled with JetBrains decompiler
// Type: ipis.frmContacts
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmContacts : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("RichTextBox1")]
  private RichTextBox _RichTextBox1;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("Label3")]
  private Label _Label3;

  [DebuggerNonUserCode]
  static frmContacts()
  {
  }

  [DebuggerNonUserCode]
  public frmContacts()
  {
    frmContacts.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmContacts.__ENCList)
    {
      if (frmContacts.__ENCList.Count == frmContacts.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmContacts.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmContacts.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmContacts.__ENCList[index1] = frmContacts.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmContacts.__ENCList.RemoveRange(index1, checked (frmContacts.__ENCList.Count - index1));
        frmContacts.__ENCList.Capacity = frmContacts.__ENCList.Count;
      }
      frmContacts.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmContacts));
    this.RichTextBox1 = new RichTextBox();
    this.Label1 = new Label();
    this.Label3 = new Label();
    this.SuspendLayout();
    this.RichTextBox1.BackColor = SystemColors.ButtonHighlight;
    this.RichTextBox1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RichTextBox richTextBox1_1 = this.RichTextBox1;
    Point point1 = new Point(-1, -3);
    Point point2 = point1;
    richTextBox1_1.Location = point2;
    this.RichTextBox1.Name = "RichTextBox1";
    RichTextBox richTextBox1_2 = this.RichTextBox1;
    Size size1 = new Size(343, 217);
    Size size2 = size1;
    richTextBox1_2.Size = size2;
    this.RichTextBox1.TabIndex = 4;
    this.RichTextBox1.Text = componentResourceManager.GetString("RichTextBox1.Text");
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(14, 236);
    Point point3 = point1;
    label1_1.Location = point3;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(59, 16 /*0x10*/);
    Size size3 = size1;
    label1_2.Size = size3;
    this.Label1.TabIndex = 5;
    this.Label1.Text = "E Mail :";
    this.Label3.AutoSize = true;
    this.Label3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label3.ForeColor = Color.Blue;
    Label label3_1 = this.Label3;
    point1 = new Point(86, 238);
    Point point4 = point1;
    label3_1.Location = point4;
    this.Label3.Name = "Label3";
    Label label3_2 = this.Label3;
    size1 = new Size(234, 16 /*0x10*/);
    Size size4 = size1;
    label3_2.Size = size4;
    this.Label3.TabIndex = 8;
    this.Label3.Text = "<EMAIL>";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(346, 286);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label3);
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.RichTextBox1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmContacts";
    this.Text = "Contacts";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual RichTextBox RichTextBox1
  {
    [DebuggerNonUserCode] get { return this._RichTextBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.RichTextBox1_TextChanged);
      if (this._RichTextBox1 != null)
        this._RichTextBox1.TextChanged -= eventHandler;
      this._RichTextBox1 = value;
      if (this._RichTextBox1 == null)
        return;
      this._RichTextBox1.TextChanged += eventHandler;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Label Label3
  {
    [DebuggerNonUserCode] get { return this._Label3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label3 = value; }
  }

  private void RichTextBox1_TextChanged(object sender, EventArgs e)
  {
  }
}

}