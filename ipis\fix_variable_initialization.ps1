# PowerShell script to fix unassigned variable issues

Write-Host "=== FIXING VARIABLE INITIALIZATION ISSUES ==="

# Fix specific files with known unassigned variable issues
$fixes = @{
    "adminDashboard.cs" = @(
        @{ Pattern = "IEnumerator enumerator;"; Replacement = "IEnumerator enumerator = null;" }
    )
    "voice_xml_files.cs" = @(
        @{ Pattern = "int index1;"; Replacement = "int index1 = 0;" }
        @{ Pattern = "int index3;"; Replacement = "int index3 = 0;" }
        @{ Pattern = "short int16_1;"; Replacement = "short int16_1 = 0;" }
        @{ Pattern = "short int16_2;"; Replacement = "short int16_2 = 0;" }
        @{ Pattern = "short int16_3;"; Replacement = "short int16_3 = 0;" }
    )
    "frmRecordPlay.cs" = @(
        @{ Pattern = "int num1;"; Replacement = "int num1 = 0;" }
    )
}

foreach ($fileName in $fixes.Keys) {
    if (Test-Path $fileName) {
        Write-Host "Processing: $fileName"
        $content = Get-Content $fileName -Raw
        
        foreach ($fix in $fixes[$fileName]) {
            if ($content -match [regex]::Escape($fix.Pattern)) {
                $content = $content -replace [regex]::Escape($fix.Pattern), $fix.Replacement
                Write-Host "  - Fixed: $($fix.Pattern)"
            }
        }
        
        Set-Content -Path $fileName -Value $content -NoNewline
    }
}

Write-Host "Variable initialization fixes complete!"
