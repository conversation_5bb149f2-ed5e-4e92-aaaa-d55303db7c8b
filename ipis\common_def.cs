// Decompiled with JetBrains decompiler
// Type: ipis.common_def
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Diagnostics;

namespace ipis
{

public class common_def
{
  public const byte ISON = 1;
  public const byte OFF = 0;
  public const byte NO_DIS_BRDS_PF = 26;
  public const byte NO_OF_RETRANSMISSIONS = 0;
  public const byte TRAIN_NO_LENGTH = 3;
  public const byte MAX_NO_DEFAULT_MSGS = 50;
  public const byte MAX_NO_USERS = 50;
  public const int MAX_MSG_PKT_LEN = 3999;
  public const byte NO_OF_PLATFORMS = 25;
  public const int MAX_NO_STATION_CODES = 600;
  public const byte NO_MSG_PER_BOARD = 250;
  public const int MAX_NO_TRAINS = 600;
  public const int NO_ONLINE_TRAIN_ROWS = 600;
  public const int MAX_NO_BOARDS = 1000;
  public const int MAX_DEF_MSG_LENGTH = 33;
  public const byte MAX_NO_LANGUAGES = 3;
  public const byte MAX_MDCH_PORTNOS = 16 /*0x10*/;
  public const byte MAX_SYSTEMS_PER_MDCH_PORT = 4;
  public const byte MAX_PDCH_PORTNOS = 16 /*0x10*/;
  public const byte MAX_SYSTEMS_PER_PDCH_PORT = 8;
  public const byte MAX_NO_SYS_IDS_PER_HUB = 20;
  public const byte NO_OF_MLDBS = 25;
  public const byte MAX_NO_MDLB_LINES = 10;
  public const byte MAX_MLDB_ADDR = 255 /*0xFF*/;
  public const int MLDB_DIS_MSG_LEN = 672;
  public const byte NO_OF_PDBS_PER_PF = 10;
  public const byte MAX_PDB_ADDR = 255 /*0xFF*/;
  public const int PDB_DIS_MSG_LEN = 672;
  public const byte NO_OF_AGDBS_PER_PF = 5;
  public const byte MAX_AGDB_ADDR = 255 /*0xFF*/;
  public const int AGDB_DIS_MSG_LEN = 240 /*0xF0*/;
  public const int AGDB_MSG_LEN = 719;
  public const byte NO_OF_CGDBS_PER_PF = 26;
  public const byte MAX_CGDB_ADDR = 255 /*0xFF*/;
  public const byte LC_FAILURE = 0;
  public const byte LC_SUCCESS = 1;
  public const byte LC_UNKNOWN = 2;
  public const byte MAX_NO_DAYS = 7;
  public const byte MAX_NO_SPECIFICDATES = 10;
  public const byte RC_CRC_FAIL = 9;
  public const byte RC_NO_MDCH = 8;
  public const byte RC_NO_PDCH = 7;
  public const byte RC_NO_CGDBS = 6;
  public const byte RC_NO_PDBS = 5;
  public const byte RC_NO_AGDBS = 5;
  public const byte RC_COM_FAIL = 6;
  public const byte RC_NO_PLATFORM = 4;
  public const byte COM_PORT_FAIL = 3;
  public const byte LINK_FAIL = 2;
  public const byte SUCCESS = 1;
  public const byte FAILURE = 0;
  public const int AGDB_PKT1_LEN = 28;
  public const int AGDB_PKT2_LEN = 24;
  public const int AGDB_PKT3_LEN = 24;
  public const int HINDI_TRAIN_NAME_LENGTH = 368;
  public const int HINDI_STATUS_NAME_LENGTH = 156;
  public const int HINDI_STATION_NAME_LENGTH = 368;
  public const int REG_LANG_TRAIN_NAME_LENGTH = 368;
  public const int REG_LANG_STATUS_NAME_LENGTH = 156;
  public const int REG_LANG_STATION_NAME_LENGTH = 368;
  public const int HINDI_ARR_HASLEFT_STATUS_NAME_LENGTH = 120;
  public const int REG_LANG_ARR_HASLEFT_STATUS_NAME_LENGTH = 120;
  public const int NORMAL_MSG_LENGTH = 769;
  public const int DIV_TER_MSG_LENGTH = 1849;
  public const int ARR_HASLEFT_MSG_LENGTH = 1094;
  public const int CANC_INDEF_MSG_LENGTH = 1094;
  public const int RESCHEDULE_MSG_LENGTH = 1094;
  public const int NUMBERCOMP = 48 /*0x30*/;
  public const int ALPHABETCOMP = 55;
  public const int ALPHABETSMALL = 97;
  public const int TRAINNO_COLUMN_NO = 1;
  public const int TRAINNAME_COLUMN_NO = 2;
  public const int ARRTIME_COLUMN_NO = 3;
  public const int DEPTIME_COLUMN_NO = 4;
  public const int AD_COLUMN_NO = 5;
  public const int STATUS_COLUMN_NO = 6;
  public const int LATE_COLUMN_NO = 7;
  public const int EXPARRTIME_COLUMN_NO = 8;
  public const int EXPDEPTIME_COLUMN_NO = 9;
  public const int PFNO_COLUMN_NO = 10;
  public const int TADBB_COLUMN_NO = 11;
  public const int CGSEDIT_COLUMN_NO = 12;
  public const int CGDB_COLUMN_NO = 13;
  public const int ANNOUNCE_COLUMN_NO = 14;
  public const int DELETE_COLUMN_NO = 15;

  [DebuggerNonUserCode]
  public common_def()
  {
  }
}

}