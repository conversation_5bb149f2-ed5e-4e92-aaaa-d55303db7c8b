# IPIS Technical Implementation Plan

## Overview

This document outlines the detailed technical implementation plan for modernizing the IPIS system using .NET Core 6 and Railway platform integration.

## Architecture Design

### 1. System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Railway Cloud Platform                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web App   │  │   API App   │  │  Worker App │         │
│  │  (Blazor)   │  │ (.NET Core) │  │ (Background)│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ PostgreSQL  │  │    Redis    │  │   Storage   │         │
│  │  Database   │  │   Cache     │  │   Service   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 2. Microservices Architecture

#### Core Services
1. **Authentication Service** - User management and security
2. **Train Data Service** - Real-time train information management
3. **Display Service** - Display board content management
4. **Announcement Service** - Voice announcement management
5. **Station Service** - Station and platform configuration
6. **Notification Service** - Real-time notifications via SignalR

#### Supporting Services
1. **API Gateway** - Request routing and rate limiting
2. **Configuration Service** - Centralized configuration management
3. **Logging Service** - Centralized logging and monitoring
4. **File Service** - File upload and management

### 3. Database Design

#### Core Tables
```sql
-- Stations
CREATE TABLE Stations (
    Id SERIAL PRIMARY KEY,
    Code VARCHAR(10) UNIQUE NOT NULL,
    Name VARCHAR(100) NOT NULL,
    Location VARCHAR(200),
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Platforms
CREATE TABLE Platforms (
    Id SERIAL PRIMARY KEY,
    StationId INTEGER REFERENCES Stations(Id),
    PlatformNumber VARCHAR(10) NOT NULL,
    PlatformName VARCHAR(50),
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Trains
CREATE TABLE Trains (
    Id SERIAL PRIMARY KEY,
    TrainNumber VARCHAR(20) UNIQUE NOT NULL,
    TrainName VARCHAR(100),
    TrainType VARCHAR(50),
    IsActive BOOLEAN DEFAULT true,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Schedules
CREATE TABLE Schedules (
    Id SERIAL PRIMARY KEY,
    TrainId INTEGER REFERENCES Trains(Id),
    StationId INTEGER REFERENCES Stations(Id),
    PlatformId INTEGER REFERENCES Platforms(Id),
    ScheduledArrival TIMESTAMP,
    ScheduledDeparture TIMESTAMP,
    ActualArrival TIMESTAMP,
    ActualDeparture TIMESTAMP,
    Status VARCHAR(20) DEFAULT 'Scheduled',
    DelayMinutes INTEGER DEFAULT 0,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Display Boards
CREATE TABLE DisplayBoards (
    Id SERIAL PRIMARY KEY,
    StationId INTEGER REFERENCES Stations(Id),
    BoardType VARCHAR(20) NOT NULL, -- AGDB, CGDB, MLDB, PDB
    BoardName VARCHAR(100),
    IpAddress VARCHAR(15),
    IsOnline BOOLEAN DEFAULT false,
    LastHeartbeat TIMESTAMP,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Messages
CREATE TABLE Messages (
    Id SERIAL PRIMARY KEY,
    DisplayBoardId INTEGER REFERENCES DisplayBoards(Id),
    MessageType VARCHAR(20), -- Train, Announcement, Emergency
    Content TEXT NOT NULL,
    Language VARCHAR(10) DEFAULT 'en',
    Priority INTEGER DEFAULT 1,
    IsActive BOOLEAN DEFAULT true,
    ValidFrom TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ValidTo TIMESTAMP,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users
CREATE TABLE Users (
    Id SERIAL PRIMARY KEY,
    Username VARCHAR(50) UNIQUE NOT NULL,
    Email VARCHAR(100) UNIQUE NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    FirstName VARCHAR(50),
    LastName VARCHAR(50),
    IsActive BOOLEAN DEFAULT true,
    LastLoginAt TIMESTAMP,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Roles
CREATE TABLE Roles (
    Id SERIAL PRIMARY KEY,
    Name VARCHAR(50) UNIQUE NOT NULL,
    Description VARCHAR(200),
    Permissions JSONB,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Roles
CREATE TABLE UserRoles (
    UserId INTEGER REFERENCES Users(Id),
    RoleId INTEGER REFERENCES Roles(Id),
    AssignedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (UserId, RoleId)
);
```

## Implementation Phases

### Phase 1: Foundation Setup (Weeks 1-2)

#### Week 1: Railway Platform Setup
```bash
# Railway project initialization
railway login
railway init ipis-system
railway add --database postgresql
railway add --database redis

# Environment setup
railway env set ASPNETCORE_ENVIRONMENT=Development
railway env set DATABASE_URL=${{Postgres.DATABASE_URL}}
railway env set REDIS_URL=${{Redis.REDIS_URL}}
```

#### Week 2: Project Structure
```
IPIS.Solution/
├── src/
│   ├── IPIS.Core/                 # Domain models and interfaces
│   ├── IPIS.Infrastructure/       # Data access and external services
│   ├── IPIS.Application/          # Business logic and services
│   ├── IPIS.API/                  # Web API project
│   ├── IPIS.Web/                  # Blazor web application
│   └── IPIS.Worker/               # Background services
├── tests/
│   ├── IPIS.UnitTests/
│   ├── IPIS.IntegrationTests/
│   └── IPIS.EndToEndTests/
├── docker/
│   ├── Dockerfile.api
│   ├── Dockerfile.web
│   └── Dockerfile.worker
└── .github/
    └── workflows/
        └── deploy.yml
```

### Phase 2: Core Services Development (Weeks 3-8)

#### Authentication Service Implementation
```csharp
// IPIS.Core/Entities/User.cs
public class User
{
    public int Id { get; set; }
    public string Username { get; set; }
    public string Email { get; set; }
    public string PasswordHash { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public ICollection<UserRole> UserRoles { get; set; }
}

// IPIS.Application/Services/AuthenticationService.cs
public class AuthenticationService : IAuthenticationService
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IJwtTokenGenerator _tokenGenerator;

    public async Task<AuthenticationResult> AuthenticateAsync(
        string username, string password)
    {
        var user = await _userRepository.GetByUsernameAsync(username);
        if (user == null || !_passwordHasher.Verify(password, user.PasswordHash))
        {
            return AuthenticationResult.Failed("Invalid credentials");
        }

        var token = _tokenGenerator.GenerateToken(user);
        return AuthenticationResult.Success(token, user);
    }
}
```

#### Train Data Service Implementation
```csharp
// IPIS.Core/Entities/Schedule.cs
public class Schedule
{
    public int Id { get; set; }
    public int TrainId { get; set; }
    public Train Train { get; set; }
    public int StationId { get; set; }
    public Station Station { get; set; }
    public int? PlatformId { get; set; }
    public Platform Platform { get; set; }
    public DateTime? ScheduledArrival { get; set; }
    public DateTime? ScheduledDeparture { get; set; }
    public DateTime? ActualArrival { get; set; }
    public DateTime? ActualDeparture { get; set; }
    public ScheduleStatus Status { get; set; }
    public int DelayMinutes { get; set; }
}

// IPIS.Application/Services/TrainDataService.cs
public class TrainDataService : ITrainDataService
{
    private readonly IScheduleRepository _scheduleRepository;
    private readonly INotificationService _notificationService;

    public async Task UpdateScheduleAsync(int scheduleId, UpdateScheduleRequest request)
    {
        var schedule = await _scheduleRepository.GetByIdAsync(scheduleId);
        if (schedule == null) throw new NotFoundException("Schedule not found");

        // Update schedule properties
        schedule.ActualArrival = request.ActualArrival;
        schedule.ActualDeparture = request.ActualDeparture;
        schedule.DelayMinutes = request.DelayMinutes;
        schedule.Status = request.Status;

        await _scheduleRepository.UpdateAsync(schedule);

        // Notify display boards and announcement systems
        await _notificationService.NotifyScheduleUpdateAsync(schedule);
    }
}
```

### Phase 3: Real-time Communication (Weeks 9-10)

#### SignalR Hub Implementation
```csharp
// IPIS.API/Hubs/IPISHub.cs
public class IPISHub : Hub
{
    public async Task JoinStationGroup(string stationCode)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Station_{stationCode}");
    }

    public async Task JoinDisplayBoardGroup(string boardId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Board_{boardId}");
    }
}

// IPIS.Application/Services/NotificationService.cs
public class NotificationService : INotificationService
{
    private readonly IHubContext<IPISHub> _hubContext;

    public async Task NotifyScheduleUpdateAsync(Schedule schedule)
    {
        var notification = new ScheduleUpdateNotification
        {
            ScheduleId = schedule.Id,
            TrainNumber = schedule.Train.TrainNumber,
            StationCode = schedule.Station.Code,
            Status = schedule.Status,
            DelayMinutes = schedule.DelayMinutes,
            UpdatedAt = DateTime.UtcNow
        };

        await _hubContext.Clients.Group($"Station_{schedule.Station.Code}")
            .SendAsync("ScheduleUpdated", notification);
    }
}
```

### Phase 4: Display Board Management (Weeks 11-12)

#### Display Board Service
```csharp
// IPIS.Application/Services/DisplayBoardService.cs
public class DisplayBoardService : IDisplayBoardService
{
    private readonly IDisplayBoardRepository _displayBoardRepository;
    private readonly IMessageRepository _messageRepository;

    public async Task<DisplayContent> GenerateDisplayContentAsync(
        int displayBoardId, string language = "en")
    {
        var board = await _displayBoardRepository.GetByIdAsync(displayBoardId);
        var schedules = await GetRelevantSchedulesAsync(board);
        var messages = await _messageRepository.GetActiveMessagesAsync(
            displayBoardId, language);

        return new DisplayContent
        {
            BoardType = board.BoardType,
            Schedules = schedules.Select(s => new DisplaySchedule
            {
                TrainNumber = s.Train.TrainNumber,
                Destination = s.Train.TrainName,
                Platform = s.Platform?.PlatformNumber,
                ScheduledTime = s.ScheduledDeparture ?? s.ScheduledArrival,
                ActualTime = s.ActualDeparture ?? s.ActualArrival,
                Status = s.Status,
                DelayMinutes = s.DelayMinutes
            }).ToList(),
            Messages = messages.Select(m => m.Content).ToList(),
            LastUpdated = DateTime.UtcNow
        };
    }
}
```

### Phase 5: Voice Announcement System (Weeks 13-14)

#### Announcement Service
```csharp
// IPIS.Application/Services/AnnouncementService.cs
public class AnnouncementService : IAnnouncementService
{
    private readonly ITextToSpeechService _ttsService;
    private readonly IAudioPlaybackService _audioService;
    private readonly IAnnouncementTemplateRepository _templateRepository;

    public async Task MakeAnnouncementAsync(AnnouncementRequest request)
    {
        var template = await _templateRepository.GetByTypeAsync(request.Type);
        var message = GenerateMessage(template, request.Parameters);

        if (request.UseTextToSpeech)
        {
            var audioData = await _ttsService.GenerateAudioAsync(
                message, request.Language, request.Voice);
            await _audioService.PlayAudioAsync(audioData, request.StationId);
        }
        else
        {
            var preRecordedFile = await GetPreRecordedFileAsync(
                request.Type, request.Language);
            await _audioService.PlayFileAsync(preRecordedFile, request.StationId);
        }

        // Log announcement
        await LogAnnouncementAsync(request, message);
    }

    private string GenerateMessage(AnnouncementTemplate template, 
        Dictionary<string, object> parameters)
    {
        var message = template.MessageTemplate;
        foreach (var param in parameters)
        {
            message = message.Replace($"{{{param.Key}}}", param.Value.ToString());
        }
        return message;
    }
}
```

## Railway Integration Implementation

### 1. Railway API Client
```csharp
// IPIS.Infrastructure/Railway/RailwayApiClient.cs
public class RailwayApiClient : IRailwayApiClient
{
    private readonly HttpClient _httpClient;
    private readonly RailwayConfiguration _config;

    public RailwayApiClient(HttpClient httpClient, 
        IOptions<RailwayConfiguration> config)
    {
        _httpClient = httpClient;
        _config = config.Value;
        
        _httpClient.DefaultRequestHeaders.Authorization = 
            new AuthenticationHeaderValue("Bearer", _config.ApiToken);
    }

    public async Task<DeploymentResult> TriggerDeploymentAsync(
        string serviceId, string environmentId)
    {
        var mutation = @"
            mutation DeployService($serviceId: String!, $environmentId: String!) {
                deploymentCreate(input: {
                    serviceId: $serviceId,
                    environmentId: $environmentId
                }) {
                    id
                    status
                    createdAt
                }
            }";

        var request = new GraphQLRequest
        {
            Query = mutation,
            Variables = new { serviceId, environmentId }
        };

        var response = await _httpClient.PostAsJsonAsync(
            "https://backboard.railway.com/graphql/v2", request);
        
        return await response.Content.ReadFromJsonAsync<DeploymentResult>();
    }
}
```

### 2. Deployment Automation
```yaml
# .github/workflows/deploy.yml
name: Deploy to Railway

on:
  push:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '6.0.x'
      - name: Run tests
        run: dotnet test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Railway
        uses: railway/cli@v2
        with:
          command: up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

### 3. Configuration Management
```csharp
// IPIS.API/Program.cs
var builder = WebApplication.CreateBuilder(args);

// Railway configuration
builder.Configuration.AddEnvironmentVariables();

// Database configuration
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") 
    ?? Environment.GetEnvironmentVariable("DATABASE_URL");

builder.Services.AddDbContext<IPISDbContext>(options =>
    options.UseNpgsql(connectionString));

// Redis configuration
var redisConnectionString = Environment.GetEnvironmentVariable("REDIS_URL");
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConnectionString;
});

// Railway API configuration
builder.Services.Configure<RailwayConfiguration>(
    builder.Configuration.GetSection("Railway"));

var app = builder.Build();

// Auto-migrate database on startup
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<IPISDbContext>();
    await context.Database.MigrateAsync();
}

app.Run();
```

## Testing Strategy

### 1. Unit Testing
```csharp
// IPIS.UnitTests/Services/TrainDataServiceTests.cs
public class TrainDataServiceTests
{
    private readonly Mock<IScheduleRepository> _mockRepository;
    private readonly Mock<INotificationService> _mockNotificationService;
    private readonly TrainDataService _service;

    [Test]
    public async Task UpdateScheduleAsync_ValidRequest_UpdatesSchedule()
    {
        // Arrange
        var schedule = new Schedule { Id = 1, DelayMinutes = 0 };
        _mockRepository.Setup(r => r.GetByIdAsync(1))
            .ReturnsAsync(schedule);

        var request = new UpdateScheduleRequest
        {
            DelayMinutes = 15,
            Status = ScheduleStatus.Delayed
        };

        // Act
        await _service.UpdateScheduleAsync(1, request);

        // Assert
        Assert.AreEqual(15, schedule.DelayMinutes);
        Assert.AreEqual(ScheduleStatus.Delayed, schedule.Status);
        _mockRepository.Verify(r => r.UpdateAsync(schedule), Times.Once);
    }
}
```

### 2. Integration Testing
```csharp
// IPIS.IntegrationTests/API/ScheduleControllerTests.cs
public class ScheduleControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ScheduleControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Test]
    public async Task GetSchedules_ReturnsScheduleList()
    {
        // Act
        var response = await _client.GetAsync("/api/schedules");
        var content = await response.Content.ReadAsStringAsync();
        var schedules = JsonSerializer.Deserialize<List<Schedule>>(content);

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.IsNotNull(schedules);
    }
}
```

## Monitoring and Observability

### 1. Application Insights Integration
```csharp
// IPIS.API/Program.cs
builder.Services.AddApplicationInsightsTelemetry();

// Custom telemetry
builder.Services.AddSingleton<ITelemetryInitializer, 
    CustomTelemetryInitializer>();
```

### 2. Health Checks
```csharp
// IPIS.API/Program.cs
builder.Services.AddHealthChecks()
    .AddNpgSql(connectionString)
    .AddRedis(redisConnectionString)
    .AddCheck<RailwayApiHealthCheck>("railway-api");

app.MapHealthChecks("/health");
```

This technical implementation plan provides a comprehensive roadmap for modernizing the IPIS system with Railway platform integration, ensuring scalability, maintainability, and modern development practices.
