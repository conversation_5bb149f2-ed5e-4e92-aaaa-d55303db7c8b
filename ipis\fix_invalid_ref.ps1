# Fix all invalid standalone ref expressions

$files = @(
    "CCTV_NewForm.cs",
    "frmAddVoiceSplMsg.cs", 
    "frmStationCode.cs",
    "frmStationDetails.cs",
    "frmStationNameVoice.cs",
    "frmTrainstatusPopup.cs",
    "frmLogin.cs",
    "RS232.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing invalid ref in: $file"
        $content = Get-Content $file -Raw
        
        # Remove standalone ref statements that cause syntax errors
        $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;\s*\n', "`n"
        $content = $content -replace '\n\s*ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;\s*\n', "`n"
        
        Set-Content -Path $file -Value $content -NoNewline
        Write-Host "  - Fixed invalid ref expressions"
    }
}

Write-Host "Invalid ref expression fixes complete!"
