using NUnit.Framework;
using FluentAssertions;
using AutoFixture;
using AutoFixture.NUnit3;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Tests.UnitTests.Models.Entities
{
    /// <summary>
    /// Comprehensive test suite for DisplayBoard entity
    /// Tests every line of code and business logic - critical for IPIS display operations
    /// </summary>
    [TestFixture]
    public class DisplayBoardTests
    {
        private IFixture _fixture;

        [SetUp]
        public void SetUp()
        {
            _fixture = new Fixture();
            _fixture.Customize<DisplayBoard>(c => c
                .With(db => db.StationId, 1)
                .With(db => db.BoardName, "Test Board")
                .With(db => db.BoardType, DisplayBoardType.AGDB)
                .With(db => db.Protocol, CommunicationProtocol.TCP)
                .With(db => db.IsActive, true));
        }

        #region Constructor and Property Tests

        [Test]
        public void DisplayBoard_DefaultConstructor_SetsDefaultValues()
        {
            // Act
            var displayBoard = new DisplayBoard();

            // Assert
            displayBoard.Id.Should().Be(0);
            displayBoard.StationId.Should().Be(0);
            displayBoard.BoardName.Should().Be(string.Empty);
            displayBoard.Protocol.Should().Be(CommunicationProtocol.TCP);
            displayBoard.IsOnline.Should().BeFalse();
            displayBoard.DisplayLanguages.Should().Be("en");
            displayBoard.RefreshInterval.Should().Be(30);
            displayBoard.MaxMessageLength.Should().Be(240);
            displayBoard.DisplayLines.Should().Be(1);
            displayBoard.Brightness.Should().Be(80);
            displayBoard.SupportsScrolling.Should().BeTrue();
            displayBoard.ScrollSpeed.Should().Be(5);
            displayBoard.FontSize.Should().Be(12);
            displayBoard.FontName.Should().Be("Arial");
            displayBoard.IsActive.Should().BeTrue();
            displayBoard.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            displayBoard.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            displayBoard.Messages.Should().NotBeNull().And.BeEmpty();
        }

        [Test]
        [AutoData]
        public void DisplayBoard_SetProperties_SetsCorrectly(int stationId, string boardName, string ipAddress, int port)
        {
            // Arrange
            var displayBoard = new DisplayBoard();
            var boardType = DisplayBoardType.CGDB;
            var protocol = CommunicationProtocol.UDP;

            // Act
            displayBoard.StationId = stationId;
            displayBoard.BoardName = boardName;
            displayBoard.BoardType = boardType;
            displayBoard.IpAddress = ipAddress;
            displayBoard.Port = port;
            displayBoard.Protocol = protocol;
            displayBoard.IsOnline = true;
            displayBoard.IsActive = false;

            // Assert
            displayBoard.StationId.Should().Be(stationId);
            displayBoard.BoardName.Should().Be(boardName);
            displayBoard.BoardType.Should().Be(boardType);
            displayBoard.IpAddress.Should().Be(ipAddress);
            displayBoard.Port.Should().Be(port);
            displayBoard.Protocol.Should().Be(protocol);
            displayBoard.IsOnline.Should().BeTrue();
            displayBoard.IsActive.Should().BeFalse();
        }

        #endregion

        #region Validation Tests

        [Test]
        public void DisplayBoard_ValidData_PassesValidation()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IpAddress = "*************";
            displayBoard.Port = 8080;
            var validationContext = new ValidationContext(displayBoard);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(displayBoard, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeTrue();
            validationResults.Should().BeEmpty();
        }

        [Test]
        [TestCase("", "Board name is required")]
        [TestCase(null, "Board name is required")]
        public void DisplayBoard_InvalidBoardName_FailsValidation(string boardName, string expectedError)
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.BoardName = boardName;
            var validationContext = new ValidationContext(displayBoard);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(displayBoard, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        [TestCase("192.168.1.256", "Invalid IP address format")]
        [TestCase("not.an.ip", "Invalid IP address format")]
        [TestCase("192.168.1", "Invalid IP address format")]
        public void DisplayBoard_InvalidIpAddress_FailsValidation(string ipAddress, string expectedError)
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IpAddress = ipAddress;
            var validationContext = new ValidationContext(displayBoard);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(displayBoard, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        [TestCase(0, "Port must be between 1 and 65535")]
        [TestCase(65536, "Port must be between 1 and 65535")]
        public void DisplayBoard_InvalidPort_FailsValidation(int port, string expectedError)
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.Port = port;
            var validationContext = new ValidationContext(displayBoard);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(displayBoard, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        #endregion

        #region Computed Properties Tests

        [Test]
        public void DisplayName_WithBoardTypeAndName_ReturnsFormattedString()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.BoardType = DisplayBoardType.AGDB;
            displayBoard.BoardName = "Main Board";

            // Act & Assert
            displayBoard.DisplayName.Should().Be("AGDB - Main Board");
        }

        [Test]
        public void FullIdentifier_WithStation_ReturnsFormattedString()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.BoardType = DisplayBoardType.PDB;
            displayBoard.BoardName = "Platform Board";
            displayBoard.Station = new Station { Code = "NYC" };

            // Act & Assert
            displayBoard.FullIdentifier.Should().Be("NYC-PDB-Platform Board");
        }

        [Test]
        public void ConnectionStatus_WhenOnline_ReturnsOnline()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = true;

            // Act & Assert
            displayBoard.ConnectionStatus.Should().Be("Online");
        }

        [Test]
        public void ConnectionStatus_WhenOffline_ReturnsOffline()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = false;

            // Act & Assert
            displayBoard.ConnectionStatus.Should().Be("Offline");
        }

        [Test]
        public void NetworkEndpoint_WithIpAndPort_ReturnsFormattedString()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IpAddress = "*************";
            displayBoard.Port = 8080;

            // Act & Assert
            displayBoard.NetworkEndpoint.Should().Be("*************:8080");
        }

        [Test]
        public void NetworkEndpoint_WithoutIpOrPort_ReturnsNotConfigured()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IpAddress = null;
            displayBoard.Port = null;

            // Act & Assert
            displayBoard.NetworkEndpoint.Should().Be("Not configured");
        }

        [Test]
        public void TimeSinceLastHeartbeat_WithHeartbeat_ReturnsTimeSpan()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.LastHeartbeat = DateTime.UtcNow.AddMinutes(-5);

            // Act
            var timeSince = displayBoard.TimeSinceLastHeartbeat;

            // Assert
            timeSince.Should().NotBeNull();
            timeSince!.Value.TotalMinutes.Should().BeApproximately(5, 0.1);
        }

        [Test]
        public void TimeSinceLastHeartbeat_WithoutHeartbeat_ReturnsNull()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.LastHeartbeat = null;

            // Act & Assert
            displayBoard.TimeSinceLastHeartbeat.Should().BeNull();
        }

        [Test]
        public void IsHealthy_WhenOnlineWithRecentHeartbeat_ReturnsTrue()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = true;
            displayBoard.LastHeartbeat = DateTime.UtcNow.AddMinutes(-2);

            // Act & Assert
            displayBoard.IsHealthy.Should().BeTrue();
        }

        [Test]
        public void IsHealthy_WhenOffline_ReturnsFalse()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = false;
            displayBoard.LastHeartbeat = DateTime.UtcNow.AddMinutes(-2);

            // Act & Assert
            displayBoard.IsHealthy.Should().BeFalse();
        }

        [Test]
        public void IsHealthy_WhenOnlineWithOldHeartbeat_ReturnsFalse()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = true;
            displayBoard.LastHeartbeat = DateTime.UtcNow.AddMinutes(-10);

            // Act & Assert
            displayBoard.IsHealthy.Should().BeFalse();
        }

        [Test]
        public void SupportedLanguages_WithMultipleLanguages_ReturnsCorrectList()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.DisplayLanguages = "en,hi,regional";

            // Act
            var languages = displayBoard.SupportedLanguages;

            // Assert
            languages.Should().HaveCount(3);
            languages.Should().Contain("en");
            languages.Should().Contain("hi");
            languages.Should().Contain("regional");
        }

        #endregion

        #region Method Tests

        [Test]
        public void UpdateHeartbeat_WhenCalled_UpdatesHeartbeatAndStatus()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = false;
            displayBoard.LastHeartbeat = null;
            var originalTimestamp = displayBoard.UpdatedAt;
            Thread.Sleep(10);

            // Act
            displayBoard.UpdateHeartbeat();

            // Assert
            displayBoard.IsOnline.Should().BeTrue();
            displayBoard.LastHeartbeat.Should().NotBeNull();
            displayBoard.LastHeartbeat.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            displayBoard.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void MarkOffline_WhenCalled_SetsOfflineStatus()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.IsOnline = true;
            var originalTimestamp = displayBoard.UpdatedAt;
            Thread.Sleep(10);

            // Act
            displayBoard.MarkOffline();

            // Assert
            displayBoard.IsOnline.Should().BeFalse();
            displayBoard.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void UpdateTimestamp_WhenCalled_UpdatesTimestamp()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            var originalTimestamp = displayBoard.UpdatedAt;
            Thread.Sleep(10);

            // Act
            displayBoard.UpdateTimestamp();

            // Assert
            displayBoard.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void UpdateTimestamp_WithUser_UpdatesTimestampAndUser()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            var originalTimestamp = displayBoard.UpdatedAt;
            var updatedBy = "testuser";
            Thread.Sleep(10);

            // Act
            displayBoard.UpdateTimestamp(updatedBy);

            // Assert
            displayBoard.UpdatedAt.Should().BeAfter(originalTimestamp);
            displayBoard.UpdatedBy.Should().Be(updatedBy);
        }

        [Test]
        public void IsValid_WithValidData_ReturnsTrue()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.StationId = 1;
            displayBoard.BoardName = "Test Board";

            // Act & Assert
            displayBoard.IsValid().Should().BeTrue();
        }

        [Test]
        [TestCase(0, "Test Board", false)]
        [TestCase(1, "", false)]
        [TestCase(1, null, false)]
        [TestCase(1, "Test Board", true)]
        public void IsValid_WithDifferentData_ReturnsExpectedResult(int stationId, string boardName, bool expected)
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.StationId = stationId;
            displayBoard.BoardName = boardName;

            // Act & Assert
            displayBoard.IsValid().Should().Be(expected);
        }

        [Test]
        public void SupportsLanguage_WithSupportedLanguage_ReturnsTrue()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.DisplayLanguages = "en,hi,regional";

            // Act & Assert
            displayBoard.SupportsLanguage("hi").Should().BeTrue();
            displayBoard.SupportsLanguage("HI").Should().BeTrue(); // Case insensitive
        }

        [Test]
        public void SupportsLanguage_WithUnsupportedLanguage_ReturnsFalse()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.DisplayLanguages = "en,hi";

            // Act & Assert
            displayBoard.SupportsLanguage("fr").Should().BeFalse();
        }

        [Test]
        [TestCase(DisplayBoardType.AGDB, 240)]
        [TestCase(DisplayBoardType.CGDB, 200)]
        [TestCase(DisplayBoardType.MLDB, 160)]
        [TestCase(DisplayBoardType.PDB, 120)]
        [TestCase(DisplayBoardType.PDCH, 100)]
        public void GetMaxMessageLength_WithDifferentBoardTypes_ReturnsCorrectLength(DisplayBoardType boardType, int expectedLength)
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.BoardType = boardType;

            // Act
            var maxLength = displayBoard.GetMaxMessageLength();

            // Assert
            maxLength.Should().Be(expectedLength);
        }

        [Test]
        public void GetSummary_WithCompleteData_ReturnsFormattedSummary()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.BoardType = DisplayBoardType.AGDB;
            displayBoard.BoardName = "Main Board";
            displayBoard.IsOnline = true;
            displayBoard.BoardLocation = "Platform 1";
            displayBoard.IpAddress = "*************";
            displayBoard.Port = 8080;

            // Act
            var summary = displayBoard.GetSummary();

            // Assert
            summary.Should().Contain("AGDB - Main Board");
            summary.Should().Contain("Online");
            summary.Should().Contain("Platform 1");
            summary.Should().Contain("*************:8080");
        }

        [Test]
        public void ToString_ReturnsDisplayName()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.BoardType = DisplayBoardType.PDB;
            displayBoard.BoardName = "Platform Board";

            // Act
            var result = displayBoard.ToString();

            // Assert
            result.Should().Be("PDB - Platform Board");
        }

        #endregion

        #region Equality Tests

        [Test]
        public void Equals_WithSameIdStationIdAndBoardName_ReturnsTrue()
        {
            // Arrange
            var displayBoard1 = _fixture.Create<DisplayBoard>();
            displayBoard1.Id = 1;
            displayBoard1.StationId = 1;
            displayBoard1.BoardName = "Test Board";

            var displayBoard2 = _fixture.Create<DisplayBoard>();
            displayBoard2.Id = 1;
            displayBoard2.StationId = 1;
            displayBoard2.BoardName = "Test Board";

            // Act & Assert
            displayBoard1.Equals(displayBoard2).Should().BeTrue();
        }

        [Test]
        public void Equals_WithDifferentId_ReturnsFalse()
        {
            // Arrange
            var displayBoard1 = _fixture.Create<DisplayBoard>();
            displayBoard1.Id = 1;
            displayBoard1.StationId = 1;
            displayBoard1.BoardName = "Test Board";

            var displayBoard2 = _fixture.Create<DisplayBoard>();
            displayBoard2.Id = 2;
            displayBoard2.StationId = 1;
            displayBoard2.BoardName = "Test Board";

            // Act & Assert
            displayBoard1.Equals(displayBoard2).Should().BeFalse();
        }

        [Test]
        public void Equals_WithNull_ReturnsFalse()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();

            // Act & Assert
            displayBoard.Equals(null).Should().BeFalse();
        }

        [Test]
        public void GetHashCode_WithSameData_ReturnsSameHashCode()
        {
            // Arrange
            var displayBoard1 = _fixture.Create<DisplayBoard>();
            displayBoard1.Id = 1;
            displayBoard1.StationId = 1;
            displayBoard1.BoardName = "TEST BOARD";

            var displayBoard2 = _fixture.Create<DisplayBoard>();
            displayBoard2.Id = 1;
            displayBoard2.StationId = 1;
            displayBoard2.BoardName = "test board"; // Different case

            // Act & Assert
            displayBoard1.GetHashCode().Should().Be(displayBoard2.GetHashCode());
        }

        #endregion

        #region Edge Cases and Real-time Scenarios

        [Test]
        public void DisplayBoard_RealTimeHeartbeatScenario_HandlesCorrectly()
        {
            // Arrange - Simulate a real display board scenario
            var displayBoard = new DisplayBoard
            {
                StationId = 1,
                BoardName = "Platform 1 Board",
                BoardType = DisplayBoardType.PDB,
                IpAddress = "*************",
                Port = 8080,
                IsOnline = false
            };

            // Act - Simulate heartbeat updates
            displayBoard.UpdateHeartbeat();
            Thread.Sleep(100);
            displayBoard.UpdateHeartbeat();

            // Assert
            displayBoard.IsOnline.Should().BeTrue();
            displayBoard.IsHealthy.Should().BeTrue();
            displayBoard.LastHeartbeat.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Test]
        public void DisplayBoard_WithNullNavigationProperties_ShouldHandleGracefully()
        {
            // Arrange
            var displayBoard = _fixture.Create<DisplayBoard>();
            displayBoard.Station = null!;

            // Act & Assert - Should not throw exceptions
            displayBoard.FullIdentifier.Should().Contain("-");
        }

        #endregion
    }
}
