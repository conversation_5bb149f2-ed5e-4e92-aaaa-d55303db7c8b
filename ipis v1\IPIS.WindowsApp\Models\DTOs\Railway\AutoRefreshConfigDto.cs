using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for auto-refresh configuration
    /// </summary>
    public class AutoRefreshConfigDto
    {
        /// <summary>
        /// Unique identifier for the configuration
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Type of entity (Station or Train)
        /// </summary>
        [Required]
        public string EntityType { get; set; } = string.Empty; // "Station" or "Train"

        /// <summary>
        /// Entity identifier (station code or train number)
        /// </summary>
        [Required]
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Whether auto-refresh is enabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Refresh interval in minutes
        /// </summary>
        [Range(1, 1440)] // 1 minute to 24 hours
        public int IntervalMinutes { get; set; } = 15;

        /// <summary>
        /// Hours before train arrival/departure to start monitoring
        /// </summary>
        [Range(1, 24)]
        public int StartHoursBefore { get; set; } = 2;

        /// <summary>
        /// Hours after train arrival/departure to stop monitoring
        /// </summary>
        [Range(0, 12)]
        public int StopHoursAfter { get; set; } = 1;

        /// <summary>
        /// Days of week when auto-refresh is active (comma-separated)
        /// </summary>
        public string? ActiveDays { get; set; } = "Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday";

        /// <summary>
        /// Start time of day for auto-refresh (24-hour format)
        /// </summary>
        public TimeSpan? StartTime { get; set; }

        /// <summary>
        /// End time of day for auto-refresh (24-hour format)
        /// </summary>
        public TimeSpan? EndTime { get; set; }

        /// <summary>
        /// Priority level (1-10, higher is more important)
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Maximum number of consecutive failures before disabling
        /// </summary>
        [Range(1, 20)]
        public int MaxFailures { get; set; } = 5;

        /// <summary>
        /// Current failure count
        /// </summary>
        public int CurrentFailures { get; set; }

        /// <summary>
        /// Last successful refresh timestamp
        /// </summary>
        public DateTime? LastSuccessfulRefresh { get; set; }

        /// <summary>
        /// Last failed refresh timestamp
        /// </summary>
        public DateTime? LastFailedRefresh { get; set; }

        /// <summary>
        /// Next scheduled refresh timestamp
        /// </summary>
        public DateTime? NextScheduledRefresh { get; set; }

        /// <summary>
        /// Configuration created timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Configuration last updated timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the configuration
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// Additional configuration notes
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Whether to refresh only during specific train schedules
        /// </summary>
        public bool RefreshOnlyDuringSchedules { get; set; } = true;

        /// <summary>
        /// Whether to send notifications on refresh failures
        /// </summary>
        public bool NotifyOnFailure { get; set; } = true;

        /// <summary>
        /// Whether to automatically retry failed refreshes
        /// </summary>
        public bool AutoRetryOnFailure { get; set; } = true;

        /// <summary>
        /// Retry interval in minutes for failed refreshes
        /// </summary>
        [Range(1, 60)]
        public int RetryIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        [Range(1, 10)]
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Current retry attempt count
        /// </summary>
        public int CurrentRetryAttempts { get; set; }

        /// <summary>
        /// Whether the configuration is currently active
        /// </summary>
        public bool IsActive
        {
            get
            {
                if (!IsEnabled) return false;
                if (CurrentFailures >= MaxFailures) return false;
                
                var now = DateTime.Now;
                
                // Check if current day is active
                if (!string.IsNullOrEmpty(ActiveDays))
                {
                    var activeDays = ActiveDays.Split(',').Select(d => d.Trim()).ToList();
                    if (!activeDays.Contains(now.DayOfWeek.ToString()))
                        return false;
                }
                
                // Check if current time is within active hours
                if (StartTime.HasValue && EndTime.HasValue)
                {
                    var currentTime = now.TimeOfDay;
                    if (StartTime.Value <= EndTime.Value)
                    {
                        // Same day range
                        if (currentTime < StartTime.Value || currentTime > EndTime.Value)
                            return false;
                    }
                    else
                    {
                        // Overnight range
                        if (currentTime < StartTime.Value && currentTime > EndTime.Value)
                            return false;
                    }
                }
                
                return true;
            }
        }

        /// <summary>
        /// Status description
        /// </summary>
        public string Status
        {
            get
            {
                if (!IsEnabled) return "Disabled";
                if (CurrentFailures >= MaxFailures) return "Failed";
                if (!IsActive) return "Inactive";
                return "Active";
            }
        }

        /// <summary>
        /// Next refresh time description
        /// </summary>
        public string NextRefreshDescription
        {
            get
            {
                if (!IsActive) return "Not scheduled";
                if (NextScheduledRefresh.HasValue)
                {
                    var diff = NextScheduledRefresh.Value - DateTime.Now;
                    if (diff.TotalMinutes < 1) return "Due now";
                    if (diff.TotalHours < 1) return $"In {(int)diff.TotalMinutes} minutes";
                    return $"In {(int)diff.TotalHours} hours {(int)diff.Minutes} minutes";
                }
                return "Not scheduled";
            }
        }
    }
}
