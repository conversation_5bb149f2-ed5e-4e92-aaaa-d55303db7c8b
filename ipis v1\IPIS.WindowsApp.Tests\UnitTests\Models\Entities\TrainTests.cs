using NUnit.Framework;
using FluentAssertions;
using AutoFixture;
using AutoFixture.NUnit3;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Tests.UnitTests.Models.Entities
{
    /// <summary>
    /// Comprehensive test suite for Train entity
    /// Tests every line of code and business logic
    /// </summary>
    [TestFixture]
    public class TrainTests
    {
        private IFixture _fixture;

        [SetUp]
        public void SetUp()
        {
            _fixture = new Fixture();
            _fixture.Customize<Train>(c => c
                .With(t => t.TrainNumber, "12345")
                .With(t => t.TrainType, TrainType.Express)
                .With(t => t.IsActive, true));
        }

        #region Constructor and Property Tests

        [Test]
        public void Train_DefaultConstructor_SetsDefaultValues()
        {
            // Act
            var train = new Train();

            // Assert
            train.Id.Should().Be(0);
            train.TrainNumber.Should().Be(string.Empty);
            train.TrainType.Should().Be(TrainType.Passenger);
            train.IsActive.Should().BeTrue();
            train.HasPantryCar.Should().BeFalse();
            train.HasACCoaches.Should().BeFalse();
            train.HasSleeperCoaches.Should().BeFalse();
            train.HasGeneralCoaches.Should().BeTrue();
            train.HasLadiesCompartment.Should().BeFalse();
            train.HasDisabledFacilities.Should().BeFalse();
            train.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            train.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            train.Schedules.Should().NotBeNull().And.BeEmpty();
            train.VoiceFiles.Should().NotBeNull().And.BeEmpty();
        }

        [Test]
        [AutoData]
        public void Train_SetProperties_SetsCorrectly(string trainNumber, string trainName, string operatorCode)
        {
            // Arrange
            var train = new Train();
            var trainType = TrainType.Rajdhani;

            // Act
            train.TrainNumber = trainNumber;
            train.TrainName = trainName;
            train.OperatorCode = operatorCode;
            train.TrainType = trainType;
            train.IsActive = false;
            train.HasPantryCar = true;
            train.HasACCoaches = true;

            // Assert
            train.TrainNumber.Should().Be(trainNumber);
            train.TrainName.Should().Be(trainName);
            train.OperatorCode.Should().Be(operatorCode);
            train.TrainType.Should().Be(trainType);
            train.IsActive.Should().BeFalse();
            train.HasPantryCar.Should().BeTrue();
            train.HasACCoaches.Should().BeTrue();
        }

        #endregion

        #region Validation Tests

        [Test]
        public void Train_ValidData_PassesValidation()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            var validationContext = new ValidationContext(train);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(train, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeTrue();
            validationResults.Should().BeEmpty();
        }

        [Test]
        [TestCase("", "Train number is required")]
        [TestCase(null, "Train number is required")]
        public void Train_InvalidTrainNumber_FailsValidation(string trainNumber, string expectedError)
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = trainNumber;
            var validationContext = new ValidationContext(train);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(train, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage == expectedError);
        }

        [Test]
        public void Train_TrainNumberTooLong_FailsValidation()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = new string('1', 21); // 21 characters, max is 20
            var validationContext = new ValidationContext(train);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(train, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("cannot exceed 20 characters"));
        }

        [Test]
        public void Train_TrainNameTooLong_FailsValidation()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainName = new string('A', 101); // 101 characters, max is 100
            var validationContext = new ValidationContext(train);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(train, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeFalse();
            validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("cannot exceed 100 characters"));
        }

        #endregion

        #region Computed Properties Tests

        [Test]
        public void DisplayName_WithTrainName_ReturnsFormattedString()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = "12345";
            train.TrainName = "Rajdhani Express";

            // Act & Assert
            train.DisplayName.Should().Be("12345 - Rajdhani Express");
        }

        [Test]
        public void DisplayName_WithoutTrainName_ReturnsTrainNumber()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = "12345";
            train.TrainName = null;

            // Act & Assert
            train.DisplayName.Should().Be("12345");
        }

        [Test]
        public void DisplayName_WithEmptyTrainName_ReturnsTrainNumber()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = "12345";
            train.TrainName = "";

            // Act & Assert
            train.DisplayName.Should().Be("12345");
        }

        [Test]
        public void StatusDescription_WhenActive_ReturnsActive()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.IsActive = true;

            // Act & Assert
            train.StatusDescription.Should().Be("Active");
        }

        [Test]
        public void StatusDescription_WhenInactive_ReturnsInactive()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.IsActive = false;

            // Act & Assert
            train.StatusDescription.Should().Be("Inactive");
        }

        [Test]
        public void RouteDescription_WithRoute_ReturnsRoute()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.Route = "Delhi - Mumbai via Jaipur";

            // Act & Assert
            train.RouteDescription.Should().Be("Delhi - Mumbai via Jaipur");
        }

        [Test]
        public void RouteDescription_WithoutRoute_ReturnsSourceToDestination()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.Route = null;
            train.SourceStation = "DEL";
            train.DestinationStation = "BOM";

            // Act & Assert
            train.RouteDescription.Should().Be("DEL to BOM");
        }

        [Test]
        public void ActiveScheduleCount_WithMixedSchedules_ReturnsCorrectCount()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            var schedules = new List<Schedule>
            {
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.Scheduled).Create(),
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.Cancelled).Create(),
                _fixture.Build<Schedule>().With(s => s.Status, ScheduleStatus.OnTime).Create()
            };
            train.Schedules = schedules;

            // Act & Assert
            train.ActiveScheduleCount.Should().Be(2); // Scheduled and OnTime
        }

        [Test]
        public void AvailableFacilities_WithAllFacilities_ReturnsAllFacilities()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.HasPantryCar = true;
            train.HasACCoaches = true;
            train.HasSleeperCoaches = true;
            train.HasGeneralCoaches = true;
            train.HasLadiesCompartment = true;
            train.HasDisabledFacilities = true;

            // Act
            var facilities = train.AvailableFacilities;

            // Assert
            facilities.Should().Contain("Pantry Car");
            facilities.Should().Contain("AC Coaches");
            facilities.Should().Contain("Sleeper Coaches");
            facilities.Should().Contain("General Coaches");
            facilities.Should().Contain("Ladies Compartment");
            facilities.Should().Contain("Disabled Facilities");
            facilities.Should().HaveCount(6);
        }

        [Test]
        public void AvailableFacilities_WithNoFacilities_ReturnsEmptyList()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.HasPantryCar = false;
            train.HasACCoaches = false;
            train.HasSleeperCoaches = false;
            train.HasGeneralCoaches = false;
            train.HasLadiesCompartment = false;
            train.HasDisabledFacilities = false;

            // Act
            var facilities = train.AvailableFacilities;

            // Assert
            facilities.Should().BeEmpty();
        }

        #endregion

        #region Method Tests

        [Test]
        public void UpdateTimestamp_WhenCalled_UpdatesTimestamp()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            var originalTimestamp = train.UpdatedAt;
            Thread.Sleep(10); // Ensure time difference

            // Act
            train.UpdateTimestamp();

            // Assert
            train.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void UpdateTimestamp_WithUser_UpdatesTimestampAndUser()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            var originalTimestamp = train.UpdatedAt;
            var updatedBy = "testuser";
            Thread.Sleep(10); // Ensure time difference

            // Act
            train.UpdateTimestamp(updatedBy);

            // Assert
            train.UpdatedAt.Should().BeAfter(originalTimestamp);
            train.UpdatedBy.Should().Be(updatedBy);
        }

        [Test]
        public void IsValid_WithValidData_ReturnsTrue()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = "12345";

            // Act & Assert
            train.IsValid().Should().BeTrue();
        }

        [Test]
        [TestCase("", false)]
        [TestCase(null, false)]
        [TestCase("VERYLONGTRAINNUMBERTHATEXCEEDSLIMIT", false)] // Too long
        [TestCase("12345", true)]
        public void IsValid_WithDifferentTrainNumbers_ReturnsExpectedResult(string trainNumber, bool expected)
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = trainNumber;

            // Act & Assert
            train.IsValid().Should().Be(expected);
        }

        [Test]
        [TestCase(DayOfWeek.Monday, "Daily", true)]
        [TestCase(DayOfWeek.Tuesday, "Mon,Wed,Fri", false)]
        [TestCase(DayOfWeek.Monday, "Mon,Wed,Fri", true)]
        [TestCase(DayOfWeek.Sunday, "Mon,Wed,Fri", false)]
        [TestCase(DayOfWeek.Friday, "", true)] // Empty means daily
        [TestCase(DayOfWeek.Saturday, null, true)] // Null means daily
        public void OperatesOnDay_WithDifferentOperatingDays_ReturnsExpectedResult(DayOfWeek dayOfWeek, string operatingDays, bool expected)
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.OperatingDays = operatingDays;

            // Act & Assert
            train.OperatesOnDay(dayOfWeek).Should().Be(expected);
        }

        [Test]
        public void GetSummary_WithValidData_ReturnsFormattedSummary()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = "12345";
            train.TrainName = "Rajdhani Express";
            train.TrainType = TrainType.Rajdhani;
            train.SourceStation = "DEL";
            train.DestinationStation = "BOM";
            train.IsActive = true;

            // Act
            var summary = train.GetSummary();

            // Assert
            summary.Should().Contain("12345 - Rajdhani Express");
            summary.Should().Contain("Rajdhani");
            summary.Should().Contain("DEL to BOM");
            summary.Should().Contain("Active");
        }

        [Test]
        [TestCase(null, "N/A")]
        [TestCase(0, "0m")]
        [TestCase(30, "30m")]
        [TestCase(60, "1h 0m")]
        [TestCase(90, "1h 30m")]
        [TestCase(150, "2h 30m")]
        [TestCase(1440, "24h 0m")]
        public void GetFormattedJourneyTime_WithDifferentTimes_ReturnsExpectedFormat(int? journeyTimeMinutes, string expected)
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.JourneyTime = journeyTimeMinutes;

            // Act
            var result = train.GetFormattedJourneyTime();

            // Assert
            result.Should().Be(expected);
        }

        [Test]
        public void ToString_ReturnsDisplayName()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.TrainNumber = "12345";
            train.TrainName = "Rajdhani Express";

            // Act
            var result = train.ToString();

            // Assert
            result.Should().Be("12345 - Rajdhani Express");
        }

        #endregion

        #region Equality Tests

        [Test]
        public void Equals_WithSameIdAndTrainNumber_ReturnsTrue()
        {
            // Arrange
            var train1 = _fixture.Create<Train>();
            train1.Id = 1;
            train1.TrainNumber = "12345";

            var train2 = _fixture.Create<Train>();
            train2.Id = 1;
            train2.TrainNumber = "12345";

            // Act & Assert
            train1.Equals(train2).Should().BeTrue();
        }

        [Test]
        public void Equals_WithDifferentId_ReturnsFalse()
        {
            // Arrange
            var train1 = _fixture.Create<Train>();
            train1.Id = 1;
            train1.TrainNumber = "12345";

            var train2 = _fixture.Create<Train>();
            train2.Id = 2;
            train2.TrainNumber = "12345";

            // Act & Assert
            train1.Equals(train2).Should().BeFalse();
        }

        [Test]
        public void Equals_WithDifferentTrainNumber_ReturnsFalse()
        {
            // Arrange
            var train1 = _fixture.Create<Train>();
            train1.Id = 1;
            train1.TrainNumber = "12345";

            var train2 = _fixture.Create<Train>();
            train2.Id = 1;
            train2.TrainNumber = "67890";

            // Act & Assert
            train1.Equals(train2).Should().BeFalse();
        }

        [Test]
        public void Equals_WithNull_ReturnsFalse()
        {
            // Arrange
            var train = _fixture.Create<Train>();

            // Act & Assert
            train.Equals(null).Should().BeFalse();
        }

        [Test]
        public void GetHashCode_WithSameData_ReturnsSameHashCode()
        {
            // Arrange
            var train1 = _fixture.Create<Train>();
            train1.Id = 1;
            train1.TrainNumber = "12345";

            var train2 = _fixture.Create<Train>();
            train2.Id = 1;
            train2.TrainNumber = "12345";

            // Act & Assert
            train1.GetHashCode().Should().Be(train2.GetHashCode());
        }

        #endregion

        #region Edge Cases and Boundary Tests

        [Test]
        public void Train_WithMaxLengthValues_ShouldBeValid()
        {
            // Arrange
            var train = new Train
            {
                TrainNumber = new string('1', 20), // Max length
                TrainName = new string('A', 100), // Max length
                OperatorCode = new string('O', 10), // Max length
                SourceStation = new string('S', 10), // Max length
                DestinationStation = new string('D', 10), // Max length
                Route = new string('R', 500), // Max length
                OperatingDays = new string('O', 50), // Max length
                Composition = new string('C', 1000), // Max length
                EngineType = new string('E', 20), // Max length
                GaugeType = new string('G', 20), // Max length
                Frequency = new string('F', 20), // Max length
                Remarks = new string('R', 500) // Max length
            };

            // Act & Assert
            train.IsValid().Should().BeTrue();
        }

        [Test]
        public void Train_WithNullCollections_ShouldHandleGracefully()
        {
            // Arrange
            var train = _fixture.Create<Train>();
            train.Schedules = null!;
            train.VoiceFiles = null!;

            // Act & Assert
            train.ActiveScheduleCount.Should().Be(0);
        }

        #endregion

        #region Performance Tests

        [Test]
        public void Train_CreateManyInstances_ShouldPerformWell()
        {
            // Arrange
            const int instanceCount = 10000;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var trains = new List<Train>();
            for (int i = 0; i < instanceCount; i++)
            {
                trains.Add(new Train
                {
                    TrainNumber = $"T{i:D6}",
                    TrainName = $"Train {i}",
                    IsActive = true
                });
            }

            stopwatch.Stop();

            // Assert
            trains.Should().HaveCount(instanceCount);
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000); // Should complete within 1 second
        }

        #endregion
    }
}
