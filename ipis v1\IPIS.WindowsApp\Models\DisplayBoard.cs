using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IPIS.WindowsApp.Models
{
    /// <summary>
    /// Represents the type of display board
    /// </summary>
    public enum DisplayBoardType
    {
        AGDB, // Arrival/Departure General Display Board
        CGDB, // Coach Guidance Display Board
        MLDB, // Multi-Line Display Board
        PDB   // Platform Display Board
    }

    /// <summary>
    /// Represents a display board in the IPIS system
    /// </summary>
    [Table("DisplayBoards")]
    public class DisplayBoard
    {
        /// <summary>
        /// Gets or sets the unique identifier for the display board
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the station ID this display board belongs to
        /// </summary>
        [Required]
        public int StationId { get; set; }

        /// <summary>
        /// Gets or sets the type of display board
        /// </summary>
        public DisplayBoardType BoardType { get; set; }

        /// <summary>
        /// Gets or sets the name or identifier of the display board
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BoardName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the IP address of the display board
        /// </summary>
        [StringLength(15)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// Gets or sets whether the display board is currently online
        /// </summary>
        public bool IsOnline { get; set; } = false;

        /// <summary>
        /// Gets or sets the last heartbeat timestamp from the display board
        /// </summary>
        public DateTime? LastHeartbeat { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the display board was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Navigation property for the station this display board belongs to
        /// </summary>
        [ForeignKey("StationId")]
        public virtual Station Station { get; set; } = null!;

        /// <summary>
        /// Navigation property for messages associated with this display board
        /// </summary>
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
    }
}
