// Decompiled with JetBrains decompiler
// Type: ipis.frmAddPfno
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmAddPfno : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("txtPfno")]
  private TextBox _txtPfno;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  private int pfno_value;
  private int length = 0;

  [DebuggerNonUserCode]
  static frmAddPfno()
  {
  }

  public frmAddPfno()
  {
    frmAddPfno.__ENCAddToList((object) this);
    this.pfno_value = 0;
    this.length = 0;
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmAddPfno.__ENCList)
    {
      if (frmAddPfno.__ENCList.Count == frmAddPfno.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmAddPfno.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmAddPfno.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmAddPfno.__ENCList[index1] = frmAddPfno.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmAddPfno.__ENCList.RemoveRange(index1, checked (frmAddPfno.__ENCList.Count - index1));
        frmAddPfno.__ENCList.Capacity = frmAddPfno.__ENCList.Count;
      }
      frmAddPfno.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblPfno = new Label();
    this.txtPfno = new TextBox();
    this.btnOk = new Button();
    this.btnExit = new Button();
    this.SuspendLayout();
    this.lblPfno.AutoSize = true;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPfno1 = this.lblPfno;
    Point point1 = new Point(29, 29);
    Point point2 = point1;
    lblPfno1.Location = point2;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    Size size1 = new Size(89, 16 /*0x10*/);
    Size size2 = size1;
    lblPfno2.Size = size2;
    this.lblPfno.TabIndex = 0;
    this.lblPfno.Text = "Platform No";
    this.txtPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPfno1 = this.txtPfno;
    point1 = new Point(152, 26);
    Point point3 = point1;
    txtPfno1.Location = point3;
    this.txtPfno.Name = "txtPfno";
    TextBox txtPfno2 = this.txtPfno;
    size1 = new Size(75, 22);
    Size size3 = size1;
    txtPfno2.Size = size3;
    this.txtPfno.TabIndex = 1;
    this.btnOk.BackColor = SystemColors.ButtonFace;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(67, 79);
    Point point4 = point1;
    btnOk1.Location = point4;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(49, 23);
    Size size4 = size1;
    btnOk2.Size = size4;
    this.btnOk.TabIndex = 3;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(142, 79);
    Point point5 = point1;
    btnExit1.Location = point5;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(49, 23);
    Size size5 = size1;
    btnExit2.Size = size5;
    this.btnExit.TabIndex = 5;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(275, 128 /*0x80*/);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPfno);
    this.Controls.Add((Control) this.lblPfno);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmAddPfno";
    this.Text = "frmAddPfno";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual TextBox txtPfno
  {
    [DebuggerNonUserCode] get { return this._txtPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtPfno = value; }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    int index = 0;
    if (Operators.CompareString(this.txtPfno.Text, "", false) == 0)
      return;
    network_db_read.get_platform_details();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      if (Operators.CompareString(this.txtPfno.Text, Strings.Trim(frmMainFormIPIS.platform_nos[index]), false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Platform No is already there", "Msg Box", 0, 0, 0);
        return;
      }
      checked { ++index; }
    }
    checked { ++frmMainFormIPIS.pfno_cnt; }
    if (frmMainFormIPIS.pfno_cnt >= 25)
    {
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Maximum platform entries exceeded", "Msg Box", 0, 0, 0);
    }
    network_db_read.set_platform_details(this.txtPfno.Text, frmMainFormIPIS.pfno_cnt);
    network_db_read.update_platform_details();
    int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Platform No Added", "Msg Box", 0, 0, 0);
    this.txtPfno.Text = string.Empty;
    try
    {
      string str = "Z:\\Database\\platformno_db.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\platformno_db.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}