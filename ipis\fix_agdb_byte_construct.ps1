# PowerShell script to fix all ref parameter issues in agdb_byte_construct.cs

Write-Host "=== FIXING AGDB_BYTE_CONSTRUCT REF ISSUES ==="

$file = "agdb_byte_construct.cs"
$content = Get-Content $file -Raw

if ([string]::IsNullOrEmpty($content)) { 
    Write-Host "File is empty or not found"
    exit 
}

$originalContent = $content

# Fix all train_number_byte calls
$content = $content -replace 'agdb_byte_construct\.train_number_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.train_number_byte($1, ref $2, ref $3)'

# Fix all name_byte calls
$content = $content -replace 'agdb_byte_construct\.name_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.name_byte($1, ref $2, ref $3)'

# Fix all platform_number_byte calls
$content = $content -replace 'agdb_byte_construct\.platform_number_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.platform_number_byte($1, ref $2, ref $3)'

# Fix double ref issues
$content = $content -replace 'ref\s+ref\s+', 'ref '

if ($content -ne $originalContent) {
    Set-Content -Path $file -Value $content -NoNewline
    Write-Host "Fixed ref parameter issues in $file"
} else {
    Write-Host "No changes needed in $file"
}

Write-Host "agdb_byte_construct.cs ref fixes complete!"
