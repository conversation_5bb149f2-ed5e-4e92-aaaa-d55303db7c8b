using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for manual train data override
    /// </summary>
    public class TrainDataOverrideDto
    {
        /// <summary>
        /// Train number being overridden
        /// </summary>
        [Required]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Override train name
        /// </summary>
        public string? TrainName { get; set; }

        /// <summary>
        /// Override current status
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Override delay in minutes
        /// </summary>
        public int? DelayMinutes { get; set; }

        /// <summary>
        /// Override current location
        /// </summary>
        public string? CurrentLocation { get; set; }

        /// <summary>
        /// Override next station
        /// </summary>
        public string? NextStation { get; set; }

        /// <summary>
        /// Override platform number
        /// </summary>
        public string? Platform { get; set; }

        /// <summary>
        /// Override expected arrival time
        /// </summary>
        public DateTime? ExpectedArrival { get; set; }

        /// <summary>
        /// Override expected departure time
        /// </summary>
        public DateTime? ExpectedDeparture { get; set; }

        /// <summary>
        /// Override actual arrival time
        /// </summary>
        public DateTime? ActualArrival { get; set; }

        /// <summary>
        /// Override actual departure time
        /// </summary>
        public DateTime? ActualDeparture { get; set; }

        /// <summary>
        /// Override remarks or announcements
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// Override coach composition
        /// </summary>
        public string? CoachComposition { get; set; }

        /// <summary>
        /// Override train speed
        /// </summary>
        public double? Speed { get; set; }

        /// <summary>
        /// Override distance remaining
        /// </summary>
        public double? DistanceRemaining { get; set; }

        /// <summary>
        /// Whether the train is cancelled
        /// </summary>
        public bool? IsCancelled { get; set; }

        /// <summary>
        /// Whether the train is diverted
        /// </summary>
        public bool? IsDiverted { get; set; }

        /// <summary>
        /// Diversion route information
        /// </summary>
        public string? DiversionRoute { get; set; }

        /// <summary>
        /// Override reason/source
        /// </summary>
        [Required]
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// User who created the override
        /// </summary>
        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Override expiry time (when to revert to API data)
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// Whether this override should persist until manually removed
        /// </summary>
        public bool IsPermanent { get; set; }

        /// <summary>
        /// Priority level (higher priority overrides lower priority)
        /// </summary>
        [Range(1, 10)]
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Stations affected by this override (comma-separated station codes)
        /// </summary>
        public string? AffectedStations { get; set; }

        /// <summary>
        /// Whether to broadcast this override to all displays
        /// </summary>
        public bool BroadcastToAllDisplays { get; set; } = true;

        /// <summary>
        /// Custom announcement message
        /// </summary>
        public string? AnnouncementMessage { get; set; }

        /// <summary>
        /// Languages for announcement (comma-separated)
        /// </summary>
        public string? AnnouncementLanguages { get; set; } = "en,hi";

        /// <summary>
        /// Whether to play audio announcement
        /// </summary>
        public bool PlayAudioAnnouncement { get; set; }

        /// <summary>
        /// Audio file path for custom announcement
        /// </summary>
        public string? CustomAudioFile { get; set; }

        /// <summary>
        /// Display color for this override
        /// </summary>
        public string? DisplayColor { get; set; }

        /// <summary>
        /// Whether to blink/flash the display
        /// </summary>
        public bool FlashDisplay { get; set; }

        /// <summary>
        /// Additional metadata as JSON
        /// </summary>
        public string? Metadata { get; set; }

        /// <summary>
        /// Override creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Whether the override is currently active
        /// </summary>
        public bool IsActive
        {
            get
            {
                if (IsPermanent) return true;
                if (ExpiryTime.HasValue)
                    return DateTime.UtcNow < ExpiryTime.Value;
                return true;
            }
        }

        /// <summary>
        /// Time remaining until expiry
        /// </summary>
        public TimeSpan? TimeToExpiry
        {
            get
            {
                if (IsPermanent) return null;
                if (ExpiryTime.HasValue)
                {
                    var remaining = ExpiryTime.Value - DateTime.UtcNow;
                    return remaining.TotalSeconds > 0 ? remaining : TimeSpan.Zero;
                }
                return null;
            }
        }

        /// <summary>
        /// Override status description
        /// </summary>
        public string StatusDescription
        {
            get
            {
                if (!IsActive) return "Expired";
                if (IsPermanent) return "Permanent Override";
                if (TimeToExpiry.HasValue)
                {
                    var remaining = TimeToExpiry.Value;
                    if (remaining.TotalHours >= 1)
                        return $"Expires in {(int)remaining.TotalHours}h {remaining.Minutes}m";
                    else
                        return $"Expires in {remaining.Minutes}m";
                }
                return "Active";
            }
        }
    }
}
