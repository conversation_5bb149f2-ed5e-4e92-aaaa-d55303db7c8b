// Decompiled with JetBrains decompiler
// Type: ipis.agdb_lookup_table
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Diagnostics;

namespace ipis
{

public class agdb_lookup_table
{
  public const byte TRAIN_NO1 = 0;
  public const byte TRAIN_NO2 = 6;
  public const byte TRAIN_NO3 = 12;
  public const byte TRAIN_NO4 = 18;
  public const byte TRAIN_NO5 = 24;
  public const byte TRAIN_NO_CUNT = 6;
  public const byte TRAIN_NAME_CUNT = 32 /*0x20*/;
  public const byte TRAIN_TIME = 181;
  public const byte TRAIN_TYPE = 210;
  public const byte TRAIN_PF_NO = 222;
  public const byte MAX_TRAIN_NAME_LENGTH = 24;
  public static byte[] BYTE_ZERO_0 = new byte[5]
  {
    (byte) 126,
    (byte) 145,
    (byte) 137,
    (byte) 133,
    (byte) 126
  };
  public static byte[] BYTE_ONE_1 = new byte[5]
  {
    (byte) 128 /*0x80*/,
    (byte) 130,
    byte.MaxValue,
    (byte) 128 /*0x80*/,
    (byte) 128 /*0x80*/
  };
  public static byte[] BYTE_TWO_2 = new byte[5]
  {
    (byte) 194,
    (byte) 161,
    (byte) 145,
    (byte) 137,
    (byte) 134
  };
  public static byte[] BYTE_THREE_3 = new byte[5]
  {
    (byte) 66,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 118
  };
  public static byte[] BYTE_FOUR_4 = new byte[5]
  {
    (byte) 56,
    (byte) 36,
    (byte) 34,
    byte.MaxValue,
    (byte) 32 /*0x20*/
  };
  public static byte[] BYTE_FIVE_5 = new byte[5]
  {
    (byte) 143,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 113
  };
  public static byte[] BYTE_SIX_6 = new byte[5]
  {
    (byte) 126,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 114
  };
  public static byte[] BYTE_SEVEN_7 = new byte[5]
  {
    (byte) 1,
    (byte) 1,
    (byte) 241,
    (byte) 9,
    (byte) 6
  };
  public static byte[] BYTE_EIGHT_8 = new byte[5]
  {
    (byte) 118,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 118
  };
  public static byte[] BYTE_NINE_9 = new byte[5]
  {
    (byte) 70,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 126
  };
  public static byte[] A_A = new byte[5]
  {
    (byte) 252,
    (byte) 18,
    (byte) 17,
    (byte) 18,
    (byte) 252
  };
  public static byte[] B_B = new byte[5]
  {
    byte.MaxValue,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 118
  };
  public static byte[] C_C = new byte[5]
  {
    (byte) 126,
    (byte) 129,
    (byte) 129,
    (byte) 129,
    (byte) 129
  };
  public static byte[] D_D = new byte[5]
  {
    byte.MaxValue,
    (byte) 129,
    (byte) 129,
    (byte) 129,
    (byte) 126
  };
  public static byte[] E_E = new byte[5]
  {
    byte.MaxValue,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 129
  };
  public static byte[] F_F = new byte[5]
  {
    byte.MaxValue,
    (byte) 9,
    (byte) 9,
    (byte) 9,
    (byte) 1
  };
  public static byte[] G_G = new byte[5]
  {
    (byte) 126,
    (byte) 129,
    (byte) 137,
    (byte) 137,
    (byte) 122
  };
  public static byte[] H_H = new byte[5]
  {
    byte.MaxValue,
    (byte) 8,
    (byte) 8,
    (byte) 8,
    byte.MaxValue
  };
  public static byte[] I_I = new byte[5]
  {
    (byte) 129,
    (byte) 129,
    byte.MaxValue,
    (byte) 129,
    (byte) 129
  };
  public static byte[] J_J = new byte[5]
  {
    (byte) 97,
    (byte) 129,
    (byte) 129,
    (byte) 127 /*0x7F*/,
    (byte) 1
  };
  public static byte[] K_K = new byte[5]
  {
    byte.MaxValue,
    (byte) 24,
    (byte) 36,
    (byte) 66,
    (byte) 129
  };
  public static byte[] L_L = new byte[5]
  {
    byte.MaxValue,
    (byte) 128 /*0x80*/,
    (byte) 128 /*0x80*/,
    (byte) 128 /*0x80*/,
    (byte) 128 /*0x80*/
  };
  public static byte[] M_M = new byte[5]
  {
    byte.MaxValue,
    (byte) 4,
    (byte) 8,
    (byte) 4,
    byte.MaxValue
  };
  public static byte[] N_N = new byte[5]
  {
    byte.MaxValue,
    (byte) 4,
    (byte) 8,
    (byte) 16 /*0x10*/,
    byte.MaxValue
  };
  public static byte[] O_O = new byte[5]
  {
    (byte) 126,
    (byte) 129,
    (byte) 129,
    (byte) 129,
    (byte) 126
  };
  public static byte[] P_P = new byte[5]
  {
    byte.MaxValue,
    (byte) 9,
    (byte) 9,
    (byte) 9,
    (byte) 6
  };
  public static byte[] Q_Q = new byte[5]
  {
    (byte) 126,
    (byte) 129,
    (byte) 161,
    (byte) 193,
    (byte) 254
  };
  public static byte[] R_R = new byte[5]
  {
    byte.MaxValue,
    (byte) 25,
    (byte) 41,
    (byte) 73,
    (byte) 134
  };
  public static byte[] S_S = new byte[5]
  {
    (byte) 70,
    (byte) 137,
    (byte) 137,
    (byte) 137,
    (byte) 114
  };
  public static byte[] T_T = new byte[5]
  {
    (byte) 1,
    (byte) 1,
    byte.MaxValue,
    (byte) 1,
    (byte) 1
  };
  public static byte[] U_U = new byte[5]
  {
    (byte) 127 /*0x7F*/,
    (byte) 128 /*0x80*/,
    (byte) 128 /*0x80*/,
    (byte) 128 /*0x80*/,
    (byte) 127 /*0x7F*/
  };
  public static byte[] V_V = new byte[5]
  {
    (byte) 63 /*0x3F*/,
    (byte) 64 /*0x40*/,
    (byte) 128 /*0x80*/,
    (byte) 64 /*0x40*/,
    (byte) 63 /*0x3F*/
  };
  public static byte[] W_W = new byte[5]
  {
    byte.MaxValue,
    (byte) 64 /*0x40*/,
    (byte) 32 /*0x20*/,
    (byte) 64 /*0x40*/,
    byte.MaxValue
  };
  public static byte[] X_X = new byte[5]
  {
    (byte) 227,
    (byte) 20,
    (byte) 8,
    (byte) 20,
    (byte) 227
  };
  public static byte[] Y_Y = new byte[5]
  {
    (byte) 7,
    (byte) 8,
    (byte) 240 /*0xF0*/,
    (byte) 8,
    (byte) 7
  };
  public static byte[] Z_Z = new byte[5]
  {
    (byte) 225,
    (byte) 145,
    (byte) 137,
    (byte) 133,
    (byte) 131
  };
  public static byte BYTE_COLON = 36;
  public static byte[] BYTE_SPACE = new byte[5]
  {
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0
  };
  public static byte[] SIGN_RIGHT = new byte[5]
  {
    (byte) 16 /*0x10*/,
    (byte) 16 /*0x10*/,
    (byte) 84,
    (byte) 56,
    (byte) 16 /*0x10*/
  };
  public static byte[] SIGN_LEFT = new byte[5]
  {
    (byte) 16 /*0x10*/,
    (byte) 56,
    (byte) 84,
    (byte) 16 /*0x10*/,
    (byte) 16 /*0x10*/
  };
  public static byte[] SIGN_LINE = new byte[2]
  {
    byte.MaxValue,
    byte.MaxValue
  };

  [DebuggerNonUserCode]
  public agdb_lookup_table()
  {
  }
}

}