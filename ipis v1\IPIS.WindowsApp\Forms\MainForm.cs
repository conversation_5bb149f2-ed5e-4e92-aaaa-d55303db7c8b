using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using IPIS.WindowsApp.Models;
using IPIS.WindowsApp.Services;

namespace IPIS.WindowsApp.Forms
{
    /// <summary>
    /// Main form for the IPIS Windows application
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly IStationService _stationService;
        private readonly ILogger<MainForm> _logger;

        /// <summary>
        /// Initializes a new instance of the MainForm class
        /// </summary>
        /// <param name="stationService">The station service</param>
        /// <param name="logger">The logger instance</param>
        public MainForm(IStationService stationService, ILogger<MainForm> logger)
        {
            _stationService = stationService ?? throw new ArgumentNullException(nameof(stationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            InitializeFormAsync();
        }

        /// <summary>
        /// Initializes the form asynchronously
        /// </summary>
        private async void InitializeFormAsync()
        {
            try
            {
                _logger.LogInformation("Initializing main form");
                await LoadStationsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing main form");
                MessageBox.Show($"Error initializing application: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Loads stations into the DataGridView
        /// </summary>
        private async Task LoadStationsAsync()
        {
            try
            {
                _logger.LogInformation("Loading stations");

                // Show loading indicator
                stationsDataGridView.DataSource = null;
                statusLabel.Text = "Loading stations...";

                var stations = await _stationService.GetAllStationsAsync();

                // Create a list for display purposes
                var stationDisplayList = stations.Select(s => new
                {
                    s.Id,
                    s.Code,
                    s.Name,
                    s.Location,
                    Status = s.IsActive ? "Active" : "Inactive",
                    PlatformCount = s.Platforms.Count,
                    s.CreatedAt
                }).ToList();

                stationsDataGridView.DataSource = stationDisplayList;

                // Configure DataGridView columns
                ConfigureDataGridView();

                statusLabel.Text = $"Loaded {stations.Count()} stations";
                _logger.LogInformation("Successfully loaded {StationCount} stations", stations.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading stations");
                statusLabel.Text = "Error loading stations";
                MessageBox.Show($"Error loading stations: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Configures the DataGridView appearance and behavior
        /// </summary>
        private void ConfigureDataGridView()
        {
            if (stationsDataGridView.Columns.Count > 0)
            {
                stationsDataGridView.Columns["Id"].HeaderText = "ID";
                stationsDataGridView.Columns["Id"].Width = 50;
                stationsDataGridView.Columns["Code"].HeaderText = "Code";
                stationsDataGridView.Columns["Code"].Width = 80;
                stationsDataGridView.Columns["Name"].HeaderText = "Station Name";
                stationsDataGridView.Columns["Name"].Width = 200;
                stationsDataGridView.Columns["Location"].HeaderText = "Location";
                stationsDataGridView.Columns["Location"].Width = 200;
                stationsDataGridView.Columns["Status"].HeaderText = "Status";
                stationsDataGridView.Columns["Status"].Width = 80;
                stationsDataGridView.Columns["PlatformCount"].HeaderText = "Platforms";
                stationsDataGridView.Columns["PlatformCount"].Width = 80;
                stationsDataGridView.Columns["CreatedAt"].HeaderText = "Created";
                stationsDataGridView.Columns["CreatedAt"].Width = 120;
            }

            stationsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            stationsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            stationsDataGridView.MultiSelect = false;
            stationsDataGridView.ReadOnly = true;
        }

        /// <summary>
        /// Handles the Add Station button click event
        /// </summary>
        private async void addStationButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Create a logger factory for the AddStationForm
                using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var addStationLogger = loggerFactory.CreateLogger<AddStationForm>();

                using var addForm = new AddStationForm(_stationService, addStationLogger);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadStationsAsync();
                    MessageBox.Show($"Station '{addForm.Station?.Name}' has been added successfully.",
                        "Station Added", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening add station form");
                MessageBox.Show($"Error opening add station form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Refresh button click event
        /// </summary>
        private async void refreshButton_Click(object sender, EventArgs e)
        {
            await LoadStationsAsync();
        }

        /// <summary>
        /// Handles the form closing event
        /// </summary>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            _logger.LogInformation("Main form closing");
        }

        /// <summary>
        /// Handles the DataGridView cell double-click event for editing
        /// </summary>
        private async void stationsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    var selectedRow = stationsDataGridView.Rows[e.RowIndex];
                    var stationId = (int)selectedRow.Cells["Id"].Value;

                    var station = await _stationService.GetStationByIdAsync(stationId);
                    if (station != null)
                    {
                        MessageBox.Show($"Station Details:\nID: {station.Id}\nCode: {station.Code}\nName: {station.Name}\nLocation: {station.Location}\nStatus: {(station.IsActive ? "Active" : "Inactive")}",
                            "Station Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error displaying station details");
                    MessageBox.Show($"Error displaying station details: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// Handles the Exit menu item click event
        /// </summary>
        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
