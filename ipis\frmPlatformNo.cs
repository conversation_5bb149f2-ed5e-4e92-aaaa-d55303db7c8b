// Decompiled with JetBrains decompiler
// Type: ipis.frmPlatformNo
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmPlatformNo : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("cmbPfno")]
  private ComboBox _cmbPfno;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("Label10")]
  private Label _Label10;
  [AccessedThroughProperty("event_pfno")]
  private frmAddPfno _event_pfno;

  [DebuggerNonUserCode]
  static frmPlatformNo()
  {
  }

  [DebuggerNonUserCode]
  public frmPlatformNo()
  {
    this.Load += new EventHandler(this.frmPlatformNo_Load);
    frmPlatformNo.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmPlatformNo.__ENCList)
    {
      if (frmPlatformNo.__ENCList.Count == frmPlatformNo.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmPlatformNo.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmPlatformNo.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmPlatformNo.__ENCList[index1] = frmPlatformNo.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmPlatformNo.__ENCList.RemoveRange(index1, checked (frmPlatformNo.__ENCList.Count - index1));
        frmPlatformNo.__ENCList.Capacity = frmPlatformNo.__ENCList.Count;
      }
      frmPlatformNo.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnDelete = new Button();
    this.cmbPfno = new ComboBox();
    this.btnAdd = new Button();
    this.Label10 = new Label();
    this.SuspendLayout();
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDelete1 = this.btnDelete;
    Point point1 = new Point(267, 77);
    Point point2 = point1;
    btnDelete1.Location = point2;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete2 = this.btnDelete;
    Size size1 = new Size(72, 23);
    Size size2 = size1;
    btnDelete2.Size = size2;
    this.btnDelete.TabIndex = 285;
    this.btnDelete.Text = "Delete";
    this.btnDelete.UseVisualStyleBackColor = true;
    this.cmbPfno.FormattingEnabled = true;
    ComboBox cmbPfno1 = this.cmbPfno;
    point1 = new Point(73, 79);
    Point point3 = point1;
    cmbPfno1.Location = point3;
    this.cmbPfno.Name = "cmbPfno";
    ComboBox cmbPfno2 = this.cmbPfno;
    size1 = new Size(69, 21);
    Size size3 = size1;
    cmbPfno2.Size = size3;
    this.cmbPfno.TabIndex = 284;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(182, 77);
    Point point4 = point1;
    btnAdd1.Location = point4;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd2 = this.btnAdd;
    size1 = new Size(54, 23);
    Size size4 = size1;
    btnAdd2.Size = size4;
    this.btnAdd.TabIndex = 283;
    this.btnAdd.Text = "Add";
    this.btnAdd.UseVisualStyleBackColor = true;
    this.Label10.AutoSize = true;
    this.Label10.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label10.ForeColor = Color.RoyalBlue;
    Label label10_1 = this.Label10;
    point1 = new Point(150, 18);
    Point point5 = point1;
    label10_1.Location = point5;
    this.Label10.Name = "Label10";
    Label label10_2 = this.Label10;
    size1 = new Size(134, 24);
    Size size5 = size1;
    label10_2.Size = size5;
    this.Label10.TabIndex = 282;
    this.Label10.Text = " Platform Nos";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(348, 126);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.cmbPfno);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.Label10);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmPlatformNo";
    this.Text = "Platform No";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbPfno
  {
    [DebuggerNonUserCode] get { return this._cmbPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbPfno = value; }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual Label Label10
  {
    [DebuggerNonUserCode] get { return this._Label10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label10 = value; }
  }

  protected virtual frmAddPfno event_pfno
  {
    [DebuggerNonUserCode] get { return this._event_pfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_pfno = value;
    }
  }

  private void frmPlatformNo_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }

  private void cmbPfno_DropDown(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPfno.Items.Clear();
    network_db_read.get_platform_details();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_pfno))
    {
      if (!this.event_pfno.IsDisposed)
      {
        this.event_pfno.WindowState = FormWindowState.Normal;
        this.event_pfno.BringToFront();
      }
      else
      {
        this.event_pfno = new frmAddPfno();
        this.event_pfno.Show();
      }
    }
    else
    {
      this.event_pfno = new frmAddPfno();
      this.event_pfno.Show();
    }
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "All platforms are deleted \r\nAfter delete, enter required Platform Nos", "Msg Box", 4, 0, 0) != (short) 6)
      return;
    online_trains.update_dgv();
    network_db_read.delete_platform_details();
  }
}

}