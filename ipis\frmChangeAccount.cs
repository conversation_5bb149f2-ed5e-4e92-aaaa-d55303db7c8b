// Decompiled with JetBrains decompiler
// Type: ipis.frmChangeAccount
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmChangeAccount : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnCreateNewAccount")]
  private Button _btnCreateNewAccount;
  [AccessedThroughProperty("btnChangeAccount")]
  private Button _btnChangeAccount;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("event_changeaccount")]
  private frmChangeUserDetails _event_changeaccount;
  [AccessedThroughProperty("event_newaccount")]
  private frmAddNewUser _event_newaccount;

  [DebuggerNonUserCode]
  static frmChangeAccount()
  {
  }

  [DebuggerNonUserCode]
  public frmChangeAccount()
  {
    this.Load += new EventHandler(this.frmChangeAccount_Load);
    frmChangeAccount.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmChangeAccount.__ENCList)
    {
      if (frmChangeAccount.__ENCList.Count == frmChangeAccount.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmChangeAccount.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmChangeAccount.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmChangeAccount.__ENCList[index1] = frmChangeAccount.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmChangeAccount.__ENCList.RemoveRange(index1, checked (frmChangeAccount.__ENCList.Count - index1));
        frmChangeAccount.__ENCList.Capacity = frmChangeAccount.__ENCList.Count;
      }
      frmChangeAccount.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnCreateNewAccount = new Button();
    this.btnChangeAccount = new Button();
    this.btnExit = new Button();
    this.SuspendLayout();
    Button createNewAccount1 = this.btnCreateNewAccount;
    Point point1 = new Point(23, 90);
    Point point2 = point1;
    createNewAccount1.Location = point2;
    Button createNewAccount2 = this.btnCreateNewAccount;
    Padding padding1 = new Padding(4);
    Padding padding2 = padding1;
    createNewAccount2.Margin = padding2;
    this.btnCreateNewAccount.Name = "btnCreateNewAccount";
    Button createNewAccount3 = this.btnCreateNewAccount;
    Size size1 = new Size(206, 28);
    Size size2 = size1;
    createNewAccount3.Size = size2;
    this.btnCreateNewAccount.TabIndex = 2;
    this.btnCreateNewAccount.Text = "Create a new Account";
    this.btnCreateNewAccount.UseVisualStyleBackColor = true;
    Button btnChangeAccount1 = this.btnChangeAccount;
    point1 = new Point(23, 27);
    Point point3 = point1;
    btnChangeAccount1.Location = point3;
    Button btnChangeAccount2 = this.btnChangeAccount;
    padding1 = new Padding(4);
    Padding padding3 = padding1;
    btnChangeAccount2.Margin = padding3;
    this.btnChangeAccount.Name = "btnChangeAccount";
    Button btnChangeAccount3 = this.btnChangeAccount;
    size1 = new Size(206, 28);
    Size size3 = size1;
    btnChangeAccount3.Size = size3;
    this.btnChangeAccount.TabIndex = 1;
    this.btnChangeAccount.Text = "Change the Account";
    this.btnChangeAccount.UseVisualStyleBackColor = true;
    this.btnExit.DialogResult = DialogResult.Cancel;
    Button btnExit1 = this.btnExit;
    point1 = new Point(83, 153);
    Point point4 = point1;
    btnExit1.Location = point4;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(75, 23);
    Size size4 = size1;
    btnExit2.Size = size4;
    this.btnExit.TabIndex = 3;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = true;
    this.AutoScaleDimensions = new SizeF(9f, 16f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(264, 197);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnCreateNewAccount);
    this.Controls.Add((Control) this.btnChangeAccount);
    this.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    padding1 = new Padding(4);
    this.Margin = padding1;
    this.Name = "frmChangeAccount";
    this.Text = "frmChangeAccount";
    this.ResumeLayout(false);
  }

  internal virtual Button btnCreateNewAccount
  {
    [DebuggerNonUserCode] get { return this._btnCreateNewAccount; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnCreateNewAccount_Click);
      if (this._btnCreateNewAccount != null)
        this._btnCreateNewAccount.Click -= eventHandler;
      this._btnCreateNewAccount = value;
      if (this._btnCreateNewAccount == null)
        return;
      this._btnCreateNewAccount.Click += eventHandler;
    }
  }

  internal virtual Button btnChangeAccount
  {
    [DebuggerNonUserCode] get { return this._btnChangeAccount; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnChangeAccount_Click);
      if (this._btnChangeAccount != null)
        this._btnChangeAccount.Click -= eventHandler;
      this._btnChangeAccount = value;
      if (this._btnChangeAccount == null)
        return;
      this._btnChangeAccount.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  protected virtual frmChangeUserDetails event_changeaccount
  {
    [DebuggerNonUserCode] get { return this._event_changeaccount; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_changeaccount = value;
    }
  }

  protected virtual frmAddNewUser event_newaccount
  {
    [DebuggerNonUserCode] get { return this._event_newaccount; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_newaccount = value;
    }
  }

  private void btnCreateNewAccount_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_newaccount))
    {
      if (!this.event_newaccount.IsDisposed)
      {
        this.event_newaccount.WindowState = FormWindowState.Normal;
        this.event_newaccount.BringToFront();
      }
      else
      {
        this.event_newaccount = new frmAddNewUser();
        this.event_newaccount.Show();
      }
    }
    else
    {
      this.event_newaccount = new frmAddNewUser();
      this.event_newaccount.Show();
    }
  }

  private void btnChangeAccount_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_changeaccount))
    {
      if (!this.event_changeaccount.IsDisposed)
      {
        this.event_changeaccount.WindowState = FormWindowState.Normal;
        this.event_changeaccount.BringToFront();
      }
      else
      {
        this.event_changeaccount = new frmChangeUserDetails();
        this.event_changeaccount.Show();
      }
    }
    else
    {
      this.event_changeaccount = new frmChangeUserDetails();
      this.event_changeaccount.Show();
    }
  }

  private void frmChangeAccount_Load(object sender, EventArgs e)
  {
    if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_login_details.group), "Admin", false) == 0)
      this.btnCreateNewAccount.Enabled = true;
    else
      this.btnCreateNewAccount.Enabled = false;
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}