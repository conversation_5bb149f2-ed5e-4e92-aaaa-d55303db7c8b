using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Interface for offline mode service operations
    /// Manages graceful degradation when Railway API is unavailable
    /// </summary>
    public interface IOfflineModeService
    {
        #region Offline Mode Status

        /// <summary>
        /// Indicates if the system is currently in offline mode
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if in offline mode</returns>
        Task<bool> IsOfflineModeActiveAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the reason why the system is in offline mode
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Offline reason or null if online</returns>
        Task<string?> GetOfflineReasonAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the timestamp when offline mode was activated
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Offline mode start time or null if online</returns>
        Task<DateTime?> GetOfflineModeStartTimeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the duration the system has been offline
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Offline duration or null if online</returns>
        Task<TimeSpan?> GetOfflineDurationAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Cache Management

        /// <summary>
        /// Gets cached schedules for a specific station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of cached schedules</returns>
        Task<List<Schedule>> GetCachedSchedulesAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets cached schedules for a specific date range
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of cached schedules</returns>
        Task<List<Schedule>> GetCachedSchedulesAsync(string stationCode, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

        /// <summary>
        /// Caches schedule data for offline use
        /// </summary>
        /// <param name="schedules">Schedules to cache</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the cache operation</returns>
        Task CacheScheduleDataAsync(List<Schedule> schedules, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets cached train status information
        /// </summary>
        /// <param name="trainNumber">Train number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cached train status or null if not found</returns>
        Task<TrainStatusDto?> GetCachedTrainStatusAsync(string trainNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Caches train status data for offline use
        /// </summary>
        /// <param name="trainStatuses">Train statuses to cache</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the cache operation</returns>
        Task CacheTrainStatusDataAsync(List<TrainStatusDto> trainStatuses, CancellationToken cancellationToken = default);

        #endregion

        #region Data Freshness

        /// <summary>
        /// Gets the timestamp of the last successful API synchronization
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Last sync time or null if never synced</returns>
        Task<DateTime?> GetLastSyncTimeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the age of cached data for a specific station
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Data age or null if no cached data</returns>
        Task<TimeSpan?> GetCachedDataAgeAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Indicates if cached data is considered stale
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="maxAge">Maximum acceptable age</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if data is stale</returns>
        Task<bool> IsCachedDataStaleAsync(string stationCode, TimeSpan maxAge, CancellationToken cancellationToken = default);

        #endregion

        #region Offline Mode Control

        /// <summary>
        /// Manually activates offline mode
        /// </summary>
        /// <param name="reason">Reason for activating offline mode</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the activation operation</returns>
        Task ActivateOfflineModeAsync(string reason, CancellationToken cancellationToken = default);

        /// <summary>
        /// Attempts to deactivate offline mode and return to online operation
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successfully returned to online mode</returns>
        Task<bool> TryReturnToOnlineModeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Forces the system back to online mode (bypasses connectivity checks)
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the force online operation</returns>
        Task ForceOnlineModeAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Cache Cleanup and Maintenance

        /// <summary>
        /// Clears all cached data
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the clear operation</returns>
        Task ClearAllCacheAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Clears cached data older than the specified age
        /// </summary>
        /// <param name="maxAge">Maximum age of data to keep</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Number of records cleared</returns>
        Task<int> ClearStaleDataAsync(TimeSpan maxAge, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the total size of cached data
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cache size information</returns>
        Task<CacheSizeInfo> GetCacheSizeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Optimizes the cache by removing redundant or outdated data
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cache optimization result</returns>
        Task<CacheOptimizationResult> OptimizeCacheAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Fallback Data

        /// <summary>
        /// Gets fallback schedule data when no cached data is available
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of fallback schedules</returns>
        Task<List<Schedule>> GetFallbackSchedulesAsync(string stationCode, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets fallback schedule data for emergency use
        /// </summary>
        /// <param name="stationCode">Station code</param>
        /// <param name="fallbackSchedules">Fallback schedules</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the set operation</returns>
        Task SetFallbackSchedulesAsync(string stationCode, List<Schedule> fallbackSchedules, CancellationToken cancellationToken = default);

        #endregion

        #region Events

        /// <summary>
        /// Event raised when offline mode is activated
        /// </summary>
        event EventHandler<OfflineModeActivatedEventArgs>? OfflineModeActivated;

        /// <summary>
        /// Event raised when the system returns to online mode
        /// </summary>
        event EventHandler<OnlineModeRestoredEventArgs>? OnlineModeRestored;

        /// <summary>
        /// Event raised when cached data becomes stale
        /// </summary>
        event EventHandler<CachedDataStaleEventArgs>? CachedDataStale;

        /// <summary>
        /// Event raised when cache cleanup is performed
        /// </summary>
        event EventHandler<CacheCleanupEventArgs>? CacheCleanup;

        #endregion
    }

    /// <summary>
    /// Information about cache size
    /// </summary>
    public class CacheSizeInfo
    {
        public long TotalSizeBytes { get; set; }
        public int TotalRecords { get; set; }
        public int ScheduleRecords { get; set; }
        public int TrainStatusRecords { get; set; }
        public int DelayRecords { get; set; }
        public int PlatformAssignmentRecords { get; set; }
        public DateTime OldestRecord { get; set; }
        public DateTime NewestRecord { get; set; }
        public string FormattedSize => FormatBytes(TotalSizeBytes);

        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024.0):F1} MB";
            return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }
    }

    /// <summary>
    /// Result of cache optimization operation
    /// </summary>
    public class CacheOptimizationResult
    {
        public bool IsSuccessful { get; set; }
        public int RecordsRemoved { get; set; }
        public long BytesFreed { get; set; }
        public TimeSpan OptimizationDuration { get; set; }
        public List<string> OptimizationActions { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }

    #region Event Arguments

    /// <summary>
    /// Event arguments for offline mode activated events
    /// </summary>
    public class OfflineModeActivatedEventArgs : EventArgs
    {
        public DateTime ActivatedAt { get; set; }
        public string Reason { get; set; } = string.Empty;
        public bool IsAutomatic { get; set; }
        public TimeSpan? LastOnlineDuration { get; set; }
    }

    /// <summary>
    /// Event arguments for online mode restored events
    /// </summary>
    public class OnlineModeRestoredEventArgs : EventArgs
    {
        public DateTime RestoredAt { get; set; }
        public TimeSpan OfflineDuration { get; set; }
        public bool IsAutomatic { get; set; }
        public int PendingSyncOperations { get; set; }
    }

    /// <summary>
    /// Event arguments for cached data stale events
    /// </summary>
    public class CachedDataStaleEventArgs : EventArgs
    {
        public string StationCode { get; set; } = string.Empty;
        public TimeSpan DataAge { get; set; }
        public DateTime LastUpdate { get; set; }
        public int AffectedRecords { get; set; }
    }

    /// <summary>
    /// Event arguments for cache cleanup events
    /// </summary>
    public class CacheCleanupEventArgs : EventArgs
    {
        public DateTime CleanupTime { get; set; }
        public int RecordsRemoved { get; set; }
        public long BytesFreed { get; set; }
        public string CleanupType { get; set; } = string.Empty;
        public TimeSpan CleanupDuration { get; set; }
    }

    #endregion
}
