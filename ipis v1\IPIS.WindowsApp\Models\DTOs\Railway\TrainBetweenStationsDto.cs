using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for trains between stations
    /// </summary>
    public class TrainBetweenStationsDto
    {
        /// <summary>
        /// Train number
        /// </summary>
        [Required]
        public string TrainNumber { get; set; } = string.Empty;

        /// <summary>
        /// Train name
        /// </summary>
        public string TrainName { get; set; } = string.Empty;

        /// <summary>
        /// Train type
        /// </summary>
        public string TrainType { get; set; } = string.Empty;

        /// <summary>
        /// Source station code
        /// </summary>
        [Required]
        public string FromStationCode { get; set; } = string.Empty;

        /// <summary>
        /// Source station name
        /// </summary>
        public string FromStationName { get; set; } = string.Empty;

        /// <summary>
        /// Destination station code
        /// </summary>
        [Required]
        public string ToStationCode { get; set; } = string.Empty;

        /// <summary>
        /// Destination station name
        /// </summary>
        public string ToStationName { get; set; } = string.Empty;

        /// <summary>
        /// Departure time from source station
        /// </summary>
        public DateTime? DepartureTime { get; set; }

        /// <summary>
        /// Arrival time at destination station
        /// </summary>
        public DateTime? ArrivalTime { get; set; }

        /// <summary>
        /// Journey duration
        /// </summary>
        public TimeSpan? Duration { get; set; }

        /// <summary>
        /// Distance between stations
        /// </summary>
        public double? Distance { get; set; }

        /// <summary>
        /// Available classes
        /// </summary>
        public List<string> AvailableClasses { get; set; } = new();

        /// <summary>
        /// Days of operation
        /// </summary>
        public string? OperatingDays { get; set; }

        /// <summary>
        /// Whether train runs on the specified date
        /// </summary>
        public bool RunsOnDate { get; set; } = true;

        /// <summary>
        /// Current delay in minutes
        /// </summary>
        public int DelayMinutes { get; set; }

        /// <summary>
        /// Current status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Whether train is cancelled
        /// </summary>
        public bool IsCancelled { get; set; }

        /// <summary>
        /// Whether train is diverted
        /// </summary>
        public bool IsDiverted { get; set; }

        /// <summary>
        /// Seat availability summary
        /// </summary>
        public Dictionary<string, string> SeatAvailability { get; set; } = new();

        /// <summary>
        /// Fare information by class
        /// </summary>
        public Dictionary<string, decimal> Fares { get; set; } = new();

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Data source
        /// </summary>
        public string DataSource { get; set; } = "API";

        /// <summary>
        /// Expected departure time (with delays)
        /// </summary>
        public DateTime? ExpectedDepartureTime
        {
            get
            {
                if (DepartureTime.HasValue)
                    return DepartureTime.Value.AddMinutes(DelayMinutes);
                return null;
            }
        }

        /// <summary>
        /// Expected arrival time (with delays)
        /// </summary>
        public DateTime? ExpectedArrivalTime
        {
            get
            {
                if (ArrivalTime.HasValue)
                    return ArrivalTime.Value.AddMinutes(DelayMinutes);
                return null;
            }
        }

        /// <summary>
        /// Formatted delay text
        /// </summary>
        public string DelayText
        {
            get
            {
                if (DelayMinutes == 0) return "On Time";
                if (DelayMinutes > 0) return $"Late by {DelayMinutes} min";
                return $"Early by {Math.Abs(DelayMinutes)} min";
            }
        }

        /// <summary>
        /// Status color for UI
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (IsCancelled) return "Red";
                if (IsDiverted) return "Purple";
                return Status.ToLower() switch
                {
                    "on time" => "Green",
                    "delayed" or "running late" => "Orange",
                    "terminated" => "Red",
                    _ => "Gray"
                };
            }
        }

        /// <summary>
        /// Whether seats are available in any class
        /// </summary>
        public bool HasAvailableSeats
        {
            get
            {
                return SeatAvailability.Values.Any(availability => 
                    !availability.Contains("WL") && 
                    !availability.Contains("REGRET") &&
                    availability != "0");
            }
        }

        /// <summary>
        /// Minimum fare across all classes
        /// </summary>
        public decimal? MinimumFare
        {
            get
            {
                return Fares.Values.Any() ? Fares.Values.Min() : null;
            }
        }

        /// <summary>
        /// Maximum fare across all classes
        /// </summary>
        public decimal? MaximumFare
        {
            get
            {
                return Fares.Values.Any() ? Fares.Values.Max() : null;
            }
        }
    }
}
