using Microsoft.EntityFrameworkCore;
using IPIS.WindowsApp.Models;

namespace IPIS.WindowsApp.Data
{
    /// <summary>
    /// Entity Framework DbContext for the IPIS application
    /// </summary>
    public class IPISDbContext : DbContext
    {
        /// <summary>
        /// Initializes a new instance of the IPISDbContext class
        /// </summary>
        /// <param name="options">The options for this context</param>
        public IPISDbContext(DbContextOptions<IPISDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// Gets or sets the Stations DbSet
        /// </summary>
        public DbSet<Station> Stations { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Platforms DbSet
        /// </summary>
        public DbSet<Platform> Platforms { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Trains DbSet
        /// </summary>
        public DbSet<Train> Trains { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Schedules DbSet
        /// </summary>
        public DbSet<Schedule> Schedules { get; set; } = null!;

        /// <summary>
        /// Gets or sets the DisplayBoards DbSet
        /// </summary>
        public DbSet<DisplayBoard> DisplayBoards { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Messages DbSet
        /// </summary>
        public DbSet<Message> Messages { get; set; } = null!;

        /// <summary>
        /// Configures the model that was discovered by convention from the entity types
        /// </summary>
        /// <param name="modelBuilder">The builder being used to construct the model for this context</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Station entity
            modelBuilder.Entity<Station>(entity =>
            {
                entity.HasIndex(e => e.Code).IsUnique();
                entity.Property(e => e.Code).IsRequired().HasMaxLength(10);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Location).HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure Platform entity
            modelBuilder.Entity<Platform>(entity =>
            {
                entity.Property(e => e.PlatformNumber).IsRequired().HasMaxLength(10);
                entity.Property(e => e.PlatformName).HasMaxLength(50);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.Station)
                    .WithMany(p => p.Platforms)
                    .HasForeignKey(d => d.StationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.StationId, e.PlatformNumber }).IsUnique();
            });

            // Configure Train entity
            modelBuilder.Entity<Train>(entity =>
            {
                entity.HasIndex(e => e.TrainNumber).IsUnique();
                entity.Property(e => e.TrainNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.TrainName).HasMaxLength(100);
                entity.Property(e => e.TrainType).HasMaxLength(50);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure Schedule entity
            modelBuilder.Entity<Schedule>(entity =>
            {
                entity.Property(e => e.Status).HasConversion<string>();
                entity.Property(e => e.Remarks).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.Train)
                    .WithMany(p => p.Schedules)
                    .HasForeignKey(d => d.TrainId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Station)
                    .WithMany(p => p.Schedules)
                    .HasForeignKey(d => d.StationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Platform)
                    .WithMany(p => p.Schedules)
                    .HasForeignKey(d => d.PlatformId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure DisplayBoard entity
            modelBuilder.Entity<DisplayBoard>(entity =>
            {
                entity.Property(e => e.BoardType).HasConversion<string>();
                entity.Property(e => e.BoardName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.IpAddress).HasMaxLength(15);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.Station)
                    .WithMany(p => p.DisplayBoards)
                    .HasForeignKey(d => d.StationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.StationId, e.BoardName }).IsUnique();
            });

            // Configure Message entity
            modelBuilder.Entity<Message>(entity =>
            {
                entity.Property(e => e.MessageType).HasConversion<string>();
                entity.Property(e => e.Content).IsRequired();
                entity.Property(e => e.Language).HasMaxLength(10).HasDefaultValue("en");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.DisplayBoard)
                    .WithMany(p => p.Messages)
                    .HasForeignKey(d => d.DisplayBoardId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        /// <summary>
        /// Override SaveChanges to automatically update UpdatedAt timestamps
        /// </summary>
        /// <returns>The number of state entries written to the database</returns>
        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        /// <summary>
        /// Override SaveChangesAsync to automatically update UpdatedAt timestamps
        /// </summary>
        /// <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
        /// <returns>A task that represents the asynchronous save operation</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp for modified entities
        /// </summary>
        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is Station station)
                    station.UpdatedAt = DateTime.UtcNow;
                else if (entry.Entity is Schedule schedule)
                    schedule.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}
