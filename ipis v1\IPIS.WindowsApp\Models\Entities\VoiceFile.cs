using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a voice file entity for announcements
    /// Based on legacy system analysis - supports 1000+ voice segments
    /// </summary>
    [Table("VoiceFiles")]
    public class VoiceFile
    {
        /// <summary>
        /// Primary key for the voice file
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the station (optional, for station-specific voices)
        /// </summary>
        [ForeignKey("Station")]
        public int? StationId { get; set; }

        /// <summary>
        /// Foreign key to the train (optional, for train-specific voices)
        /// </summary>
        [ForeignKey("Train")]
        public int? TrainId { get; set; }

        /// <summary>
        /// Type of announcement this voice file is for
        /// </summary>
        [Required]
        public AnnouncementType AnnouncementType { get; set; }

        /// <summary>
        /// Language of the voice file
        /// </summary>
        [Required]
        public Language Language { get; set; } = Language.English;

        /// <summary>
        /// File name of the voice file
        /// </summary>
        [Required(ErrorMessage = "File name is required")]
        [StringLength(255, ErrorMessage = "File name cannot exceed 255 characters")]
        [Column("FileName")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Full file path to the voice file
        /// </summary>
        [Required(ErrorMessage = "File path is required")]
        [StringLength(500, ErrorMessage = "File path cannot exceed 500 characters")]
        [Column("FilePath")]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// Text content that this voice file represents
        /// </summary>
        [Required(ErrorMessage = "Text content is required")]
        [Column(TypeName = "TEXT")]
        public string TextContent { get; set; } = string.Empty;

        /// <summary>
        /// Phonetic representation for pronunciation guidance
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? PhoneticContent { get; set; }

        /// <summary>
        /// Duration of the voice file in milliseconds
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Duration must be non-negative")]
        public int DurationMs { get; set; } = 0;

        /// <summary>
        /// File size in bytes
        /// </summary>
        [Range(0, long.MaxValue, ErrorMessage = "File size must be non-negative")]
        public long FileSizeBytes { get; set; } = 0;

        /// <summary>
        /// Audio format (WAV, MP3, etc.)
        /// </summary>
        [StringLength(10)]
        public string AudioFormat { get; set; } = "WAV";

        /// <summary>
        /// Sample rate in Hz
        /// </summary>
        [Range(8000, 96000, ErrorMessage = "Sample rate must be between 8000 and 96000 Hz")]
        public int SampleRate { get; set; } = 44100;

        /// <summary>
        /// Bit depth (8, 16, 24, 32)
        /// </summary>
        [Range(8, 32, ErrorMessage = "Bit depth must be between 8 and 32")]
        public int BitDepth { get; set; } = 16;

        /// <summary>
        /// Number of audio channels (1=Mono, 2=Stereo)
        /// </summary>
        [Range(1, 2, ErrorMessage = "Channels must be 1 (Mono) or 2 (Stereo)")]
        public int Channels { get; set; } = 1;

        /// <summary>
        /// Quality rating (1-10, 10 being highest quality)
        /// </summary>
        [Range(1, 10, ErrorMessage = "Quality must be between 1 and 10")]
        public int Quality { get; set; } = 5;

        /// <summary>
        /// Voice artist or speaker name
        /// </summary>
        [StringLength(100)]
        public string? VoiceArtist { get; set; }

        /// <summary>
        /// Gender of the voice (Male, Female, Other)
        /// </summary>
        [StringLength(10)]
        public string? VoiceGender { get; set; }

        /// <summary>
        /// Age category of the voice (Young, Adult, Senior)
        /// </summary>
        [StringLength(10)]
        public string? VoiceAge { get; set; }

        /// <summary>
        /// Voice style or tone (Formal, Friendly, Urgent)
        /// </summary>
        [StringLength(20)]
        public string? VoiceStyle { get; set; }

        /// <summary>
        /// Indicates if this is the primary voice file for the content
        /// </summary>
        public bool IsPrimary { get; set; } = true;

        /// <summary>
        /// Indicates if the voice file is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the file has been verified for quality
        /// </summary>
        public bool IsVerified { get; set; } = false;

        /// <summary>
        /// Verification notes or comments
        /// </summary>
        [StringLength(500)]
        public string? VerificationNotes { get; set; }

        /// <summary>
        /// Date when the file was verified
        /// </summary>
        public DateTime? VerifiedAt { get; set; }

        /// <summary>
        /// User who verified the file
        /// </summary>
        [StringLength(100)]
        public string? VerifiedBy { get; set; }

        /// <summary>
        /// Number of times this voice file has been played
        /// </summary>
        public int PlayCount { get; set; } = 0;

        /// <summary>
        /// Last time this voice file was played
        /// </summary>
        public DateTime? LastPlayedAt { get; set; }

        /// <summary>
        /// MD5 hash of the file for integrity checking
        /// </summary>
        [StringLength(32)]
        public string? FileHash { get; set; }

        /// <summary>
        /// Version number of the voice file
        /// </summary>
        [StringLength(10)]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// Tags for categorization and search
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Additional metadata in JSON format
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Metadata { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// The station this voice file is associated with (optional)
        /// </summary>
        public virtual Station? Station { get; set; }

        /// <summary>
        /// The train this voice file is associated with (optional)
        /// </summary>
        public virtual Train? Train { get; set; }

        // Computed Properties

        /// <summary>
        /// Gets the file extension from the file name
        /// </summary>
        [NotMapped]
        public string FileExtension => Path.GetExtension(FileName).ToLowerInvariant();

        /// <summary>
        /// Gets the file size in a human-readable format
        /// </summary>
        [NotMapped]
        public string FileSizeFormatted
        {
            get
            {
                if (FileSizeBytes < 1024) return $"{FileSizeBytes} B";
                if (FileSizeBytes < 1024 * 1024) return $"{FileSizeBytes / 1024.0:F1} KB";
                if (FileSizeBytes < 1024 * 1024 * 1024) return $"{FileSizeBytes / (1024.0 * 1024.0):F1} MB";
                return $"{FileSizeBytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
            }
        }

        /// <summary>
        /// Gets the duration in a human-readable format
        /// </summary>
        [NotMapped]
        public string DurationFormatted
        {
            get
            {
                var timeSpan = TimeSpan.FromMilliseconds(DurationMs);
                if (timeSpan.TotalHours >= 1)
                    return timeSpan.ToString(@"h\:mm\:ss");
                else
                    return timeSpan.ToString(@"m\:ss");
            }
        }

        /// <summary>
        /// Gets the audio specifications as a formatted string
        /// </summary>
        [NotMapped]
        public string AudioSpecs => $"{SampleRate}Hz, {BitDepth}-bit, {(Channels == 1 ? "Mono" : "Stereo")}";

        /// <summary>
        /// Indicates if the file exists on disk
        /// </summary>
        [NotMapped]
        public bool FileExists => !string.IsNullOrWhiteSpace(FilePath) && File.Exists(FilePath);

        /// <summary>
        /// Gets the quality description
        /// </summary>
        [NotMapped]
        public string QualityDescription => Quality switch
        {
            1 => "Very Poor",
            2 => "Poor",
            3 => "Below Average",
            4 => "Average",
            5 => "Good",
            6 => "Above Average",
            7 => "Very Good",
            8 => "Excellent",
            9 => "Outstanding",
            10 => "Perfect",
            _ => "Unknown"
        };

        /// <summary>
        /// Gets the tags as a list
        /// </summary>
        [NotMapped]
        public List<string> TagList => string.IsNullOrWhiteSpace(Tags) 
            ? new List<string>() 
            : Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                  .Select(tag => tag.Trim())
                  .ToList();

        // Methods

        /// <summary>
        /// Records a play event
        /// </summary>
        public void RecordPlay()
        {
            PlayCount++;
            LastPlayedAt = DateTime.UtcNow;
            UpdateTimestamp();
        }

        /// <summary>
        /// Verifies the voice file
        /// </summary>
        /// <param name="verifiedBy">User who verified the file</param>
        /// <param name="notes">Verification notes</param>
        public void Verify(string verifiedBy, string? notes = null)
        {
            IsVerified = true;
            VerifiedBy = verifiedBy;
            VerifiedAt = DateTime.UtcNow;
            VerificationNotes = notes;
            UpdateTimestamp();
        }

        /// <summary>
        /// Calculates and sets the file hash
        /// </summary>
        /// <returns>The calculated MD5 hash</returns>
        public string CalculateFileHash()
        {
            if (!FileExists) return string.Empty;

            using var md5 = System.Security.Cryptography.MD5.Create();
            using var stream = File.OpenRead(FilePath);
            var hash = md5.ComputeHash(stream);
            FileHash = Convert.ToHexString(hash).ToLowerInvariant();
            UpdateTimestamp();
            return FileHash;
        }

        /// <summary>
        /// Validates the file integrity using stored hash
        /// </summary>
        /// <returns>True if file is valid, false otherwise</returns>
        public bool ValidateFileIntegrity()
        {
            if (string.IsNullOrWhiteSpace(FileHash) || !FileExists)
                return false;

            var currentHash = CalculateFileHash();
            return currentHash.Equals(FileHash, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Adds a tag to the voice file
        /// </summary>
        /// <param name="tag">Tag to add</param>
        public void AddTag(string tag)
        {
            if (string.IsNullOrWhiteSpace(tag)) return;

            var tags = TagList;
            if (!tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
            {
                tags.Add(tag.Trim());
                Tags = string.Join(", ", tags);
                UpdateTimestamp();
            }
        }

        /// <summary>
        /// Removes a tag from the voice file
        /// </summary>
        /// <param name="tag">Tag to remove</param>
        public void RemoveTag(string tag)
        {
            if (string.IsNullOrWhiteSpace(tag)) return;

            var tags = TagList;
            if (tags.RemoveAll(t => t.Equals(tag, StringComparison.OrdinalIgnoreCase)) > 0)
            {
                Tags = string.Join(", ", tags);
                UpdateTimestamp();
            }
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the voice file has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(FileName) && 
                   !string.IsNullOrWhiteSpace(FilePath) && 
                   !string.IsNullOrWhiteSpace(TextContent);
        }

        /// <summary>
        /// Gets a summary of the voice file for display purposes
        /// </summary>
        /// <returns>Voice file summary string</returns>
        public string GetSummary()
        {
            return $"Voice: {FileName}, Language: {Language}, " +
                   $"Type: {AnnouncementType}, Duration: {DurationFormatted}, " +
                   $"Quality: {QualityDescription}";
        }

        /// <summary>
        /// Returns a string representation of the voice file
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{FileName} ({Language})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current voice file
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is VoiceFile other)
            {
                return Id == other.Id;
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the voice file
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
