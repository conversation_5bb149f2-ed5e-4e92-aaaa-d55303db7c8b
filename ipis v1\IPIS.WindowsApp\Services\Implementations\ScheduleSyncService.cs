using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using IPIS.WindowsApp.Configuration;
using IPIS.WindowsApp.Models.DTOs.Railway;
using IPIS.WindowsApp.Services.Interfaces;

namespace IPIS.WindowsApp.Services.Implementations
{
    /// <summary>
    /// Implementation of background schedule synchronization service
    /// Manages continuous synchronization between local system and Railway API
    /// </summary>
    public class ScheduleSyncService : BackgroundService, IScheduleSyncService
    {
        private readonly ILogger<ScheduleSyncService> _logger;
        private readonly SyncConfiguration _config;
        private readonly IRailwayApiService _railwayApiService;
        private readonly Timer? _syncTimer;
        private readonly SemaphoreSlim _syncSemaphore;
        private readonly CancellationTokenSource _cancellationTokenSource;
        
        private bool _isActive = false;
        private bool _isPaused = false;
        private DateTime? _lastSyncTime;
        private DateTime? _nextSyncTime;
        private SyncStatusDto _currentStatus;

        public ScheduleSyncService(
            ILogger<ScheduleSyncService> logger,
            IOptions<SyncConfiguration> config,
            IRailwayApiService railwayApiService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _railwayApiService = railwayApiService ?? throw new ArgumentNullException(nameof(railwayApiService));
            
            _syncSemaphore = new SemaphoreSlim(1, 1);
            _cancellationTokenSource = new CancellationTokenSource();
            
            _currentStatus = new SyncStatusDto
            {
                CurrentStatus = "Initialized",
                IsActive = false
            };

            _logger.LogInformation("Schedule Sync Service initialized");
        }

        #region IScheduleSyncService Implementation

        public bool IsActive => _isActive;
        public bool IsPaused => _isPaused;
        public DateTime? LastSyncTime => _lastSyncTime;
        public DateTime? NextSyncTime => _nextSyncTime;

        #region Events

        public event EventHandler<SyncStartedEventArgs>? SyncStarted;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
        public event EventHandler<SyncFailedEventArgs>? SyncFailed;
        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncStatusChangedEventArgs>? StatusChanged;

        #endregion

        #region Synchronization Control

        public async Task StartSynchronizationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting synchronization service");

                _isActive = true;
                _isPaused = false;
                
                UpdateStatus("Starting", "Synchronization service is starting");
                
                // Calculate next sync time
                _nextSyncTime = DateTime.UtcNow.AddMinutes(_config.IncrementalSyncIntervalMinutes);
                
                UpdateStatus("Active", "Synchronization service is active");
                
                _logger.LogInformation("Synchronization service started successfully. Next sync: {NextSync}", _nextSyncTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting synchronization service");
                _isActive = false;
                UpdateStatus("Error", $"Failed to start: {ex.Message}");
                throw;
            }
        }

        public async Task StopSynchronizationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Stopping synchronization service");

                _isActive = false;
                _isPaused = false;
                _nextSyncTime = null;
                
                UpdateStatus("Stopping", "Synchronization service is stopping");
                
                // Wait for any ongoing sync to complete
                await _syncSemaphore.WaitAsync(cancellationToken);
                try
                {
                    UpdateStatus("Stopped", "Synchronization service has been stopped");
                }
                finally
                {
                    _syncSemaphore.Release();
                }
                
                _logger.LogInformation("Synchronization service stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping synchronization service");
                UpdateStatus("Error", $"Failed to stop: {ex.Message}");
                throw;
            }
        }

        public async Task PauseSynchronizationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Pausing synchronization service");

                _isPaused = true;
                UpdateStatus("Paused", "Synchronization service is paused");
                
                _logger.LogInformation("Synchronization service paused successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pausing synchronization service");
                throw;
            }
        }

        public async Task ResumeSynchronizationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Resuming synchronization service");

                _isPaused = false;
                
                // Recalculate next sync time
                _nextSyncTime = DateTime.UtcNow.AddMinutes(_config.IncrementalSyncIntervalMinutes);
                
                UpdateStatus("Active", "Synchronization service resumed");
                
                _logger.LogInformation("Synchronization service resumed successfully. Next sync: {NextSync}", _nextSyncTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resuming synchronization service");
                throw;
            }
        }

        #endregion

        #region Status and Monitoring

        public async Task<SyncStatusDto> GetSyncStatusAsync(CancellationToken cancellationToken = default)
        {
            return await Task.FromResult(_currentStatus);
        }

        #endregion

        #region Manual Synchronization

        public async Task<SyncStatusDto> ForceFullSyncAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting forced full synchronization");

                var startTime = DateTime.UtcNow;
                OnSyncStarted("Full", startTime);

                await _syncSemaphore.WaitAsync(cancellationToken);
                try
                {
                    UpdateStatus("Syncing", "Performing full synchronization");
                    
                    var result = await PerformFullSyncAsync(cancellationToken);
                    
                    var endTime = DateTime.UtcNow;
                    var duration = endTime - startTime;
                    
                    _lastSyncTime = endTime;
                    _nextSyncTime = endTime.AddHours(_config.FullSyncIntervalHours);
                    
                    UpdateSyncStatus(result, duration);
                    
                    OnSyncCompleted("Full", startTime, endTime, result.IsSuccessful, 
                        result.RecordsProcessed, result.RecordsUpdated, result.RecordsAdded, 
                        result.ConflictsDetected, result.ConflictsResolved, duration);
                    
                    _logger.LogInformation("Forced full synchronization completed. Success: {Success}, Duration: {Duration}ms", 
                        result.IsSuccessful, duration.TotalMilliseconds);
                    
                    return _currentStatus;
                }
                finally
                {
                    _syncSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forced full synchronization");
                OnSyncFailed("Full", DateTime.UtcNow, ex.Message, ex, 0, false);
                UpdateStatus("Error", $"Full sync failed: {ex.Message}");
                throw;
            }
        }

        public async Task<SyncStatusDto> ForceIncrementalSyncAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting forced incremental synchronization");

                var startTime = DateTime.UtcNow;
                OnSyncStarted("Incremental", startTime);

                await _syncSemaphore.WaitAsync(cancellationToken);
                try
                {
                    UpdateStatus("Syncing", "Performing incremental synchronization");
                    
                    var result = await PerformIncrementalSyncAsync(cancellationToken);
                    
                    var endTime = DateTime.UtcNow;
                    var duration = endTime - startTime;
                    
                    _lastSyncTime = endTime;
                    _nextSyncTime = endTime.AddMinutes(_config.IncrementalSyncIntervalMinutes);
                    
                    UpdateSyncStatus(result, duration);
                    
                    OnSyncCompleted("Incremental", startTime, endTime, result.IsSuccessful, 
                        result.RecordsProcessed, result.RecordsUpdated, result.RecordsAdded, 
                        result.ConflictsDetected, result.ConflictsResolved, duration);
                    
                    _logger.LogInformation("Forced incremental synchronization completed. Success: {Success}, Duration: {Duration}ms", 
                        result.IsSuccessful, duration.TotalMilliseconds);
                    
                    return _currentStatus;
                }
                finally
                {
                    _syncSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forced incremental synchronization");
                OnSyncFailed("Incremental", DateTime.UtcNow, ex.Message, ex, 0, false);
                UpdateStatus("Error", $"Incremental sync failed: {ex.Message}");
                throw;
            }
        }

        public async Task<SyncStatusDto> SyncStationsAsync(List<string> stationCodes, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting station-specific synchronization for {Count} stations", stationCodes.Count);

                var startTime = DateTime.UtcNow;
                OnSyncStarted("Stations", startTime, stationCodes);

                await _syncSemaphore.WaitAsync(cancellationToken);
                try
                {
                    UpdateStatus("Syncing", $"Synchronizing {stationCodes.Count} stations");
                    
                    var result = await PerformStationSyncAsync(stationCodes, cancellationToken);
                    
                    var endTime = DateTime.UtcNow;
                    var duration = endTime - startTime;
                    
                    _lastSyncTime = endTime;
                    
                    UpdateSyncStatus(result, duration);
                    
                    OnSyncCompleted("Stations", startTime, endTime, result.IsSuccessful, 
                        result.RecordsProcessed, result.RecordsUpdated, result.RecordsAdded, 
                        result.ConflictsDetected, result.ConflictsResolved, duration);
                    
                    _logger.LogInformation("Station synchronization completed. Success: {Success}, Duration: {Duration}ms", 
                        result.IsSuccessful, duration.TotalMilliseconds);
                    
                    return _currentStatus;
                }
                finally
                {
                    _syncSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during station synchronization");
                OnSyncFailed("Stations", DateTime.UtcNow, ex.Message, ex, 0, false);
                UpdateStatus("Error", $"Station sync failed: {ex.Message}");
                throw;
            }
        }

        public async Task<SyncStatusDto> SyncTrainsAsync(List<string> trainNumbers, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting train-specific synchronization for {Count} trains", trainNumbers.Count);

                var startTime = DateTime.UtcNow;
                OnSyncStarted("Trains", startTime, trainNumbers: trainNumbers);

                await _syncSemaphore.WaitAsync(cancellationToken);
                try
                {
                    UpdateStatus("Syncing", $"Synchronizing {trainNumbers.Count} trains");
                    
                    var result = await PerformTrainSyncAsync(trainNumbers, cancellationToken);
                    
                    var endTime = DateTime.UtcNow;
                    var duration = endTime - startTime;
                    
                    _lastSyncTime = endTime;
                    
                    UpdateSyncStatus(result, duration);
                    
                    OnSyncCompleted("Trains", startTime, endTime, result.IsSuccessful, 
                        result.RecordsProcessed, result.RecordsUpdated, result.RecordsAdded, 
                        result.ConflictsDetected, result.ConflictsResolved, duration);
                    
                    _logger.LogInformation("Train synchronization completed. Success: {Success}, Duration: {Duration}ms", 
                        result.IsSuccessful, duration.TotalMilliseconds);
                    
                    return _currentStatus;
                }
                finally
                {
                    _syncSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during train synchronization");
                OnSyncFailed("Trains", DateTime.UtcNow, ex.Message, ex, 0, false);
                UpdateStatus("Error", $"Train sync failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #endregion

        #region BackgroundService Implementation

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Schedule Sync Service background task started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (_isActive && !_isPaused && _nextSyncTime.HasValue && DateTime.UtcNow >= _nextSyncTime.Value)
                    {
                        await PerformScheduledSyncAsync(stoppingToken);
                    }

                    // Wait for a short interval before checking again
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in background sync task");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait before retrying
                }
            }

            _logger.LogInformation("Schedule Sync Service background task stopped");
        }

        #endregion

        #region Private Helper Methods

        private async Task PerformScheduledSyncAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogDebug("Performing scheduled synchronization");

                // Determine sync type based on last full sync time
                var shouldPerformFullSync = ShouldPerformFullSync();
                var syncType = shouldPerformFullSync ? "Full" : "Incremental";

                var startTime = DateTime.UtcNow;
                OnSyncStarted(syncType, startTime);

                await _syncSemaphore.WaitAsync(cancellationToken);
                try
                {
                    UpdateStatus("Syncing", $"Performing scheduled {syncType.ToLower()} synchronization");

                    SyncResult result;
                    if (shouldPerformFullSync)
                    {
                        result = await PerformFullSyncAsync(cancellationToken);
                    }
                    else
                    {
                        result = await PerformIncrementalSyncAsync(cancellationToken);
                    }

                    var endTime = DateTime.UtcNow;
                    var duration = endTime - startTime;

                    _lastSyncTime = endTime;
                    _nextSyncTime = CalculateNextSyncTime(shouldPerformFullSync);

                    UpdateSyncStatus(result, duration);

                    OnSyncCompleted(syncType, startTime, endTime, result.IsSuccessful,
                        result.RecordsProcessed, result.RecordsUpdated, result.RecordsAdded,
                        result.ConflictsDetected, result.ConflictsResolved, duration);

                    _logger.LogInformation("Scheduled {SyncType} synchronization completed. Success: {Success}, Next sync: {NextSync}",
                        syncType, result.IsSuccessful, _nextSyncTime);
                }
                finally
                {
                    _syncSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during scheduled synchronization");
                OnSyncFailed("Scheduled", DateTime.UtcNow, ex.Message, ex, 0, false);
                UpdateStatus("Error", $"Scheduled sync failed: {ex.Message}");

                // Schedule retry
                _nextSyncTime = DateTime.UtcNow.AddMinutes(5); // Retry in 5 minutes
            }
        }

        private bool ShouldPerformFullSync()
        {
            if (_lastSyncTime == null) return true; // First sync should be full

            var timeSinceLastSync = DateTime.UtcNow - _lastSyncTime.Value;
            return timeSinceLastSync.TotalHours >= _config.FullSyncIntervalHours;
        }

        private DateTime CalculateNextSyncTime(bool wasFullSync)
        {
            if (wasFullSync)
            {
                return DateTime.UtcNow.AddHours(_config.FullSyncIntervalHours);
            }
            else
            {
                return DateTime.UtcNow.AddMinutes(_config.IncrementalSyncIntervalMinutes);
            }
        }

        private async Task<SyncResult> PerformFullSyncAsync(CancellationToken cancellationToken)
        {
            var result = new SyncResult { IsSuccessful = true };

            try
            {
                // Perform all sync operations
                var scheduleSuccess = await _railwayApiService.SyncScheduleDataAsync(cancellationToken);
                var statusSuccess = await _railwayApiService.SyncTrainStatusAsync(cancellationToken);
                var delaySuccess = await _railwayApiService.SyncDelayInformationAsync(cancellationToken);
                var platformSuccess = await _railwayApiService.SyncPlatformAssignmentsAsync(cancellationToken);

                result.IsSuccessful = scheduleSuccess && statusSuccess && delaySuccess && platformSuccess;
                result.RecordsProcessed = 100; // Placeholder - would be actual count
                result.RecordsUpdated = 80;
                result.RecordsAdded = 20;

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private async Task<SyncResult> PerformIncrementalSyncAsync(CancellationToken cancellationToken)
        {
            var result = new SyncResult { IsSuccessful = true };

            try
            {
                // Perform incremental sync operations (typically just delays and status)
                var statusSuccess = await _railwayApiService.SyncTrainStatusAsync(cancellationToken);
                var delaySuccess = await _railwayApiService.SyncDelayInformationAsync(cancellationToken);

                result.IsSuccessful = statusSuccess && delaySuccess;
                result.RecordsProcessed = 50; // Placeholder
                result.RecordsUpdated = 45;
                result.RecordsAdded = 5;

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private async Task<SyncResult> PerformStationSyncAsync(List<string> stationCodes, CancellationToken cancellationToken)
        {
            var result = new SyncResult { IsSuccessful = true };

            try
            {
                var recordsProcessed = 0;
                foreach (var stationCode in stationCodes)
                {
                    var schedules = await _railwayApiService.GetTrainSchedulesAsync(stationCode, DateTime.Today, cancellationToken);
                    var assignments = await _railwayApiService.GetPlatformAssignmentsAsync(stationCode, cancellationToken);
                    recordsProcessed += schedules.Count + assignments.Count;
                }

                result.RecordsProcessed = recordsProcessed;
                result.RecordsUpdated = recordsProcessed;

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private async Task<SyncResult> PerformTrainSyncAsync(List<string> trainNumbers, CancellationToken cancellationToken)
        {
            var result = new SyncResult { IsSuccessful = true };

            try
            {
                var statuses = await _railwayApiService.GetTrainStatusesAsync(trainNumbers, cancellationToken);
                
                result.RecordsProcessed = statuses.Count;
                result.RecordsUpdated = statuses.Count;

                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private void UpdateStatus(string status, string? reason = null)
        {
            var previousStatus = _currentStatus.CurrentStatus;
            
            _currentStatus.CurrentStatus = status;
            _currentStatus.IsActive = _isActive;
            _currentStatus.LastSyncTime = _lastSyncTime;
            _currentStatus.NextSyncTime = _nextSyncTime;

            OnStatusChanged(previousStatus, status, reason);
        }

        private void UpdateSyncStatus(SyncResult result, TimeSpan duration)
        {
            _currentStatus.RecordsProcessed = result.RecordsProcessed;
            _currentStatus.RecordsUpdated = result.RecordsUpdated;
            _currentStatus.RecordsAdded = result.RecordsAdded;
            _currentStatus.ConflictsDetected = result.ConflictsDetected;
            _currentStatus.ConflictsResolved = result.ConflictsResolved;
            _currentStatus.LastSyncDuration = duration;
            _currentStatus.LastSyncTime = _lastSyncTime;
            _currentStatus.NextSyncTime = _nextSyncTime;

            if (!result.IsSuccessful && !string.IsNullOrWhiteSpace(result.ErrorMessage))
            {
                _currentStatus.Errors.Add(result.ErrorMessage);
            }

            UpdateStatus(_isActive ? "Active" : "Stopped");
        }

        #endregion

        #region Event Handlers

        private void OnSyncStarted(string syncType, DateTime startTime, List<string>? stationCodes = null, List<string>? trainNumbers = null)
        {
            SyncStarted?.Invoke(this, new SyncStartedEventArgs
            {
                SyncType = syncType,
                StartTime = startTime,
                StationCodes = stationCodes,
                TrainNumbers = trainNumbers
            });
        }

        private void OnSyncCompleted(string syncType, DateTime startTime, DateTime endTime, bool isSuccessful,
            int recordsProcessed, int recordsUpdated, int recordsAdded, int conflictsDetected, int conflictsResolved, TimeSpan duration)
        {
            SyncCompleted?.Invoke(this, new SyncCompletedEventArgs
            {
                SyncType = syncType,
                StartTime = startTime,
                EndTime = endTime,
                IsSuccessful = isSuccessful,
                RecordsProcessed = recordsProcessed,
                RecordsUpdated = recordsUpdated,
                RecordsAdded = recordsAdded,
                ConflictsDetected = conflictsDetected,
                ConflictsResolved = conflictsResolved,
                Duration = duration
            });
        }

        private void OnSyncFailed(string syncType, DateTime failTime, string errorMessage, Exception? exception, int retryAttempt, bool willRetry)
        {
            SyncFailed?.Invoke(this, new SyncFailedEventArgs
            {
                SyncType = syncType,
                StartTime = failTime,
                FailTime = failTime,
                ErrorMessage = errorMessage,
                Exception = exception,
                RetryAttempt = retryAttempt,
                WillRetry = willRetry
            });
        }

        private void OnStatusChanged(string previousStatus, string newStatus, string? reason)
        {
            StatusChanged?.Invoke(this, new SyncStatusChangedEventArgs
            {
                PreviousStatus = previousStatus,
                NewStatus = newStatus,
                StatusChangedAt = DateTime.UtcNow,
                Reason = reason
            });
        }

        #endregion

        #region Configuration Methods

        public async Task UpdateSyncIntervalAsync(int intervalMinutes)
        {
            _config.IncrementalSyncIntervalMinutes = intervalMinutes;
            
            if (_isActive && !_isPaused)
            {
                _nextSyncTime = DateTime.UtcNow.AddMinutes(intervalMinutes);
                _logger.LogInformation("Sync interval updated to {Interval} minutes. Next sync: {NextSync}", intervalMinutes, _nextSyncTime);
            }
        }

        public async Task SetRealTimeSyncAsync(bool enabled)
        {
            // This would update the configuration for real-time sync
            _logger.LogInformation("Real-time sync {Status}", enabled ? "enabled" : "disabled");
        }

        public async Task SetMaxConcurrentOperationsAsync(int maxConcurrent)
        {
            // This would update the configuration for max concurrent operations
            _logger.LogInformation("Max concurrent operations set to {MaxConcurrent}", maxConcurrent);
        }

        #endregion

        #region Statistics and Reporting

        public async Task<SyncStatistics> GetDailyStatisticsAsync(CancellationToken cancellationToken = default)
        {
            // This would query the database for daily statistics
            return new SyncStatistics
            {
                StartDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(1),
                TotalSyncOperations = 10,
                SuccessfulSyncs = 9,
                FailedSyncs = 1,
                TotalRecordsProcessed = 1000,
                TotalRecordsUpdated = 800,
                TotalRecordsAdded = 200
            };
        }

        public async Task<SyncStatistics> GetStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            // This would query the database for statistics in the date range
            return new SyncStatistics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSyncOperations = 50,
                SuccessfulSyncs = 45,
                FailedSyncs = 5,
                TotalRecordsProcessed = 5000,
                TotalRecordsUpdated = 4000,
                TotalRecordsAdded = 1000
            };
        }

        public async Task<List<SyncHistoryRecord>> GetSyncHistoryAsync(int limit = 100, CancellationToken cancellationToken = default)
        {
            // This would query the database for sync history
            return new List<SyncHistoryRecord>();
        }

        #endregion

        #region IDisposable Implementation

        public override void Dispose()
        {
            _syncSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
            base.Dispose();
        }

        #endregion

        #region Helper Classes

        private class SyncResult
        {
            public bool IsSuccessful { get; set; }
            public int RecordsProcessed { get; set; }
            public int RecordsUpdated { get; set; }
            public int RecordsAdded { get; set; }
            public int ConflictsDetected { get; set; }
            public int ConflictsResolved { get; set; }
            public string? ErrorMessage { get; set; }
        }

        #endregion
    }
}
