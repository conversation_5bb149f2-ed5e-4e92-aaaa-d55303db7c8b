using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for train route station information
    /// </summary>
    public class TrainRouteStationDto
    {
        /// <summary>
        /// Station code
        /// </summary>
        [Required]
        public string StationCode { get; set; } = string.Empty;

        /// <summary>
        /// Station name
        /// </summary>
        public string StationName { get; set; } = string.Empty;

        /// <summary>
        /// Sequence number in the route
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// Distance from source station in kilometers
        /// </summary>
        public double? DistanceFromSource { get; set; }

        /// <summary>
        /// Scheduled arrival time
        /// </summary>
        public DateTime? ScheduledArrival { get; set; }

        /// <summary>
        /// Scheduled departure time
        /// </summary>
        public DateTime? ScheduledDeparture { get; set; }

        /// <summary>
        /// Expected arrival time (with delays)
        /// </summary>
        public DateTime? ExpectedArrival { get; set; }

        /// <summary>
        /// Expected departure time (with delays)
        /// </summary>
        public DateTime? ExpectedDeparture { get; set; }

        /// <summary>
        /// Actual arrival time
        /// </summary>
        public DateTime? ActualArrival { get; set; }

        /// <summary>
        /// Actual departure time
        /// </summary>
        public DateTime? ActualDeparture { get; set; }

        /// <summary>
        /// Platform number
        /// </summary>
        public string? Platform { get; set; }

        /// <summary>
        /// Halt duration in minutes
        /// </summary>
        public int? HaltDuration { get; set; }

        /// <summary>
        /// Current status at this station
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Delay in minutes at this station
        /// </summary>
        public int DelayMinutes { get; set; }

        /// <summary>
        /// Whether this is an important/major station
        /// </summary>
        public bool IsImportantStation { get; set; }

        /// <summary>
        /// Whether this is the source station
        /// </summary>
        public bool IsSourceStation { get; set; }

        /// <summary>
        /// Whether this is the destination station
        /// </summary>
        public bool IsDestinationStation { get; set; }

        /// <summary>
        /// Whether the train has arrived at this station
        /// </summary>
        public bool HasArrived => ActualArrival.HasValue;

        /// <summary>
        /// Whether the train has departed from this station
        /// </summary>
        public bool HasDeparted => ActualDeparture.HasValue;

        /// <summary>
        /// Whether this is the current station (train is here now)
        /// </summary>
        public bool IsCurrentStation => HasArrived && !HasDeparted;

        /// <summary>
        /// Whether this station is upcoming
        /// </summary>
        public bool IsUpcoming => !HasArrived && ScheduledArrival > DateTime.Now;

        /// <summary>
        /// Whether this station is completed (train has departed)
        /// </summary>
        public bool IsCompleted => HasDeparted;

        /// <summary>
        /// Time remaining for arrival (in minutes)
        /// </summary>
        public int? MinutesToArrival
        {
            get
            {
                if (HasArrived) return 0;
                var arrivalTime = ExpectedArrival ?? ScheduledArrival;
                if (arrivalTime.HasValue)
                {
                    var diff = arrivalTime.Value - DateTime.Now;
                    return diff.TotalMinutes > 0 ? (int)diff.TotalMinutes : 0;
                }
                return null;
            }
        }

        /// <summary>
        /// Time remaining for departure (in minutes)
        /// </summary>
        public int? MinutesToDeparture
        {
            get
            {
                if (HasDeparted) return 0;
                var departureTime = ExpectedDeparture ?? ScheduledDeparture;
                if (departureTime.HasValue)
                {
                    var diff = departureTime.Value - DateTime.Now;
                    return diff.TotalMinutes > 0 ? (int)diff.TotalMinutes : 0;
                }
                return null;
            }
        }

        /// <summary>
        /// Formatted delay text
        /// </summary>
        public string DelayText
        {
            get
            {
                if (DelayMinutes == 0) return "On Time";
                if (DelayMinutes > 0) return $"Late by {DelayMinutes} min";
                return $"Early by {Math.Abs(DelayMinutes)} min";
            }
        }

        /// <summary>
        /// Status color for UI display
        /// </summary>
        public string StatusColor
        {
            get
            {
                if (IsCompleted) return "Green";
                if (IsCurrentStation) return "Blue";
                if (DelayMinutes > 30) return "Red";
                if (DelayMinutes > 15) return "Orange";
                return "Gray";
            }
        }

        /// <summary>
        /// Station type icon for UI
        /// </summary>
        public string StationIcon
        {
            get
            {
                if (IsSourceStation) return "🚀";
                if (IsDestinationStation) return "🏁";
                if (IsImportantStation) return "🚉";
                return "⚫";
            }
        }

        /// <summary>
        /// Formatted arrival time string
        /// </summary>
        public string ArrivalTimeText
        {
            get
            {
                if (ActualArrival.HasValue)
                    return $"Arrived: {ActualArrival.Value:HH:mm}";
                if (ExpectedArrival.HasValue)
                    return $"Expected: {ExpectedArrival.Value:HH:mm}";
                if (ScheduledArrival.HasValue)
                    return $"Scheduled: {ScheduledArrival.Value:HH:mm}";
                return "N/A";
            }
        }

        /// <summary>
        /// Formatted departure time string
        /// </summary>
        public string DepartureTimeText
        {
            get
            {
                if (ActualDeparture.HasValue)
                    return $"Departed: {ActualDeparture.Value:HH:mm}";
                if (ExpectedDeparture.HasValue)
                    return $"Expected: {ExpectedDeparture.Value:HH:mm}";
                if (ScheduledDeparture.HasValue)
                    return $"Scheduled: {ScheduledDeparture.Value:HH:mm}";
                return "N/A";
            }
        }

        /// <summary>
        /// Station status description
        /// </summary>
        public string StationStatusDescription
        {
            get
            {
                if (IsCompleted) return "Completed";
                if (IsCurrentStation) return "Current Station";
                if (IsUpcoming) return "Upcoming";
                return "Unknown";
            }
        }
    }
}
