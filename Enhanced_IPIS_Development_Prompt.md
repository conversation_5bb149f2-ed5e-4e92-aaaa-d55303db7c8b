# Enhanced IPIS Windows Forms Application Development Prompt

## Project Overview
Create a comprehensive .NET 6 Windows Forms application for the IPIS (Integrated Passenger Information System) that serves as a modernized desktop client for railway station management, incorporating lessons learned from the legacy VB.NET system analysis.

## Project Location and Structure
- **Project Path**: `E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp`
- **Solution Name**: `IPIS.WindowsApp`
- **Target Framework**: `net6.0-windows` (LTS .NET 6)
- **Project Type**: Windows Forms App (.NET)
- **Architecture Pattern**: Layered architecture with separation of concerns

## Technology Stack Requirements

### Core Framework
- **Framework**: .NET 6 with Windows Forms support
- **Database**: SQLite with Entity Framework Core 6.x
- **ORM**: Microsoft.EntityFrameworkCore.Sqlite (version 6.0.x)
- **Logging**: Serilog with structured logging
- **Configuration**: Microsoft.Extensions.Configuration with JSON support
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Validation**: System.ComponentModel.DataAnnotations

### Required NuGet Packages
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.25" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.25" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.25" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.25" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
<PackageReference Include="Serilog" Version="2.12.0" />
<PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
<PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
```

## Database Configuration

### SQLite Setup
- **Database File**: `ipis.db` in project root directory
- **Connection String**: `Data Source=ipis.db` (relative path)
- **Build Action**: Copy to Output Directory: Copy always
- **Auto-Migration**: Database created automatically on first run
- **Backup Strategy**: Implement automatic backup functionality

### Entity Framework Configuration
- Code-First approach with migrations
- Proper foreign key relationships and constraints
- Indexes for performance optimization
- Soft delete implementation where appropriate
- Audit trail for data changes

## Enhanced Entity Model Requirements

### Core Entities (Based on Legacy System Analysis)

#### 1. Station Entity
```csharp
- Id (Primary Key, Auto-increment)
- Code (Unique, 10 chars, Required) // e.g., "NYC", "BOS"
- Name (100 chars, Required)
- Location (200 chars, Optional)
- IsActive (Boolean, Default: true)
- StationType (Enum: Terminal, Junction, Regular)
- TimeZone (String, Default: "UTC")
- CreatedAt (DateTime, Auto-generated)
- UpdatedAt (DateTime, Auto-updated)
- CreatedBy (String, User tracking)
- UpdatedBy (String, User tracking)
```

#### 2. Platform Entity
```csharp
- Id (Primary Key, Auto-increment)
- StationId (Foreign Key to Station)
- PlatformNumber (10 chars, Required) // e.g., "1", "2A", "3B"
- PlatformName (50 chars, Optional)
- PlatformType (Enum: Passenger, Freight, Mixed)
- TrackNumber (String, Optional)
- Length (Decimal, Optional) // Platform length in meters
- IsActive (Boolean, Default: true)
- HasCover (Boolean, Default: false)
- Accessibility (Boolean, Default: false) // Wheelchair accessible
- CreatedAt (DateTime, Auto-generated)
```

#### 3. Train Entity
```csharp
- Id (Primary Key, Auto-increment)
- TrainNumber (20 chars, Unique, Required)
- TrainName (100 chars, Optional)
- TrainType (Enum: Express, Local, Freight, Special)
- OperatorCode (10 chars, Optional)
- MaxSpeed (Integer, Optional) // km/h
- CoachCount (Integer, Optional)
- IsActive (Boolean, Default: true)
- CreatedAt (DateTime, Auto-generated)
```

#### 4. Schedule Entity (Enhanced from Legacy System)
```csharp
- Id (Primary Key, Auto-increment)
- TrainId (Foreign Key to Train)
- StationId (Foreign Key to Station)
- PlatformId (Foreign Key to Platform, Optional)
- ScheduledArrival (DateTime, Optional)
- ScheduledDeparture (DateTime, Optional)
- ActualArrival (DateTime, Optional)
- ActualDeparture (DateTime, Optional)
- Status (Enum: Scheduled, OnTime, Delayed, Cancelled, Departed, Arrived)
- DelayMinutes (Integer, Default: 0)
- DelayReason (String, Optional)
- Remarks (500 chars, Optional)
- IsPublished (Boolean, Default: false)
- Priority (Integer, Default: 1) // 1=High, 5=Low
- CreatedAt (DateTime, Auto-generated)
- UpdatedAt (DateTime, Auto-updated)
```

#### 5. DisplayBoard Entity (Based on Legacy AGDB, CGDB, MLDB, PDB)
```csharp
- Id (Primary Key, Auto-increment)
- StationId (Foreign Key to Station)
- BoardType (Enum: AGDB, CGDB, MLDB, PDB)
- BoardName (100 chars, Required)
- BoardLocation (100 chars, Optional)
- IpAddress (15 chars, Optional)
- Port (Integer, Optional)
- Protocol (Enum: TCP, UDP, Serial)
- IsOnline (Boolean, Default: false)
- LastHeartbeat (DateTime, Optional)
- Configuration (JSON, Optional) // Board-specific settings
- DisplayLanguages (String, Default: "en") // Comma-separated
- RefreshInterval (Integer, Default: 30) // seconds
- CreatedAt (DateTime, Auto-generated)
```

#### 6. Message Entity (Enhanced for Multi-language Support)
```csharp
- Id (Primary Key, Auto-increment)
- DisplayBoardId (Foreign Key to DisplayBoard, Optional)
- StationId (Foreign Key to Station, Optional) // For station-wide messages
- MessageType (Enum: Train, Announcement, Emergency, Information, Weather)
- Content (Text, Required)
- ContentHtml (Text, Optional) // Rich text content
- Language (10 chars, Default: "en")
- Priority (Integer, Default: 1) // 1=Highest, 10=Lowest
- IsActive (Boolean, Default: true)
- IsScrolling (Boolean, Default: false)
- ScrollSpeed (Integer, Optional)
- ValidFrom (DateTime, Default: Now)
- ValidTo (DateTime, Optional)
- RepeatCount (Integer, Default: 1)
- AudioEnabled (Boolean, Default: false)
- CreatedAt (DateTime, Auto-generated)
- CreatedBy (String, User tracking)
```

#### 7. User Entity (New - Based on Legacy System Analysis)
```csharp
- Id (Primary Key, Auto-increment)
- Username (50 chars, Unique, Required)
- Email (100 chars, Unique, Required)
- PasswordHash (255 chars, Required)
- FirstName (50 chars, Required)
- LastName (50 chars, Required)
- Role (Enum: Admin, Operator, Viewer, Maintenance)
- StationAccess (JSON, Optional) // Array of accessible station IDs
- IsActive (Boolean, Default: true)
- LastLoginAt (DateTime, Optional)
- FailedLoginAttempts (Integer, Default: 0)
- AccountLockedUntil (DateTime, Optional)
- CreatedAt (DateTime, Auto-generated)
- UpdatedAt (DateTime, Auto-updated)
```

#### 8. AuditLog Entity (New - For Compliance)
```csharp
- Id (Primary Key, Auto-increment)
- UserId (Foreign Key to User, Optional)
- EntityType (String, Required) // Table name
- EntityId (String, Required) // Record ID
- Action (Enum: Create, Update, Delete, View)
- OldValues (JSON, Optional)
- NewValues (JSON, Optional)
- IpAddress (String, Optional)
- UserAgent (String, Optional)
- Timestamp (DateTime, Auto-generated)
```

## Enhanced Service Layer Requirements

### Core Services
1. **IStationService / StationService**
   - Full CRUD operations with validation
   - Station search and filtering
   - Platform management within stations
   - Station status management

2. **ITrainService / TrainService**
   - Train registration and management
   - Train type categorization
   - Operator management

3. **IScheduleService / ScheduleService**
   - Schedule creation and management
   - Real-time updates and delay tracking
   - Schedule conflict detection
   - Bulk schedule operations

4. **IDisplayBoardService / DisplayBoardService**
   - Board configuration and management
   - Content generation for different board types
   - Board status monitoring
   - Message distribution

5. **IMessageService / MessageService**
   - Message creation and management
   - Multi-language message support
   - Message scheduling and expiration
   - Emergency message broadcasting

6. **IUserService / UserService** (New)
   - User authentication and authorization
   - Role-based access control
   - Session management
   - Password policy enforcement

7. **IAuditService / AuditService** (New)
   - Audit trail logging
   - Compliance reporting
   - Data change tracking

### Service Implementation Requirements
- Async/await pattern for all database operations
- Comprehensive error handling with custom exceptions
- Input validation using Data Annotations
- Business rule enforcement
- Transaction management for complex operations
- Caching for frequently accessed data
- Logging for all operations

## Enhanced UI Requirements

### Main Application Window
- **Layout**: Professional MDI or tabbed interface
- **Menu System**: Comprehensive menu with keyboard shortcuts
- **Toolbar**: Context-sensitive toolbars
- **Status Bar**: Multi-panel status information
- **Theme Support**: Light/Dark theme switching

### Core Forms and Features

#### 1. Station Management
- Station list with advanced filtering and sorting
- Station details form with platform management
- Station status dashboard
- Platform assignment interface

#### 2. Train Management
- Train registry with search capabilities
- Train details and specifications
- Train type management
- Operator information

#### 3. Schedule Management
- Schedule grid with real-time updates
- Schedule creation wizard
- Delay tracking and reporting
- Conflict resolution interface

#### 4. Display Board Management
- Board configuration interface
- Live board preview
- Board status monitoring
- Message composition and scheduling

#### 5. User Management (New)
- User account management
- Role assignment interface
- Access control configuration
- Login audit trail

#### 6. System Administration (New)
- Database backup and restore
- System configuration
- Audit log viewer
- Performance monitoring

### UI Enhancement Features
- **Data Validation**: Real-time validation with user-friendly error messages
- **Auto-complete**: Smart auto-complete for common fields
- **Keyboard Navigation**: Full keyboard support
- **Accessibility**: Screen reader support and high contrast mode
- **Responsive Design**: Proper form resizing and layout management
- **Context Menus**: Right-click context menus for common operations

## Configuration and Settings

### appsettings.json Structure
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=ipis.db",
    "BackupConnection": "Data Source=backup/ipis_backup.db"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": "Information",
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "path": "logs/ipis-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  },
  "Application": {
    "Name": "IPIS Windows Application",
    "Version": "1.0.0",
    "DatabaseAutoMigrate": true,
    "BackupEnabled": true,
    "BackupIntervalHours": 24,
    "SessionTimeoutMinutes": 30,
    "MaxLoginAttempts": 5,
    "PasswordMinLength": 8
  },
  "Display": {
    "DefaultRefreshInterval": 30,
    "MaxMessagesPerBoard": 10,
    "DefaultLanguage": "en",
    "SupportedLanguages": ["en", "es", "fr"]
  }
}
```

## Security and Compliance Requirements

### Security Features
- Password hashing using BCrypt or similar
- Session management with timeout
- Role-based access control (RBAC)
- Input sanitization and SQL injection prevention
- Audit logging for compliance
- Data encryption for sensitive information

### Compliance Features
- GDPR compliance for user data
- Audit trail for all data changes
- Data retention policies
- Backup and recovery procedures
- Access control documentation

## Performance and Scalability

### Performance Requirements
- Application startup time < 5 seconds
- Database query response time < 1 second
- UI responsiveness with background operations
- Efficient memory usage
- Proper resource disposal

### Scalability Considerations
- Database indexing strategy
- Connection pooling
- Caching implementation
- Asynchronous operations
- Batch processing for bulk operations

## Testing Requirements

### Unit Testing
- Service layer unit tests with 80%+ coverage
- Repository pattern testing
- Business logic validation testing
- Error handling testing

### Integration Testing
- Database integration tests
- Service integration tests
- UI automation tests (basic)

## Documentation Requirements

### Code Documentation
- XML documentation for all public APIs
- Inline comments for complex business logic
- README file with setup instructions
- Database schema documentation

### User Documentation
- User manual with screenshots
- Administrator guide
- Troubleshooting guide
- Installation instructions

## Deployment and Maintenance

### Deployment Package
- Self-contained deployment option
- Database migration scripts
- Configuration templates
- Installation wizard

### Maintenance Features
- Automatic backup functionality
- Database maintenance tools
- Log file management
- Performance monitoring

## Success Criteria

### Technical Criteria
- Application builds without errors or warnings
- All unit tests pass
- Database migrations work correctly
- Performance requirements met

### Functional Criteria
- All CRUD operations work correctly
- Real-time updates function properly
- Multi-user support works
- Data integrity maintained

### User Experience Criteria
- Intuitive user interface
- Responsive application performance
- Comprehensive error handling
- Professional appearance and behavior

## Implementation Guidelines

### Project Structure
```
IPIS.WindowsApp/
├── Models/
│   ├── Entities/
│   │   ├── Station.cs
│   │   ├── Platform.cs
│   │   ├── Train.cs
│   │   ├── Schedule.cs
│   │   ├── DisplayBoard.cs
│   │   ├── Message.cs
│   │   ├── User.cs
│   │   └── AuditLog.cs
│   ├── Enums/
│   │   ├── ScheduleStatus.cs
│   │   ├── DisplayBoardType.cs
│   │   ├── MessageType.cs
│   │   └── UserRole.cs
│   └── DTOs/
│       ├── StationDto.cs
│       ├── ScheduleDto.cs
│       └── MessageDto.cs
├── Data/
│   ├── IPISDbContext.cs
│   ├── Configurations/
│   │   ├── StationConfiguration.cs
│   │   ├── ScheduleConfiguration.cs
│   │   └── MessageConfiguration.cs
│   └── Migrations/
├── Services/
│   ├── Interfaces/
│   │   ├── IStationService.cs
│   │   ├── ITrainService.cs
│   │   ├── IScheduleService.cs
│   │   ├── IDisplayBoardService.cs
│   │   ├── IMessageService.cs
│   │   ├── IUserService.cs
│   │   └── IAuditService.cs
│   └── Implementations/
│       ├── StationService.cs
│       ├── TrainService.cs
│       ├── ScheduleService.cs
│       ├── DisplayBoardService.cs
│       ├── MessageService.cs
│       ├── UserService.cs
│       └── AuditService.cs
├── Forms/
│   ├── MainForm.cs
│   ├── StationManagementForm.cs
│   ├── TrainManagementForm.cs
│   ├── ScheduleManagementForm.cs
│   ├── DisplayBoardForm.cs
│   ├── MessageComposerForm.cs
│   ├── UserManagementForm.cs
│   ├── LoginForm.cs
│   └── SettingsForm.cs
├── Common/
│   ├── Extensions/
│   ├── Helpers/
│   ├── Constants/
│   └── Exceptions/
├── Resources/
│   ├── Images/
│   ├── Icons/
│   └── Strings/
├── appsettings.json
├── Program.cs
└── IPIS.WindowsApp.csproj
```

### Database Migration Strategy
1. **Initial Migration**: Create all base tables with relationships
2. **Seed Data**: Insert default stations, users, and configuration data
3. **Version Control**: Track all schema changes through migrations
4. **Rollback Support**: Implement migration rollback capabilities

### Error Handling Strategy
```csharp
// Custom Exception Classes
public class IPISException : Exception
public class ValidationException : IPISException
public class BusinessRuleException : IPISException
public class DataAccessException : IPISException
public class SecurityException : IPISException

// Global Exception Handler
public class GlobalExceptionHandler
{
    public static void HandleException(Exception ex, ILogger logger)
    {
        // Log exception details
        // Show user-friendly message
        // Report critical errors
    }
}
```

### Logging Strategy
- **Structured Logging**: Use Serilog with structured data
- **Log Levels**: Debug, Information, Warning, Error, Fatal
- **Log Categories**: Database, UI, Business Logic, Security, Performance
- **Log Retention**: 30 days for regular logs, 1 year for audit logs

### Data Validation Framework
```csharp
// Validation Attributes
[Required(ErrorMessage = "Station code is required")]
[StringLength(10, ErrorMessage = "Station code cannot exceed 10 characters")]
[RegularExpression(@"^[A-Z0-9]+$", ErrorMessage = "Station code must contain only uppercase letters and numbers")]
public string Code { get; set; }

// Custom Validation
public class UniqueStationCodeAttribute : ValidationAttribute
{
    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // Custom validation logic
    }
}
```

### Caching Strategy
- **Memory Caching**: For frequently accessed reference data
- **Cache Invalidation**: Automatic cache refresh on data changes
- **Cache Keys**: Structured naming convention for cache keys
- **Cache Duration**: Configurable cache expiration times

### Background Services
```csharp
// Background Service for Real-time Updates
public class ScheduleUpdateService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            // Check for schedule updates
            // Notify display boards
            // Update UI components
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
        }
    }
}
```

### Integration Points
1. **Legacy System Integration**: Import data from existing Access databases
2. **External APIs**: Weather services, train tracking systems
3. **Hardware Integration**: Display board controllers, audio systems
4. **Export Capabilities**: CSV, Excel, PDF report generation

## Railway API Integration Requirements

### **External Railway API Connectivity**
Based on the objective to implement external API connectivity for train data synchronization:

#### **Railway API Integration Framework**

**CRITICAL REQUIREMENT**: The IPIS system MUST integrate with external Railway APIs for real-time train data synchronization. This integration is essential for providing accurate, up-to-date passenger information.

##### **API Service Interface**
```csharp
// Primary Railway API Service Interface
public interface IRailwayApiService
{
    // Schedule Operations
    Task<List<TrainScheduleDto>> GetTrainSchedulesAsync(string stationCode, DateTime date);
    Task<TrainStatusDto> GetTrainStatusAsync(string trainNumber);
    Task<List<DelayInfoDto>> GetDelayUpdatesAsync();
    Task<List<PlatformAssignmentDto>> GetPlatformAssignmentsAsync(string stationCode);

    // Real-time Updates
    Task<bool> SyncScheduleDataAsync();
    Task<bool> SyncTrainStatusAsync();
    Task<bool> SyncDelayInformationAsync();

    // Health and Monitoring
    Task<ApiHealthDto> GetApiHealthAsync();
    Task<bool> TestConnectionAsync();
}

// Background Synchronization Service
public interface IScheduleSyncService
{
    Task StartSynchronizationAsync();
    Task StopSynchronizationAsync();
    Task<SyncStatusDto> GetSyncStatusAsync();
    Task ForceFullSyncAsync();
}

// Offline Mode Support Service
public interface IOfflineModeService
{
    Task<bool> IsOfflineModeActiveAsync();
    Task<List<Schedule>> GetCachedSchedulesAsync(string stationCode);
    Task CacheScheduleDataAsync(List<Schedule> schedules);
    Task<DateTime?> GetLastSyncTimeAsync();
}

// Data Conflict Resolution Service
public interface IDataConflictResolver
{
    Task<ConflictResolutionResult> ResolveScheduleConflictAsync(Schedule localSchedule, TrainScheduleDto apiSchedule);
    Task<List<DataConflict>> DetectConflictsAsync();
    Task ApplyConflictResolutionAsync(List<ConflictResolution> resolutions);
}
```

##### **API Configuration Classes**
```csharp
// Main Railway API Configuration
public class RailwayApiConfiguration
{
    public string BaseUrl { get; set; } = "https://api.railway.gov.in";
    public string ApiKey { get; set; } = "";
    public string ApiVersion { get; set; } = "v1";
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
    public int SyncIntervalMinutes { get; set; } = 5;
    public bool EnableRealTimeSync { get; set; } = true;
    public bool EnableOfflineMode { get; set; } = true;
    public int CacheExpirationHours { get; set; } = 24;
    public int MaxConcurrentRequests { get; set; } = 10;
    public bool EnableCircuitBreaker { get; set; } = true;
    public int CircuitBreakerFailureThreshold { get; set; } = 5;
    public int CircuitBreakerTimeoutMinutes { get; set; } = 5;
}

// Synchronization Configuration
public class SyncConfiguration
{
    public int FullSyncIntervalHours { get; set; } = 6;
    public int IncrementalSyncIntervalMinutes { get; set; } = 5;
    public int MaxSyncRetries { get; set; } = 3;
    public bool EnableConflictResolution { get; set; } = true;
    public ConflictResolutionStrategy DefaultStrategy { get; set; } = ConflictResolutionStrategy.ApiPriority;
    public int SyncBatchSize { get; set; } = 100;
    public bool EnableSyncLogging { get; set; } = true;
}
```

##### **Data Transfer Objects (DTOs)**
```csharp
// Train Schedule DTO from Railway API
public class TrainScheduleDto
{
    public string TrainNumber { get; set; } = string.Empty;
    public string TrainName { get; set; } = string.Empty;
    public string StationCode { get; set; } = string.Empty;
    public DateTime? ScheduledArrival { get; set; }
    public DateTime? ScheduledDeparture { get; set; }
    public DateTime? ExpectedArrival { get; set; }
    public DateTime? ExpectedDeparture { get; set; }
    public string PlatformNumber { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int DelayMinutes { get; set; }
    public string DelayReason { get; set; } = string.Empty;
    public bool IsCancelled { get; set; }
    public string Source { get; set; } = string.Empty;
    public string Destination { get; set; } = string.Empty;
    public DateTime LastUpdated { get; set; }
    public string ApiVersion { get; set; } = string.Empty;
}

// Train Status DTO
public class TrainStatusDto
{
    public string TrainNumber { get; set; } = string.Empty;
    public string CurrentStatus { get; set; } = string.Empty;
    public string CurrentLocation { get; set; } = string.Empty;
    public DateTime LastLocationUpdate { get; set; }
    public int OverallDelayMinutes { get; set; }
    public List<StationStatusDto> StationStatuses { get; set; } = new();
    public bool IsRunning { get; set; }
    public DateTime DataTimestamp { get; set; }
}

// Delay Information DTO
public class DelayInfoDto
{
    public string TrainNumber { get; set; } = string.Empty;
    public string StationCode { get; set; } = string.Empty;
    public int DelayMinutes { get; set; }
    public string DelayReason { get; set; } = string.Empty;
    public DateTime DelayReportedAt { get; set; }
    public string DelayCategory { get; set; } = string.Empty;
    public bool IsRecovered { get; set; }
    public DateTime? RecoveryTime { get; set; }
}

// Platform Assignment DTO
public class PlatformAssignmentDto
{
    public string TrainNumber { get; set; } = string.Empty;
    public string StationCode { get; set; } = string.Empty;
    public string PlatformNumber { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; }
    public bool IsConfirmed { get; set; }
    public string AssignmentReason { get; set; } = string.Empty;
}

// API Health DTO
public class ApiHealthDto
{
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CheckTime { get; set; }
    public int ResponseTimeMs { get; set; }
    public string Version { get; set; } = string.Empty;
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

// Sync Status DTO
public class SyncStatusDto
{
    public bool IsActive { get; set; }
    public DateTime? LastSyncTime { get; set; }
    public DateTime? NextSyncTime { get; set; }
    public int RecordsProcessed { get; set; }
    public int RecordsUpdated { get; set; }
    public int RecordsAdded { get; set; }
    public int ConflictsDetected { get; set; }
    public int ConflictsResolved { get; set; }
    public List<string> Errors { get; set; } = new();
    public TimeSpan LastSyncDuration { get; set; }
}

// Data Conflict DTOs
public class DataConflict
{
    public string EntityType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public string ConflictType { get; set; } = string.Empty;
    public object LocalValue { get; set; } = new();
    public object ApiValue { get; set; } = new();
    public DateTime DetectedAt { get; set; }
    public ConflictSeverity Severity { get; set; }
}

public class ConflictResolutionResult
{
    public bool IsResolved { get; set; }
    public ConflictResolutionStrategy StrategyUsed { get; set; }
    public object ResolvedValue { get; set; } = new();
    public string ResolutionReason { get; set; } = string.Empty;
    public DateTime ResolvedAt { get; set; }
}
```

#### **Real-time Data Synchronization**
- **Automatic Schedule Updates**: Sync train schedules every 5 minutes
- **Delay Information**: Real-time delay and cancellation updates
- **Platform Changes**: Automatic platform reassignment notifications
- **Train Status Updates**: Live tracking of train positions and status
- **Conflict Resolution**: Handle discrepancies between local and API data
- **Background Processing**: Non-blocking synchronization operations
- **Batch Processing**: Efficient bulk data updates
- **Error Recovery**: Automatic retry with exponential backoff

#### **Offline Mode Support**
- **Local Data Cache**: Maintain local copy when API is unavailable
- **Graceful Degradation**: Continue operations with cached data
- **Sync Recovery**: Automatic resynchronization when API becomes available
- **Data Validation**: Verify API data integrity before applying updates
- **Cache Management**: Intelligent cache expiration and refresh
- **Offline Indicators**: Clear UI indication of offline status
- **Data Staleness**: Track and display data age information

#### **API Data Models**
```csharp
public class TrainScheduleDto
{
    public string TrainNumber { get; set; }
    public string StationCode { get; set; }
    public DateTime ScheduledArrival { get; set; }
    public DateTime ScheduledDeparture { get; set; }
    public string PlatformNumber { get; set; }
    public string Status { get; set; }
    public int DelayMinutes { get; set; }
}

public class TrainStatusDto
{
    public string TrainNumber { get; set; }
    public string CurrentLocation { get; set; }
    public DateTime LastUpdated { get; set; }
    public string Status { get; set; }
    public List<StationUpdateDto> StationUpdates { get; set; }
}
```

## Communication Protocols and Network Management

### **Network Communication Systems**
Based on legacy system analysis - support for all existing communication protocols:

#### **Serial Communication (RS232)**
```csharp
public interface ISerialCommunicationService
{
    Task<bool> ConnectAsync(string portName, int baudRate);
    Task<string> SendCommandAsync(string command);
    Task<bool> TestConnectionAsync();
    void Disconnect();
}

// Serial Port Configuration
public class SerialPortConfiguration
{
    public string PortName { get; set; } = "COM1";
    public int BaudRate { get; set; } = 9600;
    public int DataBits { get; set; } = 8;
    public Parity Parity { get; set; } = Parity.None;
    public StopBits StopBits { get; set; } = StopBits.One;
}
```

#### **TCP/IP Network Communication**
```csharp
public interface INetworkCommunicationService
{
    Task<bool> ConnectToDeviceAsync(string ipAddress, int port);
    Task<bool> SendMessageAsync(string deviceId, byte[] message);
    Task<DeviceStatus> GetDeviceStatusAsync(string deviceId);
    Task<bool> PerformLinkCheckAsync(string deviceId);
}

// Network Device Management
public class NetworkDevice
{
    public string DeviceId { get; set; }
    public string IpAddress { get; set; }
    public int Port { get; set; }
    public DisplayBoardType BoardType { get; set; }
    public bool IsOnline { get; set; }
    public DateTime LastHeartbeat { get; set; }
}
```

#### **Display Board Communication Protocols**
Based on legacy system analysis - support for all 5 board types:

```csharp
// AGDB (Arrival/General Display Board) Protocol
public class AGDBProtocol : IDisplayBoardProtocol
{
    public byte[] CreateMessage(string content, int priority);
    public byte[] CreateScheduleDisplay(List<Schedule> schedules);
    public byte[] CreateAnnouncementMessage(string message);
}

// CGDB (Coach Guidance Display Board) Protocol
public class CGDBProtocol : IDisplayBoardProtocol
{
    public byte[] CreateCoachGuidanceMessage(string trainNumber, string coachInfo);
    public byte[] CreatePlatformMessage(string platformNumber);
}

// MLDB (Multi-Line Display Board) Protocol
public class MLDBProtocol : IDisplayBoardProtocol
{
    public byte[] CreateMultiLineMessage(List<string> lines);
    public byte[] CreateScrollingMessage(string content, int speed);
}

// PDB (Platform Display Board) Protocol
public class PDBProtocol : IDisplayBoardProtocol
{
    public byte[] CreatePlatformSpecificMessage(string platformNumber, string content);
    public byte[] CreateTrainArrivalMessage(Schedule schedule);
}

// PDCH (Platform Display Controller Hub) Protocol
public class PDCHProtocol : IDisplayBoardProtocol
{
    public byte[] CreateHubControlMessage(List<string> deviceIds, byte[] message);
    public byte[] CreateDeviceStatusRequest();
}
```

## Voice Announcement System

### **Multi-Language Voice Engine**
Based on legacy system analysis - support for English, Hindi, and Regional languages:

#### **Voice File Management**
```csharp
public interface IVoiceService
{
    Task<bool> GenerateAnnouncementAsync(AnnouncementRequest request);
    Task<List<VoiceFile>> GetVoiceFilesAsync(Language language);
    Task<bool> PlayAnnouncementAsync(string announcementId);
    Task<bool> ValidateVoiceFilesAsync();
}

public class AnnouncementRequest
{
    public string TrainNumber { get; set; }
    public string StationName { get; set; }
    public string PlatformNumber { get; set; }
    public AnnouncementType Type { get; set; }
    public List<Language> Languages { get; set; }
    public int Priority { get; set; }
}
```

#### **Voice File Structure**
Based on legacy system analysis - 1000+ voice segments:
```
Resources/Voice/
├── English/
│   ├── StationNames/          # 500+ station recordings
│   ├── TrainNumbers/          # Train number recordings
│   ├── CommonPhrases/         # "arriving", "departing", etc.
│   ├── PlatformNumbers/       # Platform number recordings
│   └── SpecialMessages/       # Emergency announcements
├── Hindi/
│   ├── StationNames/
│   ├── TrainNumbers/
│   ├── CommonPhrases/
│   └── SpecialMessages/
└── Regional/
    ├── StationNames/
    ├── TrainNumbers/
    ├── CommonPhrases/
    └── SpecialMessages/
```

#### **Dynamic Announcement Composition**
```csharp
public class AnnouncementComposer
{
    public async Task<string> ComposeTrainArrivalAsync(Schedule schedule, Language language)
    {
        // Compose: "Train number [12345] [Rajdhani Express] from [Delhi]
        // to [Mumbai] is arriving at platform number [3]"
    }

    public async Task<string> ComposeTrainDepartureAsync(Schedule schedule, Language language)
    {
        // Compose departure announcement with delay information
    }

    public async Task<string> ComposePlatformChangeAsync(Schedule schedule, string newPlatform, Language language)
    {
        // Compose platform change announcement
    }
}
```

## Advertisement Management System

### **Commercial Content Management**
Based on legacy system analysis - integrated advertisement system:

#### **Advertisement Service**
```csharp
public interface IAdvertisementService
{
    Task<bool> UploadContentAsync(AdvertisementContent content);
    Task<List<Advertisement>> GetScheduledAdsAsync(DateTime date);
    Task<bool> ScheduleAdvertisementAsync(AdvertisementSchedule schedule);
    Task<AdvertisementMetrics> GetPerformanceMetricsAsync(int advertisementId);
}

public class AdvertisementContent
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string ContentPath { get; set; }
    public AdvertisementType Type { get; set; } // Audio, Video, Text
    public TimeSpan Duration { get; set; }
    public List<string> TargetStations { get; set; }
    public DateTime ValidFrom { get; set; }
    public DateTime ValidTo { get; set; }
}
```

#### **Advertisement Scheduling**
```csharp
public class AdvertisementScheduler
{
    public async Task<bool> ScheduleRotationAsync(List<Advertisement> ads, TimeSpan interval);
    public async Task<Advertisement> GetNextAdvertisementAsync(string stationCode);
    public async Task<bool> HandleEmergencyOverrideAsync(string emergencyMessage);
}
```

#### **Revenue Tracking**
```csharp
public class AdvertisementMetrics
{
    public int PlayCount { get; set; }
    public TimeSpan TotalPlayTime { get; set; }
    public List<string> StationsPlayed { get; set; }
    public DateTime LastPlayed { get; set; }
    public decimal Revenue { get; set; }
}
```

## Hardware Integration Requirements

### **Display Board Hardware Management**
Based on legacy system analysis - support for existing hardware:

#### **Device Discovery and Management**
```csharp
public interface IHardwareService
{
    Task<List<NetworkDevice>> DiscoverDevicesAsync();
    Task<bool> ConfigureDeviceAsync(string deviceId, DeviceConfiguration config);
    Task<DeviceHealth> GetDeviceHealthAsync(string deviceId);
    Task<bool> UpdateFirmwareAsync(string deviceId, byte[] firmwareData);
}

public class DeviceHealth
{
    public string DeviceId { get; set; }
    public bool IsOnline { get; set; }
    public int SignalStrength { get; set; }
    public double Temperature { get; set; }
    public int Brightness { get; set; }
    public DateTime LastMaintenance { get; set; }
    public List<string> Errors { get; set; }
}
```

#### **Audio System Integration**
```csharp
public interface IAudioSystemService
{
    Task<bool> ConnectToAmplifierAsync(string deviceId);
    Task<bool> SetVolumeAsync(int volumeLevel);
    Task<bool> PlayAudioFileAsync(string filePath);
    Task<bool> TestSpeakersAsync(List<string> speakerIds);
}
```

### **Legacy Database Migration Tools**
Based on analysis of 15 Access databases:

#### **Data Migration Service**
```csharp
public interface IDataMigrationService
{
    Task<bool> MigrateStationDataAsync(string accessDbPath);
    Task<bool> MigrateTrainDataAsync(string accessDbPath);
    Task<bool> MigrateScheduleDataAsync(string accessDbPath);
    Task<bool> MigrateUserDataAsync(string accessDbPath);
    Task<bool> ValidateMigrationAsync();
    Task<MigrationReport> GenerateReportAsync();
}

public class MigrationReport
{
    public int TotalRecords { get; set; }
    public int MigratedRecords { get; set; }
    public int FailedRecords { get; set; }
    public List<string> Errors { get; set; }
    public TimeSpan Duration { get; set; }
}
```

### Deployment Considerations
- **Single File Deployment**: Self-contained executable
- **Database Deployment**: Automatic database creation and migration
- **Configuration Management**: Environment-specific settings
- **Update Mechanism**: In-place application updates

### Quality Assurance
- **Code Analysis**: Enable all compiler warnings and treat as errors
- **Static Analysis**: Use code analysis tools (SonarQube, CodeQL)
- **Performance Profiling**: Regular performance testing
- **Memory Leak Detection**: Monitor memory usage patterns

### Accessibility Features
- **Keyboard Navigation**: Full keyboard support for all functions
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **High Contrast Mode**: Support for high contrast themes
- **Font Scaling**: Respect system font size settings

### Internationalization (i18n)
- **Resource Files**: Separate resource files for each language
- **Culture Support**: Proper date, time, and number formatting
- **RTL Support**: Right-to-left language support if needed
- **Dynamic Language Switching**: Change language without restart

### Backup and Recovery
- **Automatic Backups**: Scheduled database backups
- **Backup Verification**: Verify backup integrity
- **Point-in-Time Recovery**: Restore to specific timestamps
- **Disaster Recovery**: Complete system recovery procedures

### Monitoring and Diagnostics
- **Performance Counters**: Track application performance metrics
- **Health Checks**: System health monitoring
- **Diagnostic Tools**: Built-in diagnostic and troubleshooting tools
- **Usage Analytics**: Track feature usage and performance

### Security Implementation
```csharp
// Password Hashing
public class PasswordHasher
{
    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, 12);
    }

    public bool VerifyPassword(string password, string hash)
    {
        return BCrypt.Net.BCrypt.Verify(password, hash);
    }
}

// Session Management
public class SessionManager
{
    public void CreateSession(User user)
    public void ValidateSession(string sessionId)
    public void ExpireSession(string sessionId)
}
```

### Development Best Practices
1. **SOLID Principles**: Follow SOLID design principles
2. **Clean Code**: Meaningful names, small functions, clear comments
3. **Design Patterns**: Repository, Unit of Work, Factory, Observer
4. **Code Reviews**: Mandatory code reviews for all changes
5. **Version Control**: Git with feature branch workflow

### Testing Strategy
```csharp
// Unit Test Example
[Test]
public async Task CreateStation_ValidData_ReturnsStation()
{
    // Arrange
    var station = new Station { Code = "TEST", Name = "Test Station" };

    // Act
    var result = await _stationService.CreateStationAsync(station);

    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual("TEST", result.Code);
}

// Integration Test Example
[Test]
public async Task StationService_DatabaseIntegration_WorksCorrectly()
{
    // Test with real database context
}
```

## Real-time Operations and Background Services

### **Real-time Update Framework**
Based on the requirement for real-time train operations:

#### **Background Service Architecture**
```csharp
// Real-time Schedule Update Service
public class ScheduleUpdateService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await SyncWithRailwayApiAsync();
            await UpdateDisplayBoardsAsync();
            await TriggerVoiceAnnouncementsAsync();
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }
}

// Device Health Monitoring Service
public class DeviceMonitoringService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await PerformLinkChecksAsync();
            await UpdateDeviceStatusAsync();
            await GenerateHealthReportsAsync();
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
        }
    }
}

// Advertisement Rotation Service
public class AdvertisementService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await RotateAdvertisementsAsync();
            await UpdateRevenueMetricsAsync();
            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}
```

#### **Event-Driven Architecture**
```csharp
// Event System for Real-time Updates
public interface IEventBus
{
    Task PublishAsync<T>(T eventData) where T : class;
    void Subscribe<T>(Func<T, Task> handler) where T : class;
}

// Event Types
public class TrainArrivalEvent
{
    public string TrainNumber { get; set; }
    public string StationCode { get; set; }
    public DateTime ActualArrival { get; set; }
    public string PlatformNumber { get; set; }
}

public class PlatformChangeEvent
{
    public string TrainNumber { get; set; }
    public string OldPlatform { get; set; }
    public string NewPlatform { get; set; }
    public DateTime ChangeTime { get; set; }
}

public class EmergencyMessageEvent
{
    public string Message { get; set; }
    public int Priority { get; set; }
    public List<string> TargetStations { get; set; }
}
```

## Production Deployment and Migration Strategy

### **Zero-Downtime Migration Plan**
Based on the requirement for immediate production deployment:

#### **Parallel System Operation**
```csharp
public interface IMigrationService
{
    Task<bool> StartParallelOperationAsync();
    Task<bool> ValidateDataConsistencyAsync();
    Task<bool> SwitchToNewSystemAsync();
    Task<bool> RollbackToLegacyAsync();
}

// Migration Phases
public enum MigrationPhase
{
    Preparation,
    DataMigration,
    ParallelTesting,
    UserAcceptance,
    ProductionSwitch,
    LegacyDecommission
}
```

#### **Data Validation and Integrity**
```csharp
public class DataValidationService
{
    public async Task<ValidationReport> ValidateStationDataAsync();
    public async Task<ValidationReport> ValidateTrainDataAsync();
    public async Task<ValidationReport> ValidateScheduleDataAsync();
    public async Task<bool> CompareWithLegacySystemAsync();
}

public class ValidationReport
{
    public int TotalRecords { get; set; }
    public int ValidRecords { get; set; }
    public int InvalidRecords { get; set; }
    public List<ValidationError> Errors { get; set; }
    public bool IsValid => InvalidRecords == 0;
}
```

### **Rollback and Recovery Procedures**
```csharp
public interface IRecoveryService
{
    Task<bool> CreateSystemSnapshotAsync();
    Task<bool> RestoreFromSnapshotAsync(string snapshotId);
    Task<bool> ValidateSystemIntegrityAsync();
    Task<RecoveryReport> GenerateRecoveryReportAsync();
}
```

## Comprehensive Testing Framework

### **Production-Ready Testing Strategy**
Based on the requirement for immediate production deployment:

#### **Automated Testing Suite**
```csharp
// Integration Tests for Railway API
[TestFixture]
public class RailwayApiIntegrationTests
{
    [Test]
    public async Task SyncScheduleData_ValidApi_ReturnsUpdatedSchedules()
    {
        // Test real API integration
    }

    [Test]
    public async Task HandleApiFailure_OfflineMode_ContinuesOperation()
    {
        // Test offline mode functionality
    }
}

// Communication Protocol Tests
[TestFixture]
public class CommunicationProtocolTests
{
    [Test]
    public async Task AGDB_SendMessage_ReceivesAcknowledgment()
    {
        // Test AGDB communication
    }

    [Test]
    public async Task SerialCommunication_RS232_TransmitsCorrectly()
    {
        // Test RS232 communication
    }
}

// Voice System Tests
[TestFixture]
public class VoiceSystemTests
{
    [Test]
    public async Task GenerateAnnouncement_MultiLanguage_CreatesCorrectFiles()
    {
        // Test multi-language voice generation
    }
}
```

#### **Load and Performance Testing**
```csharp
[TestFixture]
public class PerformanceTests
{
    [Test]
    public async Task HandleConcurrentUsers_100Users_MaintainsPerformance()
    {
        // Test concurrent user load
    }

    [Test]
    public async Task ProcessScheduleUpdates_1000Updates_CompletesWithinTimeout()
    {
        // Test bulk schedule processing
    }
}
```

### **User Acceptance Testing Framework**
```csharp
public class UserAcceptanceTestSuite
{
    public async Task<bool> TestTrainOperatorWorkflowAsync();
    public async Task<bool> TestStationMasterWorkflowAsync();
    public async Task<bool> TestMaintenanceWorkflowAsync();
    public async Task<bool> TestEmergencyProceduresAsync();
}
```

## Security and Compliance Framework

### **Production Security Requirements**
Based on railway operational security needs:

#### **Advanced Security Features**
```csharp
public interface ISecurityService
{
    Task<bool> ValidateUserPermissionsAsync(string userId, string action);
    Task<bool> EncryptSensitiveDataAsync(object data);
    Task<bool> AuditUserActionAsync(string userId, string action, object data);
    Task<bool> DetectSecurityThreatsAsync();
}

// Role-Based Access Control
public class RBACService
{
    public async Task<bool> HasPermissionAsync(string userId, Permission permission);
    public async Task<List<Permission>> GetUserPermissionsAsync(string userId);
    public async Task<bool> AssignRoleAsync(string userId, UserRole role);
}
```

#### **Compliance and Audit Framework**
```csharp
public class ComplianceService
{
    public async Task<ComplianceReport> GenerateAuditReportAsync(DateTime from, DateTime to);
    public async Task<bool> ValidateDataRetentionPolicyAsync();
    public async Task<bool> ExportAuditDataAsync(string format);
}
```

## Performance Optimization and Monitoring

### **Production Performance Requirements**
- **Real-time Response**: Schedule updates < 1 second
- **Voice Generation**: Announcement creation < 2 seconds
- **Display Updates**: Board content refresh < 3 seconds
- **API Synchronization**: External data sync < 5 seconds
- **System Availability**: 99.9% uptime requirement

#### **Performance Monitoring**
```csharp
public interface IPerformanceMonitorService
{
    Task<PerformanceMetrics> GetSystemMetricsAsync();
    Task<bool> AlertOnPerformanceIssuesAsync();
    Task<bool> OptimizeDatabaseQueriesAsync();
    Task<bool> MonitorMemoryUsageAsync();
}

public class PerformanceMetrics
{
    public double CpuUsage { get; set; }
    public long MemoryUsage { get; set; }
    public int ActiveConnections { get; set; }
    public double AverageResponseTime { get; set; }
    public int ErrorCount { get; set; }
}
```

## Final Implementation Checklist

### **Production Readiness Criteria**
- [ ] **100% Feature Parity**: All legacy features implemented and tested
- [ ] **Railway API Integration**: Real-time data synchronization working
- [ ] **Communication Protocols**: All 5 display board types operational
- [ ] **Voice System**: Multi-language announcements functional
- [ ] **Advertisement System**: Commercial content management working
- [ ] **Hardware Integration**: Display boards and audio systems connected
- [ ] **Security Framework**: Authentication, authorization, and audit logging
- [ ] **Performance Validation**: All performance benchmarks met
- [ ] **User Acceptance**: Railway operators approve the system
- [ ] **Migration Tools**: Legacy data successfully migrated
- [ ] **Backup and Recovery**: Data protection mechanisms validated
- [ ] **Documentation**: Complete user and technical documentation
- [ ] **Training Materials**: User training completed
- [ ] **Support Procedures**: 24/7 support framework established

### **Go-Live Requirements**
1. **Technical Validation**: All systems tested and validated
2. **User Training**: All operators trained on new system
3. **Data Migration**: Complete and verified data transfer
4. **Parallel Testing**: Successful side-by-side operation
5. **Performance Validation**: System meets all performance criteria
6. **Security Clearance**: Security audit passed
7. **Backup Procedures**: Recovery mechanisms tested
8. **Support Team**: Technical support team ready
9. **Rollback Plan**: Emergency rollback procedures prepared
10. **Stakeholder Approval**: Final approval from railway management

This enhanced prompt provides a comprehensive foundation for developing a production-ready, modern IPIS Windows Forms application that not only replicates all existing functionality but also provides enhanced capabilities for modern railway operations while ensuring zero feature loss from the legacy VB.NET system and meeting all requirements for immediate production deployment.
