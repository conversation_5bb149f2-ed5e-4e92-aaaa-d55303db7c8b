# PowerShell script to fix all remaining ref parameter issues comprehensively

Write-Host "=== FIXING ALL REMAINING REF PARAMETER ISSUES ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix agdb_byte_construct method calls with local variables
    $content = $content -replace 'agdb_byte_construct\.train_number_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.train_number_byte($1, ref $2, ref $3)'
    $content = $content -replace 'agdb_byte_construct\.name_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.name_byte($1, ref $2, ref $3)'
    $content = $content -replace 'agdb_byte_construct\.platform_number_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.platform_number_byte($1, ref $2, ref $3)'
    $content = $content -replace 'agdb_byte_construct\.time_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.time_byte($1, ref $2, ref $3)'
    $content = $content -replace 'agdb_byte_construct\.status_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'agdb_byte_construct.status_byte($1, ref $2, ref $3)'
    
    # Fix double ref issues
    $content = $content -replace 'ref\s+ref\s+', 'ref '
    
    # Fix invalid standalone ref expressions that cause syntax errors
    $content = $content -replace '\n\s*ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;\s*\n', "`n    $1;`n"
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*,', ' $1,'
    $content = $content -replace '\s+ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\)', ' $1)'
    
    # Fix array access with ref
    $content = $content -replace 'ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\[([^\]]+)\]', '$1[$2]'
    
    # Fix specific method calls that should not have ref on certain parameters
    $content = $content -replace '\.Write\(ref\s+([^,)]+)\)', '.Write($1)'
    $content = $content -replace '\.WriteLine\(ref\s+([^,)]+)\)', '.WriteLine($1)'
    $content = $content -replace '\.ToString\(ref\s+([^,)]+)\)', '.ToString($1)'
    
    # Fix parentheses issues
    $content = $content -replace '\(\s*ref\s+([^,)]+)\s*,\s*ref\s+([^,)]+)\s*,\s*ref\s+([^,)]+)\s*,\s*ref\s+([^,)]+)\s*,\s*ref\s+([^)]+)\s*\)', '(ref $1, ref $2, ref $3, ref $4, ref $5)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed ref parameter issues"
    }
}

Write-Host "All final ref parameter fixes complete!"
