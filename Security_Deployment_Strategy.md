# IPIS Security and Deployment Strategy

## Security Framework

### 1. Authentication and Authorization

#### 1.1 JWT Token-Based Authentication
```csharp
// IPIS.Infrastructure/Security/JwtTokenGenerator.cs
public class JwtTokenGenerator : IJwtTokenGenerator
{
    private readonly JwtConfiguration _config;

    public string GenerateToken(User user)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.Username),
            new(ClaimTypes.Email, user.Email),
            new("station_access", GetStationAccess(user)),
        };

        // Add role claims
        foreach (var role in user.UserRoles.Select(ur => ur.Role))
        {
            claims.Add(new(ClaimTypes.Role, role.Name));
            
            // Add permission claims
            if (role.Permissions != null)
            {
                foreach (var permission in role.Permissions)
                {
                    claims.Add(new("permission", permission.ToString()));
                }
            }
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_config.SecretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _config.Issuer,
            audience: _config.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddHours(_config.ExpirationHours),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}
```

#### 1.2 Role-Based Access Control
```csharp
// IPIS.Core/Security/Permissions.cs
public static class Permissions
{
    public const string ViewSchedules = "schedules:view";
    public const string ManageSchedules = "schedules:manage";
    public const string ViewDisplayBoards = "displays:view";
    public const string ManageDisplayBoards = "displays:manage";
    public const string MakeAnnouncements = "announcements:make";
    public const string ManageUsers = "users:manage";
    public const string ViewReports = "reports:view";
    public const string SystemAdmin = "system:admin";
}

// IPIS.API/Authorization/PermissionRequirement.cs
public class PermissionRequirement : IAuthorizationRequirement
{
    public string Permission { get; }
    
    public PermissionRequirement(string permission)
    {
        Permission = permission;
    }
}

public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
{
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        if (context.User.HasClaim("permission", requirement.Permission))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}
```

#### 1.3 API Security Configuration
```csharp
// IPIS.API/Program.cs
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:SecretKey"]))
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("ManageSchedules", policy =>
        policy.RequireAuthenticatedUser()
              .Requirements.Add(new PermissionRequirement(Permissions.ManageSchedules)));
              
    options.AddPolicy("MakeAnnouncements", policy =>
        policy.RequireAuthenticatedUser()
              .Requirements.Add(new PermissionRequirement(Permissions.MakeAnnouncements)));
});
```

### 2. Data Protection

#### 2.1 Encryption at Rest
```csharp
// IPIS.Infrastructure/Security/DataProtectionService.cs
public class DataProtectionService : IDataProtectionService
{
    private readonly IDataProtector _protector;

    public DataProtectionService(IDataProtectionProvider provider)
    {
        _protector = provider.CreateProtector("IPIS.SensitiveData");
    }

    public string Encrypt(string plainText)
    {
        return _protector.Protect(plainText);
    }

    public string Decrypt(string cipherText)
    {
        return _protector.Unprotect(cipherText);
    }
}

// Database configuration with encryption
builder.Services.AddDbContext<IPISDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    options.UseNpgsql(connectionString, npgsqlOptions =>
    {
        npgsqlOptions.EnableSensitiveDataLogging(false);
    });
});
```

#### 2.2 Secrets Management
```csharp
// IPIS.Infrastructure/Configuration/SecretsManager.cs
public class SecretsManager : ISecretsManager
{
    private readonly IConfiguration _configuration;

    public async Task<string> GetSecretAsync(string secretName)
    {
        // Try Railway environment variables first
        var secret = Environment.GetEnvironmentVariable(secretName);
        if (!string.IsNullOrEmpty(secret))
            return secret;

        // Fallback to configuration
        return _configuration[secretName];
    }
}

// Railway secrets configuration
railway env set JWT_SECRET_KEY="your-super-secure-secret-key"
railway env set DATABASE_ENCRYPTION_KEY="your-database-encryption-key"
railway env set EXTERNAL_API_KEY="your-external-api-key"
```

### 3. Network Security

#### 3.1 HTTPS Configuration
```csharp
// IPIS.API/Program.cs
if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
    app.UseHttpsRedirection();
}

// Security headers
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    context.Response.Headers.Add("Content-Security-Policy", 
        "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
    
    await next();
});
```

#### 3.2 Rate Limiting
```csharp
// IPIS.API/Middleware/RateLimitingMiddleware.cs
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IMemoryCache _cache;

    public async Task InvokeAsync(HttpContext context)
    {
        var clientId = GetClientIdentifier(context);
        var key = $"rate_limit_{clientId}";
        
        if (_cache.TryGetValue(key, out int requestCount))
        {
            if (requestCount >= 100) // 100 requests per minute
            {
                context.Response.StatusCode = 429;
                await context.Response.WriteAsync("Rate limit exceeded");
                return;
            }
            _cache.Set(key, requestCount + 1, TimeSpan.FromMinutes(1));
        }
        else
        {
            _cache.Set(key, 1, TimeSpan.FromMinutes(1));
        }

        await _next(context);
    }
}
```

### 4. Audit Logging

#### 4.1 Security Event Logging
```csharp
// IPIS.Infrastructure/Logging/SecurityLogger.cs
public class SecurityLogger : ISecurityLogger
{
    private readonly ILogger<SecurityLogger> _logger;

    public void LogLoginAttempt(string username, bool success, string ipAddress)
    {
        _logger.LogInformation("Login attempt: {Username}, Success: {Success}, IP: {IpAddress}",
            username, success, ipAddress);
    }

    public void LogPermissionDenied(string username, string resource, string action)
    {
        _logger.LogWarning("Permission denied: {Username} attempted {Action} on {Resource}",
            username, action, resource);
    }

    public void LogDataAccess(string username, string dataType, string operation)
    {
        _logger.LogInformation("Data access: {Username} performed {Operation} on {DataType}",
            username, operation, dataType);
    }
}
```

## Deployment Strategy

### 1. Railway Platform Configuration

#### 1.1 Project Structure
```
Railway Project: IPIS-Production
├── Services:
│   ├── ipis-api          # Main API service
│   ├── ipis-web          # Web application
│   ├── ipis-worker       # Background worker
│   └── ipis-gateway      # API Gateway (optional)
├── Databases:
│   ├── postgresql        # Main database
│   └── redis            # Cache and sessions
└── Environments:
    ├── development
    ├── staging
    └── production
```

#### 1.2 Environment Configuration
```bash
# Development Environment
railway env set ASPNETCORE_ENVIRONMENT=Development
railway env set LOG_LEVEL=Debug
railway env set ENABLE_SWAGGER=true

# Staging Environment
railway env set ASPNETCORE_ENVIRONMENT=Staging
railway env set LOG_LEVEL=Information
railway env set ENABLE_SWAGGER=true

# Production Environment
railway env set ASPNETCORE_ENVIRONMENT=Production
railway env set LOG_LEVEL=Warning
railway env set ENABLE_SWAGGER=false
railway env set FORCE_HTTPS=true
```

### 2. CI/CD Pipeline

#### 2.1 GitHub Actions Workflow
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]

env:
  DOTNET_VERSION: '6.0.x'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}
          
      - name: Restore dependencies
        run: dotnet restore
        
      - name: Build
        run: dotnet build --no-restore --configuration Release
        
      - name: Run unit tests
        run: dotnet test --no-build --configuration Release --logger trx --collect:"XPlat Code Coverage"
        
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: TestResults/
          
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run security scan
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: security-scan-results.sarif

  deploy-staging:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Railway Staging
        uses: railway/cli@v2
        with:
          command: up --service ipis-api --environment staging
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_STAGING_TOKEN }}

  deploy-production:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Railway Production
        uses: railway/cli@v2
        with:
          command: up --service ipis-api --environment production
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_PRODUCTION_TOKEN }}
          
      - name: Run smoke tests
        run: |
          sleep 30  # Wait for deployment
          curl -f https://ipis-api.railway.app/health || exit 1
```

#### 2.2 Database Migration Strategy
```csharp
// IPIS.Infrastructure/Migrations/MigrationService.cs
public class MigrationService : IMigrationService
{
    private readonly IPISDbContext _context;
    private readonly ILogger<MigrationService> _logger;

    public async Task MigrateAsync()
    {
        try
        {
            _logger.LogInformation("Starting database migration...");
            
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("Applying {Count} pending migrations", 
                    pendingMigrations.Count());
                await _context.Database.MigrateAsync();
                _logger.LogInformation("Database migration completed successfully");
            }
            else
            {
                _logger.LogInformation("No pending migrations found");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database migration failed");
            throw;
        }
    }
}

// Auto-migration on startup
public static async Task Main(string[] args)
{
    var host = CreateHostBuilder(args).Build();
    
    using (var scope = host.Services.CreateScope())
    {
        var migrationService = scope.ServiceProvider.GetRequiredService<IMigrationService>();
        await migrationService.MigrateAsync();
    }
    
    await host.RunAsync();
}
```

### 3. Monitoring and Alerting

#### 3.1 Health Checks
```csharp
// IPIS.API/HealthChecks/DatabaseHealthCheck.cs
public class DatabaseHealthCheck : IHealthCheck
{
    private readonly IPISDbContext _context;

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync("SELECT 1", cancellationToken);
            return HealthCheckResult.Healthy("Database is accessible");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Database is not accessible", ex);
        }
    }
}

// Health check configuration
builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck>("database")
    .AddCheck<RedisHealthCheck>("redis")
    .AddCheck<RailwayApiHealthCheck>("railway-api");

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

#### 3.2 Application Monitoring
```csharp
// IPIS.API/Program.cs
builder.Services.AddApplicationInsightsTelemetry();

// Custom telemetry
builder.Services.AddSingleton<ITelemetryInitializer>(provider =>
    new CustomTelemetryInitializer
    {
        RoleName = "IPIS-API",
        RoleInstance = Environment.MachineName
    });

// Structured logging
builder.Host.UseSerilog((context, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithProperty("Application", "IPIS")
        .WriteTo.Console()
        .WriteTo.ApplicationInsights(TelemetryConfiguration.CreateDefault(), 
            TelemetryConverter.Traces);
});
```

### 4. Disaster Recovery

#### 4.1 Backup Strategy
```bash
# Automated database backup script
#!/bin/bash
BACKUP_DIR="/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="ipis_backup_${TIMESTAMP}.sql"

# Create backup
pg_dump $DATABASE_URL > "${BACKUP_DIR}/${BACKUP_FILE}"

# Compress backup
gzip "${BACKUP_DIR}/${BACKUP_FILE}"

# Upload to cloud storage
railway storage upload "${BACKUP_DIR}/${BACKUP_FILE}.gz"

# Clean up old backups (keep last 30 days)
find $BACKUP_DIR -name "ipis_backup_*.sql.gz" -mtime +30 -delete
```

#### 4.2 Recovery Procedures
```bash
# Database recovery procedure
#!/bin/bash
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# Download backup from storage
railway storage download $BACKUP_FILE

# Restore database
gunzip -c $BACKUP_FILE | psql $DATABASE_URL

echo "Database recovery completed"
```

This security and deployment strategy ensures robust protection of the IPIS system while enabling efficient, automated deployment processes through Railway's platform capabilities.
