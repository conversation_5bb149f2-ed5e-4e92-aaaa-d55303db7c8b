using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a railway platform entity
    /// Based on legacy system analysis - enhanced with comprehensive features
    /// </summary>
    [Table("Platforms")]
    public class Platform
    {
        /// <summary>
        /// Primary key for the platform
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the station this platform belongs to
        /// </summary>
        [Required]
        [ForeignKey("Station")]
        public int StationId { get; set; }

        /// <summary>
        /// Platform number or identifier (e.g., "1", "2A", "3B")
        /// Based on legacy system analysis - supports alphanumeric platform numbers
        /// </summary>
        [Required(ErrorMessage = "Platform number is required")]
        [StringLength(10, ErrorMessage = "Platform number cannot exceed 10 characters")]
        [Column("PlatformNumber")]
        public string PlatformNumber { get; set; } = string.Empty;

        /// <summary>
        /// Descriptive name for the platform (optional)
        /// </summary>
        [StringLength(50, ErrorMessage = "Platform name cannot exceed 50 characters")]
        [Column("PlatformName")]
        public string? PlatformName { get; set; }

        /// <summary>
        /// Type of platform (Passenger, Freight, Mixed, Maintenance)
        /// </summary>
        [Required]
        public PlatformType PlatformType { get; set; } = PlatformType.Passenger;

        /// <summary>
        /// Track number associated with this platform
        /// </summary>
        [StringLength(10)]
        public string? TrackNumber { get; set; }

        /// <summary>
        /// Platform length in meters
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal? Length { get; set; }

        /// <summary>
        /// Platform width in meters
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal? Width { get; set; }

        /// <summary>
        /// Platform height in meters (above rail level)
        /// </summary>
        [Column(TypeName = "decimal(4,2)")]
        public decimal? Height { get; set; }

        /// <summary>
        /// Indicates if the platform is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates if the platform has roof/cover
        /// </summary>
        public bool HasCover { get; set; } = false;

        /// <summary>
        /// Indicates if the platform is wheelchair accessible
        /// </summary>
        public bool IsAccessible { get; set; } = false;

        /// <summary>
        /// Indicates if the platform has lighting
        /// </summary>
        public bool HasLighting { get; set; } = true;

        /// <summary>
        /// Indicates if the platform has seating arrangements
        /// </summary>
        public bool HasSeating { get; set; } = false;

        /// <summary>
        /// Indicates if the platform has water facilities
        /// </summary>
        public bool HasWaterFacility { get; set; } = false;

        /// <summary>
        /// Indicates if the platform has restroom facilities
        /// </summary>
        public bool HasRestroom { get; set; } = false;

        /// <summary>
        /// Maximum number of coaches that can be accommodated
        /// </summary>
        public int? MaxCoaches { get; set; }

        /// <summary>
        /// Platform capacity (number of passengers)
        /// </summary>
        public int? Capacity { get; set; }

        /// <summary>
        /// Side of the platform (Left, Right, Both)
        /// </summary>
        [StringLength(10)]
        public string? PlatformSide { get; set; }

        /// <summary>
        /// Surface type of the platform (Concrete, Asphalt, etc.)
        /// </summary>
        [StringLength(20)]
        public string? SurfaceType { get; set; }

        /// <summary>
        /// Safety features available on the platform
        /// </summary>
        [StringLength(200)]
        public string? SafetyFeatures { get; set; }

        /// <summary>
        /// Additional remarks or notes about the platform
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// The station this platform belongs to
        /// </summary>
        public virtual Station Station { get; set; } = null!;

        /// <summary>
        /// Collection of schedules for trains at this platform
        /// </summary>
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();

        /// <summary>
        /// Collection of display boards on this platform
        /// </summary>
        public virtual ICollection<DisplayBoard> DisplayBoards { get; set; } = new List<DisplayBoard>();

        /// <summary>
        /// Collection of messages specific to this platform
        /// </summary>
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        // Computed Properties

        /// <summary>
        /// Gets a formatted display name for the platform
        /// </summary>
        [NotMapped]
        public string DisplayName => string.IsNullOrWhiteSpace(PlatformName) 
            ? $"Platform {PlatformNumber}" 
            : $"Platform {PlatformNumber} ({PlatformName})";

        /// <summary>
        /// Gets the full platform identifier including station
        /// </summary>
        [NotMapped]
        public string FullIdentifier => $"{Station?.Code}-{PlatformNumber}";

        /// <summary>
        /// Gets the platform status description
        /// </summary>
        [NotMapped]
        public string StatusDescription => IsActive ? "Active" : "Inactive";

        /// <summary>
        /// Gets the accessibility status description
        /// </summary>
        [NotMapped]
        public string AccessibilityStatus => IsAccessible ? "Accessible" : "Not Accessible";

        /// <summary>
        /// Gets the number of display boards on this platform
        /// </summary>
        [NotMapped]
        public int DisplayBoardCount => DisplayBoards?.Count ?? 0;

        /// <summary>
        /// Gets the number of active schedules for this platform
        /// </summary>
        [NotMapped]
        public int ActiveScheduleCount => Schedules?.Count(s => s.Status != ScheduleStatus.Cancelled && 
                                                                s.Status != ScheduleStatus.Departed) ?? 0;

        // Methods

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the platform has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return StationId > 0 && 
                   !string.IsNullOrWhiteSpace(PlatformNumber) && 
                   PlatformNumber.Length <= 10;
        }

        /// <summary>
        /// Checks if the platform can accommodate a train with specified number of coaches
        /// </summary>
        /// <param name="coachCount">Number of coaches</param>
        /// <returns>True if can accommodate, false otherwise</returns>
        public bool CanAccommodate(int coachCount)
        {
            return MaxCoaches == null || MaxCoaches >= coachCount;
        }

        /// <summary>
        /// Gets a summary of the platform for display purposes
        /// </summary>
        /// <returns>Platform summary string</returns>
        public string GetSummary()
        {
            return $"Platform: {DisplayName}, Type: {PlatformType}, " +
                   $"Length: {Length?.ToString("F1") ?? "N/A"}m, Status: {StatusDescription}";
        }

        /// <summary>
        /// Gets the facilities available on this platform
        /// </summary>
        /// <returns>List of available facilities</returns>
        public List<string> GetAvailableFacilities()
        {
            var facilities = new List<string>();
            
            if (HasCover) facilities.Add("Covered");
            if (IsAccessible) facilities.Add("Wheelchair Accessible");
            if (HasLighting) facilities.Add("Lighting");
            if (HasSeating) facilities.Add("Seating");
            if (HasWaterFacility) facilities.Add("Water");
            if (HasRestroom) facilities.Add("Restroom");
            
            return facilities;
        }

        /// <summary>
        /// Returns a string representation of the platform
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayName;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current platform
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is Platform other)
            {
                return Id == other.Id && 
                       StationId == other.StationId && 
                       PlatformNumber.Equals(other.PlatformNumber, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the platform
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Id, StationId, PlatformNumber.ToUpperInvariant());
        }
    }
}
