using System.ComponentModel;

namespace IPIS.WindowsApp.Models.Enums
{
    /// <summary>
    /// Represents the status of a train schedule
    /// Based on legacy system analysis - supports all existing status types
    /// </summary>
    public enum ScheduleStatus
    {
        [Description("Scheduled")]
        Scheduled = 0,

        [Description("On Time")]
        OnTime = 1,

        [Description("Delayed")]
        Delayed = 2,

        [Description("Cancelled")]
        Cancelled = 3,

        [Description("Departed")]
        Departed = 4,

        [Description("Arrived")]
        Arrived = 5,

        [Description("Diverted")]
        Diverted = 6,

        [Description("Platform Changed")]
        PlatformChanged = 7,

        [Description("Terminated")]
        Terminated = 8,

        [Description("Rescheduled")]
        Rescheduled = 9,

        [Description("Running Late")]
        RunningLate = 10,

        [Description("Approaching")]
        Approaching = 11,

        [Description("At Platform")]
        AtPlatform = 12
    }

    /// <summary>
    /// Represents different types of display boards
    /// Based on legacy system analysis - supports all 5 board types
    /// </summary>
    public enum DisplayBoardType
    {
        [Description("Arrival/General Display Board")]
        AGDB = 0,

        [Description("Coach Guidance Display Board")]
        CGDB = 1,

        [Description("Multi-Line Display Board")]
        MLDB = 2,

        [Description("Platform Display Board")]
        PDB = 3,

        [Description("Platform Display Controller Hub")]
        PDCH = 4,

        [Description("Multi Display Controller Hub")]
        MDCH = 5
    }

    /// <summary>
    /// Represents different types of messages
    /// Based on legacy system analysis - supports all message categories
    /// </summary>
    public enum MessageType
    {
        [Description("Train Information")]
        Train = 0,

        [Description("General Announcement")]
        Announcement = 1,

        [Description("Emergency Message")]
        Emergency = 2,

        [Description("Information Message")]
        Information = 3,

        [Description("Weather Information")]
        Weather = 4,

        [Description("Advertisement")]
        Advertisement = 5,

        [Description("Platform Change")]
        PlatformChange = 6,

        [Description("Special Message")]
        Special = 7,

        [Description("Maintenance Message")]
        Maintenance = 8,

        [Description("Security Alert")]
        Security = 9
    }

    /// <summary>
    /// Represents user roles in the system
    /// Based on legacy system analysis - supports all access levels
    /// </summary>
    public enum UserRole
    {
        [Description("System Administrator")]
        Admin = 0,

        [Description("Train Operator")]
        Operator = 1,

        [Description("Information Viewer")]
        Viewer = 2,

        [Description("Maintenance Personnel")]
        Maintenance = 3,

        [Description("Station Master")]
        StationMaster = 4,

        [Description("Announcer")]
        Announcer = 5,

        [Description("Advertisement Manager")]
        AdvertisementManager = 6
    }

    /// <summary>
    /// Represents different types of trains
    /// Based on legacy system analysis - supports all train categories
    /// </summary>
    public enum TrainType
    {
        [Description("Express")]
        Express = 0,

        [Description("Local")]
        Local = 1,

        [Description("Passenger")]
        Passenger = 2,

        [Description("Mail")]
        Mail = 3,

        [Description("Freight")]
        Freight = 4,

        [Description("Rajdhani")]
        Rajdhani = 5,

        [Description("Shatabdi")]
        Shatabdi = 6,

        [Description("Duronto")]
        Duronto = 7,

        [Description("Jan Shatabdi")]
        JanShatabdi = 8,

        [Description("Intercity")]
        Intercity = 9,

        [Description("Special")]
        Special = 10,

        [Description("Goods")]
        Goods = 11
    }

    /// <summary>
    /// Represents different types of stations
    /// Based on legacy system analysis
    /// </summary>
    public enum StationType
    {
        [Description("Terminal Station")]
        Terminal = 0,

        [Description("Junction Station")]
        Junction = 1,

        [Description("Regular Station")]
        Regular = 2,

        [Description("Halt Station")]
        Halt = 3,

        [Description("Crossing Station")]
        Crossing = 4
    }

    /// <summary>
    /// Represents different types of platforms
    /// Based on legacy system analysis
    /// </summary>
    public enum PlatformType
    {
        [Description("Passenger Platform")]
        Passenger = 0,

        [Description("Freight Platform")]
        Freight = 1,

        [Description("Mixed Platform")]
        Mixed = 2,

        [Description("Maintenance Platform")]
        Maintenance = 3
    }

    /// <summary>
    /// Represents communication protocols
    /// Based on legacy system analysis
    /// </summary>
    public enum CommunicationProtocol
    {
        [Description("TCP/IP")]
        TCP = 0,

        [Description("UDP")]
        UDP = 1,

        [Description("Serial RS232")]
        Serial = 2,

        [Description("Custom Protocol")]
        Custom = 3
    }

    /// <summary>
    /// Represents audit action types
    /// For compliance and tracking
    /// </summary>
    public enum AuditAction
    {
        [Description("Create")]
        Create = 0,

        [Description("Update")]
        Update = 1,

        [Description("Delete")]
        Delete = 2,

        [Description("View")]
        View = 3,

        [Description("Login")]
        Login = 4,

        [Description("Logout")]
        Logout = 5,

        [Description("Export")]
        Export = 6,

        [Description("Import")]
        Import = 7,

        [Description("Backup")]
        Backup = 8,

        [Description("Restore")]
        Restore = 9
    }

    /// <summary>
    /// Represents voice announcement types
    /// Based on legacy system analysis
    /// </summary>
    public enum AnnouncementType
    {
        [Description("Train Arrival")]
        TrainArrival = 0,

        [Description("Train Departure")]
        TrainDeparture = 1,

        [Description("Platform Change")]
        PlatformChange = 2,

        [Description("Special Message")]
        SpecialMessage = 3,

        [Description("Advertisement")]
        Advertisement = 4,

        [Description("Emergency")]
        Emergency = 5,

        [Description("General Information")]
        GeneralInformation = 6
    }

    /// <summary>
    /// Represents supported languages
    /// Based on legacy system analysis
    /// </summary>
    public enum Language
    {
        [Description("English")]
        English = 0,

        [Description("Hindi")]
        Hindi = 1,

        [Description("Regional")]
        Regional = 2,

        [Description("Telugu")]
        Telugu = 3,

        [Description("Marathi")]
        Marathi = 4,

        [Description("Tamil")]
        Tamil = 5,

        [Description("Bengali")]
        Bengali = 6
    }
}
