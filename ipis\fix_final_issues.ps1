# PowerShell script to fix all remaining compilation issues

Write-Host "=== FIXING FINAL COMPILATION ISSUES ==="

# Get all C# files
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix 1: Remove nameof() calls (not supported in older C#)
    $content = $content -replace 'nameof\([^)]+\)', '""'
    
    # Fix 2: Fix property setters that return values
    $content = $content -replace 'set\s*\{\s*return\s+([^;]+);\s*\}', 'set { $1; }'
    
    # Fix 3: Fix ref parameters in method calls (remove ref keyword from calls)
    $content = $content -replace '\bref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*,', '$1,'
    $content = $content -replace '\bref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\)', '$1)'
    
    # Fix 4: Fix unassigned variable issues by initializing them
    $content = $content -replace '(\w+)\s+(\w+);\s*$', '$1 $2 = default($1);'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed syntax issues"
    }
}

Write-Host "Final issues fix complete!"
