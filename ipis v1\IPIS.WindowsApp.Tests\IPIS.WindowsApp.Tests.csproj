<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <!-- Testing Framework -->
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="NUnit.Analyzers" Version="3.9.0" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    
    <!-- Mocking Framework -->
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="AutoFixture" Version="4.18.0" />
    <PackageReference Include="AutoFixture.NUnit3" Version="4.18.0" />
    <PackageReference Include="AutoFixture.AutoMoq" Version="4.18.0" />
    
    <!-- Entity Framework Testing -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.25" />
    
    <!-- Assertion Libraries -->
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    
    <!-- Test Data Generation -->
    <PackageReference Include="Bogus" Version="34.0.2" />
    
    <!-- Performance Testing -->
    <PackageReference Include="NBomber" Version="5.0.6" />
    
    <!-- HTTP Testing -->
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.25" />
    
    <!-- Configuration Testing -->
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    
    <!-- Logging for Tests -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\IPIS.WindowsApp\IPIS.WindowsApp.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="UnitTests\Models\Entities\" />
    <Folder Include="UnitTests\Services\" />
    <Folder Include="UnitTests\Communication\" />
    <Folder Include="UnitTests\Voice\" />
    <Folder Include="UnitTests\Advertisement\" />
    <Folder Include="IntegrationTests\Database\" />
    <Folder Include="IntegrationTests\API\" />
    <Folder Include="IntegrationTests\Communication\" />
    <Folder Include="PerformanceTests\" />
    <Folder Include="TestData\" />
    <Folder Include="Helpers\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="TestData\*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\*.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
