using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using IPIS.WindowsApp.Models.Enums;

namespace IPIS.WindowsApp.Models.Entities
{
    /// <summary>
    /// Represents a display board entity
    /// Based on legacy system analysis - supports all 5 board types (AGDB, CGDB, MLDB, PDB, PDCH)
    /// </summary>
    [Table("DisplayBoards")]
    public class DisplayBoard
    {
        /// <summary>
        /// Primary key for the display board
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the station this display board belongs to
        /// </summary>
        [Required]
        [ForeignKey("Station")]
        public int StationId { get; set; }

        /// <summary>
        /// Foreign key to the platform (optional, for platform-specific boards)
        /// </summary>
        [ForeignKey("Platform")]
        public int? PlatformId { get; set; }

        /// <summary>
        /// Type of display board (AGDB, CGDB, MLDB, PDB, PDCH)
        /// Based on legacy system analysis
        /// </summary>
        [Required]
        public DisplayBoardType BoardType { get; set; }

        /// <summary>
        /// Unique identifier/name for the display board
        /// </summary>
        [Required(ErrorMessage = "Board name is required")]
        [StringLength(100, ErrorMessage = "Board name cannot exceed 100 characters")]
        [Column("BoardName")]
        public string BoardName { get; set; } = string.Empty;

        /// <summary>
        /// Physical location description of the board
        /// </summary>
        [StringLength(100)]
        public string? BoardLocation { get; set; }

        /// <summary>
        /// IP address for network communication
        /// </summary>
        [StringLength(15)]
        [RegularExpression(@"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$", ErrorMessage = "Invalid IP address format")]
        public string? IpAddress { get; set; }

        /// <summary>
        /// Port number for network communication
        /// </summary>
        [Range(1, 65535, ErrorMessage = "Port must be between 1 and 65535")]
        public int? Port { get; set; }

        /// <summary>
        /// Communication protocol (TCP, UDP, Serial)
        /// </summary>
        [Required]
        public CommunicationProtocol Protocol { get; set; } = CommunicationProtocol.TCP;

        /// <summary>
        /// Indicates if the display board is currently online
        /// </summary>
        [Required]
        public bool IsOnline { get; set; } = false;

        /// <summary>
        /// Last heartbeat/ping timestamp
        /// </summary>
        public DateTime? LastHeartbeat { get; set; }

        /// <summary>
        /// Board-specific configuration in JSON format
        /// </summary>
        [Column(TypeName = "TEXT")]
        public string? Configuration { get; set; }

        /// <summary>
        /// Supported display languages (comma-separated)
        /// </summary>
        [StringLength(50)]
        public string DisplayLanguages { get; set; } = "en";

        /// <summary>
        /// Refresh interval in seconds
        /// </summary>
        [Range(1, 3600, ErrorMessage = "Refresh interval must be between 1 and 3600 seconds")]
        public int RefreshInterval { get; set; } = 30;

        /// <summary>
        /// Maximum message length for this board type
        /// </summary>
        [Range(1, 1000, ErrorMessage = "Max message length must be between 1 and 1000 characters")]
        public int MaxMessageLength { get; set; } = 240;

        /// <summary>
        /// Number of display lines (for multi-line boards)
        /// </summary>
        [Range(1, 10, ErrorMessage = "Display lines must be between 1 and 10")]
        public int DisplayLines { get; set; } = 1;

        /// <summary>
        /// Display brightness level (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "Brightness must be between 0 and 100")]
        public int Brightness { get; set; } = 80;

        /// <summary>
        /// Indicates if the board supports scrolling text
        /// </summary>
        public bool SupportsScrolling { get; set; } = true;

        /// <summary>
        /// Scrolling speed (characters per second)
        /// </summary>
        [Range(1, 50, ErrorMessage = "Scroll speed must be between 1 and 50")]
        public int ScrollSpeed { get; set; } = 5;

        /// <summary>
        /// Font size for display
        /// </summary>
        [Range(8, 72, ErrorMessage = "Font size must be between 8 and 72")]
        public int FontSize { get; set; } = 12;

        /// <summary>
        /// Font name/family
        /// </summary>
        [StringLength(50)]
        public string? FontName { get; set; } = "Arial";

        /// <summary>
        /// Indicates if the board is currently active
        /// </summary>
        [Required]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Last maintenance date
        /// </summary>
        public DateTime? LastMaintenance { get; set; }

        /// <summary>
        /// Next scheduled maintenance date
        /// </summary>
        public DateTime? NextMaintenance { get; set; }

        /// <summary>
        /// Firmware version of the display board
        /// </summary>
        [StringLength(20)]
        public string? FirmwareVersion { get; set; }

        /// <summary>
        /// Hardware model/type
        /// </summary>
        [StringLength(50)]
        public string? HardwareModel { get; set; }

        /// <summary>
        /// Serial number of the device
        /// </summary>
        [StringLength(50)]
        public string? SerialNumber { get; set; }

        /// <summary>
        /// Installation date
        /// </summary>
        public DateTime? InstallationDate { get; set; }

        /// <summary>
        /// Warranty expiration date
        /// </summary>
        public DateTime? WarrantyExpiry { get; set; }

        /// <summary>
        /// Additional remarks or notes
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// Record creation timestamp
        /// </summary>
        [Required]
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Record last update timestamp
        /// </summary>
        [Required]
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(100)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(100)]
        public string? UpdatedBy { get; set; }

        // Navigation Properties

        /// <summary>
        /// The station this display board belongs to
        /// </summary>
        public virtual Station Station { get; set; } = null!;

        /// <summary>
        /// The platform this display board is associated with (optional)
        /// </summary>
        public virtual Platform? Platform { get; set; }

        /// <summary>
        /// Collection of messages displayed on this board
        /// </summary>
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        // Computed Properties

        /// <summary>
        /// Gets a formatted display name for the board
        /// </summary>
        [NotMapped]
        public string DisplayName => $"{BoardType} - {BoardName}";

        /// <summary>
        /// Gets the full board identifier including station
        /// </summary>
        [NotMapped]
        public string FullIdentifier => $"{Station?.Code}-{BoardType}-{BoardName}";

        /// <summary>
        /// Gets the connection status description
        /// </summary>
        [NotMapped]
        public string ConnectionStatus => IsOnline ? "Online" : "Offline";

        /// <summary>
        /// Gets the network endpoint (IP:Port)
        /// </summary>
        [NotMapped]
        public string NetworkEndpoint => !string.IsNullOrWhiteSpace(IpAddress) && Port.HasValue 
            ? $"{IpAddress}:{Port}" 
            : "Not configured";

        /// <summary>
        /// Gets the time since last heartbeat
        /// </summary>
        [NotMapped]
        public TimeSpan? TimeSinceLastHeartbeat => LastHeartbeat.HasValue 
            ? DateTime.UtcNow - LastHeartbeat.Value 
            : null;

        /// <summary>
        /// Indicates if the board is considered healthy
        /// </summary>
        [NotMapped]
        public bool IsHealthy => IsOnline && 
                                LastHeartbeat.HasValue && 
                                DateTime.UtcNow - LastHeartbeat.Value < TimeSpan.FromMinutes(5);

        /// <summary>
        /// Gets the supported languages as a list
        /// </summary>
        [NotMapped]
        public List<string> SupportedLanguages => DisplayLanguages
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(lang => lang.Trim())
            .ToList();

        // Methods

        /// <summary>
        /// Updates the heartbeat timestamp
        /// </summary>
        public void UpdateHeartbeat()
        {
            LastHeartbeat = DateTime.UtcNow;
            IsOnline = true;
            UpdateTimestamp();
        }

        /// <summary>
        /// Marks the board as offline
        /// </summary>
        public void MarkOffline()
        {
            IsOnline = false;
            UpdateTimestamp();
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp
        /// </summary>
        public void UpdateTimestamp()
        {
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the UpdatedAt timestamp and UpdatedBy user
        /// </summary>
        /// <param name="updatedBy">User who is updating the record</param>
        public void UpdateTimestamp(string updatedBy)
        {
            UpdatedAt = DateTime.UtcNow;
            UpdatedBy = updatedBy;
        }

        /// <summary>
        /// Validates if the display board has the minimum required information
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return StationId > 0 && 
                   !string.IsNullOrWhiteSpace(BoardName) && 
                   BoardName.Length <= 100;
        }

        /// <summary>
        /// Checks if the board supports a specific language
        /// </summary>
        /// <param name="language">Language code to check</param>
        /// <returns>True if supported, false otherwise</returns>
        public bool SupportsLanguage(string language)
        {
            return SupportedLanguages.Contains(language, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets the maximum message length based on board type
        /// </summary>
        /// <returns>Maximum message length</returns>
        public int GetMaxMessageLength()
        {
            return BoardType switch
            {
                DisplayBoardType.AGDB => 240,
                DisplayBoardType.CGDB => 200,
                DisplayBoardType.MLDB => 160,
                DisplayBoardType.PDB => 120,
                DisplayBoardType.PDCH => 100,
                _ => MaxMessageLength
            };
        }

        /// <summary>
        /// Gets a summary of the display board for display purposes
        /// </summary>
        /// <returns>Display board summary string</returns>
        public string GetSummary()
        {
            return $"Board: {DisplayName}, Status: {ConnectionStatus}, " +
                   $"Location: {BoardLocation ?? "N/A"}, Endpoint: {NetworkEndpoint}";
        }

        /// <summary>
        /// Returns a string representation of the display board
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return DisplayName;
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current display board
        /// </summary>
        /// <param name="obj">The object to compare</param>
        /// <returns>True if equal, false otherwise</returns>
        public override bool Equals(object? obj)
        {
            if (obj is DisplayBoard other)
            {
                return Id == other.Id && 
                       StationId == other.StationId && 
                       BoardName.Equals(other.BoardName, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Returns a hash code for the display board
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Id, StationId, BoardName.ToUpperInvariant());
        }
    }
}
