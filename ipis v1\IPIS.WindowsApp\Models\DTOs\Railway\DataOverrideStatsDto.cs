namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// DTO for data override statistics
    /// </summary>
    public class DataOverrideStatsDto
    {
        /// <summary>
        /// Total number of active overrides
        /// </summary>
        public int TotalActiveOverrides { get; set; }

        /// <summary>
        /// Number of active train overrides
        /// </summary>
        public int ActiveTrainOverrides { get; set; }

        /// <summary>
        /// Number of active station overrides
        /// </summary>
        public int ActiveStationOverrides { get; set; }

        /// <summary>
        /// Number of permanent overrides
        /// </summary>
        public int PermanentOverrides { get; set; }

        /// <summary>
        /// Number of temporary overrides
        /// </summary>
        public int TemporaryOverrides { get; set; }

        /// <summary>
        /// Number of expired overrides (not yet cleaned up)
        /// </summary>
        public int ExpiredOverrides { get; set; }

        /// <summary>
        /// Total number of overrides created today
        /// </summary>
        public int OverridesToday { get; set; }

        /// <summary>
        /// Total number of overrides created this week
        /// </summary>
        public int OverridesThisWeek { get; set; }

        /// <summary>
        /// Total number of overrides created this month
        /// </summary>
        public int OverridesThisMonth { get; set; }

        /// <summary>
        /// Most recent override creation time
        /// </summary>
        public DateTime? LastOverrideCreated { get; set; }

        /// <summary>
        /// Most recent override expiry time
        /// </summary>
        public DateTime? LastOverrideExpired { get; set; }

        /// <summary>
        /// Average override duration in hours
        /// </summary>
        public double AverageOverrideDuration { get; set; }

        /// <summary>
        /// Most frequently overridden train
        /// </summary>
        public string? MostOverriddenTrain { get; set; }

        /// <summary>
        /// Most frequently overridden station
        /// </summary>
        public string? MostOverriddenStation { get; set; }

        /// <summary>
        /// User who created the most overrides
        /// </summary>
        public string? MostActiveUser { get; set; }

        /// <summary>
        /// Most common override reason
        /// </summary>
        public string? MostCommonReason { get; set; }

        /// <summary>
        /// Override usage by hour of day
        /// </summary>
        public Dictionary<int, int> OverridesByHour { get; set; } = new();

        /// <summary>
        /// Override usage by day of week
        /// </summary>
        public Dictionary<string, int> OverridesByDayOfWeek { get; set; } = new();

        /// <summary>
        /// Override reasons and their frequency
        /// </summary>
        public Dictionary<string, int> OverrideReasons { get; set; } = new();

        /// <summary>
        /// Users and their override counts
        /// </summary>
        public Dictionary<string, int> OverridesByUser { get; set; } = new();

        /// <summary>
        /// Stations and their override counts
        /// </summary>
        public Dictionary<string, int> OverridesByStation { get; set; } = new();

        /// <summary>
        /// Trains and their override counts
        /// </summary>
        public Dictionary<string, int> OverridesByTrain { get; set; } = new();

        /// <summary>
        /// Next override expiry time
        /// </summary>
        public DateTime? NextOverrideExpiry { get; set; }

        /// <summary>
        /// Number of overrides expiring in the next hour
        /// </summary>
        public int OverridesExpiringNextHour { get; set; }

        /// <summary>
        /// Number of overrides expiring today
        /// </summary>
        public int OverridesExpiringToday { get; set; }

        /// <summary>
        /// System health status based on override usage
        /// </summary>
        public string HealthStatus
        {
            get
            {
                var overrideRate = TotalActiveOverrides;
                return overrideRate switch
                {
                    0 => "Excellent",
                    <= 5 => "Good",
                    <= 15 => "Fair",
                    <= 30 => "Poor",
                    _ => "Critical"
                };
            }
        }

        /// <summary>
        /// Health status color for UI
        /// </summary>
        public string HealthColor
        {
            get
            {
                return HealthStatus switch
                {
                    "Excellent" => "Green",
                    "Good" => "LightGreen",
                    "Fair" => "Yellow",
                    "Poor" => "Orange",
                    "Critical" => "Red",
                    _ => "Gray"
                };
            }
        }

        /// <summary>
        /// Override trend (increasing, decreasing, stable)
        /// </summary>
        public string OverrideTrend
        {
            get
            {
                if (OverridesThisWeek > OverridesToday * 7 * 1.2) return "Increasing";
                if (OverridesThisWeek < OverridesToday * 7 * 0.8) return "Decreasing";
                return "Stable";
            }
        }

        /// <summary>
        /// Trend color for UI
        /// </summary>
        public string TrendColor
        {
            get
            {
                return OverrideTrend switch
                {
                    "Increasing" => "Red",
                    "Decreasing" => "Green",
                    "Stable" => "Blue",
                    _ => "Gray"
                };
            }
        }

        /// <summary>
        /// Time since last override was created
        /// </summary>
        public TimeSpan? TimeSinceLastOverride
        {
            get
            {
                return LastOverrideCreated.HasValue ? DateTime.UtcNow - LastOverrideCreated.Value : null;
            }
        }

        /// <summary>
        /// Time until next override expires
        /// </summary>
        public TimeSpan? TimeToNextExpiry
        {
            get
            {
                return NextOverrideExpiry.HasValue ? NextOverrideExpiry.Value - DateTime.UtcNow : null;
            }
        }

        /// <summary>
        /// Percentage of overrides that are permanent
        /// </summary>
        public double PermanentOverridePercentage
        {
            get
            {
                return TotalActiveOverrides > 0 ? (double)PermanentOverrides / TotalActiveOverrides * 100 : 0;
            }
        }

        /// <summary>
        /// Average overrides per day this month
        /// </summary>
        public double AverageOverridesPerDay
        {
            get
            {
                var daysInMonth = DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month);
                return OverridesThisMonth > 0 ? (double)OverridesThisMonth / daysInMonth : 0;
            }
        }
    }
}
