# PowerShell script to fix missing ref parameters in method calls

Write-Host "=== FIXING MISSING REF PARAMETERS ==="

$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -File

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    $originalContent = $content
    
    # Fix specific method calls that need ref parameters
    $content = $content -replace '\.train_number_byte\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.train_number_byte($1, ref $2, ref $3)'
    $content = $content -replace '\.platform_no_voice\(([^,]+),\s*([^,]+),\s*([^)]+)\)', '.platform_no_voice($1, $2, $3)'
    $content = $content -replace '\.train_status_voice\(([^,]+),\s*([^)]+)\)', '.train_status_voice($1, $2)'
    $content = $content -replace '\.reg_time_hour_voice\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.reg_time_hour_voice($1, $2, $3, $4)'
    $content = $content -replace '\.reg_arr_dep_time_voice\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.reg_arr_dep_time_voice($1, $2, $3, $4)'
    $content = $content -replace '\.hindi_arr_dep_time_voice\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)', '.hindi_arr_dep_time_voice($1, $2, $3, $4)'
    $content = $content -replace '\.station_name_voice\(([^,]+),\s*([^)]+)\)', '.station_name_voice($1, $2)'
    
    # Fix invalid ref expressions that are causing syntax errors
    $content = $content -replace 'ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*;', '$1;'
    
    # Fix method calls with incorrect parentheses
    $content = $content -replace '\(\s*ref\s+([^,)]+)\s*,\s*ref\s+([^,)]+)\s*,\s*ref\s+([^,)]+)\s*,\s*ref\s+([^,)]+)\s*,\s*ref\s+([^)]+)\s*\)', '(ref $1, ref $2, ref $3, ref $4, ref $5)'
    
    # Fix specific issues with voice_xml_files
    $content = $content -replace 'ref\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\[([^\]]+)\]', '$1[$2]'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed ref parameter issues"
    }
}

Write-Host "Ref parameter fixes complete!"
