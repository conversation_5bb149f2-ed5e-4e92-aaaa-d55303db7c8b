using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;
using IPIS.WindowsApp.Data;
using IPIS.WindowsApp.Configuration;
using IPIS.WindowsApp.Services.Interfaces;
using IPIS.WindowsApp.Services.Implementations;
using IPIS.WindowsApp.Forms;
using IPIS.WindowsApp.Models;

namespace IPIS.WindowsApp
{
    /// <summary>
    /// Main program entry point for IPIS Windows Forms Application
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configure Serilog early
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/ipis-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            try
            {
                Log.Information("Starting IPIS Windows Application");

                // To customize application configuration such as set high DPI settings or default font,
                // see https://aka.ms/applicationconfiguration.
                ApplicationConfiguration.Initialize();

                // Build configuration
                var configuration = BuildConfiguration();

                // Build service provider
                var serviceProvider = BuildServiceProvider(configuration);

                // Initialize database
                InitializeDatabase(serviceProvider);

                // Start the application
                var mainForm = serviceProvider.GetRequiredService<MainForm>();
                Application.Run(mainForm);

                Log.Information("IPIS Windows Application stopped");
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "IPIS Windows Application terminated unexpectedly");
                MessageBox.Show($"A fatal error occurred: {ex.Message}", "IPIS Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// Builds the application configuration
        /// </summary>
        /// <returns>Configuration root</returns>
        private static IConfiguration BuildConfiguration()
        {
            return new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json",
                    optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();
        }

        /// <summary>
        /// Builds the service provider with dependency injection
        /// </summary>
        /// <param name="configuration">Application configuration</param>
        /// <returns>Service provider</returns>
        private static ServiceProvider BuildServiceProvider(IConfiguration configuration)
        {
            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton<IConfiguration>(configuration);

            // Configure options
            services.Configure<RailwayApiConfiguration>(configuration.GetSection("RailwayApi"));
            services.Configure<SyncConfiguration>(configuration.GetSection("Synchronization"));

            // Add logging
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // Add Entity Framework
            services.AddDbContext<IPISDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                options.UseSqlite(connectionString);
                options.EnableSensitiveDataLogging(false);
                options.EnableDetailedErrors(true);
            });

            // Add HTTP client for Railway API
            services.AddHttpClient<IRailwayApiService, RailwayApiService>(client =>
            {
                var apiConfig = configuration.GetSection("RailwayApi").Get<RailwayApiConfiguration>();
                if (apiConfig != null)
                {
                    client.BaseAddress = new Uri(apiConfig.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(apiConfig.TimeoutSeconds);
                }
            });

            // Add application services
            services.AddScoped<IRailwayApiService, RailwayApiService>();
            services.AddSingleton<IScheduleSyncService, ScheduleSyncService>();
            services.AddScoped<IStationService, StationService>();
            services.AddScoped<IAutoRefreshService, AutoRefreshService>();

            // Add Windows Forms
            services.AddScoped<MainForm>();

            // Add hosted services
            services.AddHostedService<ScheduleSyncService>();

            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Initializes the database with migrations
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        private static void InitializeDatabase(ServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<IPISDbContext>();
                var configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();

                // Create database schema manually
                Log.Information("Creating database schema manually");

                // Ensure database is deleted first
                context.Database.EnsureDeleted();

                // Create the database file
                context.Database.OpenConnection();

                // Create Stations table manually
                var createStationsTable = @"
                    CREATE TABLE IF NOT EXISTS Stations (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL UNIQUE,
                        Name TEXT NOT NULL,
                        Location TEXT,
                        Type INTEGER NOT NULL DEFAULT 0,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT NOT NULL
                    );";

                context.Database.ExecuteSqlRaw(createStationsTable);

                Log.Information("Database schema created successfully");

                // Seed some initial data if needed
                SeedInitialData(context);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error initializing database");
                throw;
            }
        }

        /// <summary>
        /// Seeds initial data into the database
        /// </summary>
        /// <param name="context">Database context</param>
        private static void SeedInitialData(IPISDbContext context)
        {
            try
            {
                // Check if we already have data
                if (context.Stations.Any())
                {
                    Log.Information("Database already contains data, skipping seed");
                    return;
                }

                Log.Information("Seeding initial data");

                // Add some sample stations
                var stations = new[]
                {
                    new Station
                    {
                        Code = "DEL",
                        Name = "New Delhi",
                        Location = "Delhi",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Station
                    {
                        Code = "BOM",
                        Name = "Mumbai Central",
                        Location = "Mumbai",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Station
                    {
                        Code = "BLR",
                        Name = "Bangalore City",
                        Location = "Bangalore",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.Stations.AddRange(stations);
                context.SaveChanges();

                Log.Information("Initial data seeded successfully");
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Error seeding initial data");
                // Don't throw here as this is not critical
            }
        }
    }
}
