using Microsoft.Extensions.Logging;

namespace IPIS.WindowsApp.Tests
{
    /// <summary>
    /// Test runner for executing Railway API integration tests
    /// </summary>
    public class TestRunner
    {
        /// <summary>
        /// Main entry point for running Railway API integration tests
        /// </summary>
        public static async Task<int> Main(string[] args)
        {
            Console.WriteLine("🚀 IPIS Railway API Integration Test Suite");
            Console.WriteLine("==========================================");
            Console.WriteLine();

            try
            {
                // Create and run integration tests
                using var integrationTests = new RailwayApiIntegrationTests();
                
                Console.WriteLine("Starting Railway API integration tests...");
                Console.WriteLine();
                
                var testsPassed = await integrationTests.RunAllTests();
                
                Console.WriteLine();
                Console.WriteLine("==========================================");
                
                if (testsPassed)
                {
                    Console.WriteLine("🎉 All Railway API integration tests completed successfully!");
                    Console.WriteLine("✅ Railway API integration is fully functional and ready for production use.");
                    return 0; // Success
                }
                else
                {
                    Console.WriteLine("❌ Some Railway API integration tests failed.");
                    Console.WriteLine("⚠️ Please review the test results and fix any issues before deploying.");
                    return 1; // Failure
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 Fatal error during test execution: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return 2; // Fatal error
            }
        }
    }
}
