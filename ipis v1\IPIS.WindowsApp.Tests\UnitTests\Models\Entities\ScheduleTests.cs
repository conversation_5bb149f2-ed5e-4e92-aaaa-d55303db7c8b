using NUnit.Framework;
using FluentAssertions;
using AutoFixture;
using AutoFixture.NUnit3;
using IPIS.WindowsApp.Models.Entities;
using IPIS.WindowsApp.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Tests.UnitTests.Models.Entities
{
    /// <summary>
    /// Comprehensive test suite for Schedule entity
    /// Tests every line of code and business logic - critical for IPIS operations
    /// </summary>
    [TestFixture]
    public class ScheduleTests
    {
        private IFixture _fixture;

        [SetUp]
        public void SetUp()
        {
            _fixture = new Fixture();
            _fixture.Customize<Schedule>(c => c
                .With(s => s.TrainId, 1)
                .With(s => s.StationId, 1)
                .With(s => s.Status, ScheduleStatus.Scheduled)
                .With(s => s.DelayMinutes, 0)
                .With(s => s.IsPublished, false)
                .With(s => s.Priority, 5));
        }

        #region Constructor and Property Tests

        [Test]
        public void Schedule_DefaultConstructor_SetsDefaultValues()
        {
            // Act
            var schedule = new Schedule();

            // Assert
            schedule.Id.Should().Be(0);
            schedule.TrainId.Should().Be(0);
            schedule.StationId.Should().Be(0);
            schedule.PlatformId.Should().BeNull();
            schedule.Status.Should().Be(ScheduleStatus.Scheduled);
            schedule.DelayMinutes.Should().Be(0);
            schedule.IsPublished.Should().BeFalse();
            schedule.Priority.Should().Be(5);
            schedule.IsOrigin.Should().BeFalse();
            schedule.IsDestination.Should().BeFalse();
            schedule.IsTechnicalHalt.Should().BeFalse();
            schedule.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            schedule.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            schedule.Messages.Should().NotBeNull().And.BeEmpty();
        }

        [Test]
        [AutoData]
        public void Schedule_SetProperties_SetsCorrectly(int trainId, int stationId, int platformId, int delayMinutes)
        {
            // Arrange
            var schedule = new Schedule();
            var scheduledArrival = DateTime.Now.AddHours(1);
            var scheduledDeparture = DateTime.Now.AddHours(2);
            var status = ScheduleStatus.Delayed;

            // Act
            schedule.TrainId = trainId;
            schedule.StationId = stationId;
            schedule.PlatformId = platformId;
            schedule.ScheduledArrival = scheduledArrival;
            schedule.ScheduledDeparture = scheduledDeparture;
            schedule.Status = status;
            schedule.DelayMinutes = delayMinutes;
            schedule.IsPublished = true;
            schedule.IsOrigin = true;

            // Assert
            schedule.TrainId.Should().Be(trainId);
            schedule.StationId.Should().Be(stationId);
            schedule.PlatformId.Should().Be(platformId);
            schedule.ScheduledArrival.Should().Be(scheduledArrival);
            schedule.ScheduledDeparture.Should().Be(scheduledDeparture);
            schedule.Status.Should().Be(status);
            schedule.DelayMinutes.Should().Be(delayMinutes);
            schedule.IsPublished.Should().BeTrue();
            schedule.IsOrigin.Should().BeTrue();
        }

        #endregion

        #region Validation Tests

        [Test]
        public void Schedule_ValidData_PassesValidation()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var validationContext = new ValidationContext(schedule);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(schedule, validationContext, validationResults, true);

            // Assert
            isValid.Should().BeTrue();
            validationResults.Should().BeEmpty();
        }

        [Test]
        [TestCase(0, 11, false)] // Priority too high
        [TestCase(11, 5, false)] // Priority too high
        [TestCase(1, 1, true)] // Valid range
        [TestCase(10, 10, true)] // Valid range
        public void Schedule_PriorityValidation_ReturnsExpectedResult(int priority, int expectedPriority, bool shouldBeValid)
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Priority = priority;
            var validationContext = new ValidationContext(schedule);
            var validationResults = new List<ValidationResult>();

            // Act
            var isValid = Validator.TryValidateObject(schedule, validationContext, validationResults, true);

            // Assert
            if (shouldBeValid)
            {
                isValid.Should().BeTrue();
                validationResults.Should().BeEmpty();
            }
            else
            {
                isValid.Should().BeFalse();
                validationResults.Should().Contain(vr => vr.ErrorMessage!.Contains("Priority must be between 1 and 10"));
            }
        }

        #endregion

        #region Computed Properties Tests

        [Test]
        public void ExpectedArrival_WithActualArrival_ReturnsActualArrival()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledArrival = DateTime.Now.AddHours(1);
            var actualArrival = DateTime.Now.AddHours(1).AddMinutes(15);
            schedule.ScheduledArrival = scheduledArrival;
            schedule.ActualArrival = actualArrival;

            // Act & Assert
            schedule.ExpectedArrival.Should().Be(actualArrival);
        }

        [Test]
        public void ExpectedArrival_WithoutActualArrival_ReturnsScheduledArrival()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledArrival = DateTime.Now.AddHours(1);
            schedule.ScheduledArrival = scheduledArrival;
            schedule.ActualArrival = null;

            // Act & Assert
            schedule.ExpectedArrival.Should().Be(scheduledArrival);
        }

        [Test]
        public void ExpectedDeparture_WithActualDeparture_ReturnsActualDeparture()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledDeparture = DateTime.Now.AddHours(2);
            var actualDeparture = DateTime.Now.AddHours(2).AddMinutes(10);
            schedule.ScheduledDeparture = scheduledDeparture;
            schedule.ActualDeparture = actualDeparture;

            // Act & Assert
            schedule.ExpectedDeparture.Should().Be(actualDeparture);
        }

        [Test]
        public void ArrivalDelay_WithActualAndScheduledArrival_CalculatesCorrectly()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledArrival = DateTime.Now;
            var actualArrival = scheduledArrival.AddMinutes(15);
            schedule.ScheduledArrival = scheduledArrival;
            schedule.ActualArrival = actualArrival;

            // Act & Assert
            schedule.ArrivalDelay.Should().Be(15);
        }

        [Test]
        public void ArrivalDelay_WithoutActualArrival_ReturnsZero()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ScheduledArrival = DateTime.Now;
            schedule.ActualArrival = null;

            // Act & Assert
            schedule.ArrivalDelay.Should().Be(0);
        }

        [Test]
        public void DepartureDelay_WithActualAndScheduledDeparture_CalculatesCorrectly()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledDeparture = DateTime.Now;
            var actualDeparture = scheduledDeparture.AddMinutes(20);
            schedule.ScheduledDeparture = scheduledDeparture;
            schedule.ActualDeparture = actualDeparture;

            // Act & Assert
            schedule.DepartureDelay.Should().Be(20);
        }

        [Test]
        public void DisplayInfo_WithTrainAndStation_ReturnsFormattedString()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Train = new Train { TrainNumber = "12345" };
            schedule.Station = new Station { Name = "Central Station" };
            schedule.Status = ScheduleStatus.OnTime;

            // Act & Assert
            schedule.DisplayInfo.Should().Be("12345 - Central Station - OnTime");
        }

        [Test]
        public void PlatformDisplay_WithPlatform_ReturnsPlatformNumber()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Platform = new Platform { PlatformNumber = "3A" };

            // Act & Assert
            schedule.PlatformDisplay.Should().Be("3A");
        }

        [Test]
        public void PlatformDisplay_WithoutPlatform_ReturnsTBA()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Platform = null;

            // Act & Assert
            schedule.PlatformDisplay.Should().Be("TBA");
        }

        [Test]
        public void IsDelayed_WithDelayMinutes_ReturnsTrue()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.DelayMinutes = 10;
            schedule.Status = ScheduleStatus.OnTime;

            // Act & Assert
            schedule.IsDelayed.Should().BeTrue();
        }

        [Test]
        public void IsDelayed_WithDelayedStatus_ReturnsTrue()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.DelayMinutes = 0;
            schedule.Status = ScheduleStatus.Delayed;

            // Act & Assert
            schedule.IsDelayed.Should().BeTrue();
        }

        [Test]
        public void IsDelayed_WithoutDelayOrStatus_ReturnsFalse()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.DelayMinutes = 0;
            schedule.Status = ScheduleStatus.OnTime;

            // Act & Assert
            schedule.IsDelayed.Should().BeFalse();
        }

        [Test]
        public void IsToday_WithTodaysScheduledArrival_ReturnsTrue()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ScheduledArrival = DateTime.Today.AddHours(10);
            schedule.ScheduledDeparture = null;

            // Act & Assert
            schedule.IsToday.Should().BeTrue();
        }

        [Test]
        public void IsToday_WithTodaysScheduledDeparture_ReturnsTrue()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ScheduledArrival = null;
            schedule.ScheduledDeparture = DateTime.Today.AddHours(15);

            // Act & Assert
            schedule.IsToday.Should().BeTrue();
        }

        [Test]
        public void IsToday_WithYesterdaysSchedule_ReturnsFalse()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ScheduledArrival = DateTime.Today.AddDays(-1).AddHours(10);
            schedule.ScheduledDeparture = DateTime.Today.AddDays(-1).AddHours(15);

            // Act & Assert
            schedule.IsToday.Should().BeFalse();
        }

        #endregion

        #region Method Tests

        [Test]
        public void CalculateDelay_WithArrivedStatus_CalculatesArrivalDelay()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledArrival = DateTime.Now;
            var actualArrival = scheduledArrival.AddMinutes(25);
            schedule.Status = ScheduleStatus.Arrived;
            schedule.ScheduledArrival = scheduledArrival;
            schedule.ActualArrival = actualArrival;

            // Act
            schedule.CalculateDelay();

            // Assert
            schedule.DelayMinutes.Should().Be(25);
        }

        [Test]
        public void CalculateDelay_WithDepartedStatus_CalculatesDepartureDelay()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledDeparture = DateTime.Now;
            var actualDeparture = scheduledDeparture.AddMinutes(15);
            schedule.Status = ScheduleStatus.Departed;
            schedule.ScheduledDeparture = scheduledDeparture;
            schedule.ActualDeparture = actualDeparture;

            // Act
            schedule.CalculateDelay();

            // Assert
            schedule.DelayMinutes.Should().Be(15);
        }

        [Test]
        public void CalculateDelay_WithDelayedStatus_CalculatesCurrentDelay()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledArrival = DateTime.Now.AddMinutes(-30); // 30 minutes ago
            schedule.Status = ScheduleStatus.Delayed;
            schedule.ScheduledArrival = scheduledArrival;

            // Act
            schedule.CalculateDelay();

            // Assert
            schedule.DelayMinutes.Should().BeGreaterThan(25); // Should be around 30, allowing for test execution time
        }

        [Test]
        public void CalculateDelay_WithEarlyArrival_SetsDelayToZero()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var scheduledArrival = DateTime.Now;
            var actualArrival = scheduledArrival.AddMinutes(-10); // Early arrival
            schedule.Status = ScheduleStatus.Arrived;
            schedule.ScheduledArrival = scheduledArrival;
            schedule.ActualArrival = actualArrival;

            // Act
            schedule.CalculateDelay();

            // Assert
            schedule.DelayMinutes.Should().Be(0); // No negative delays
        }

        [Test]
        public void UpdateStatus_WithActualDeparture_SetsToDeparted()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ActualDeparture = DateTime.Now;
            schedule.Status = ScheduleStatus.Arrived;

            // Act
            schedule.UpdateStatus();

            // Assert
            schedule.Status.Should().Be(ScheduleStatus.Departed);
        }

        [Test]
        public void UpdateStatus_WithActualArrival_SetsToArrived()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ActualArrival = DateTime.Now;
            schedule.ActualDeparture = null;
            schedule.Status = ScheduleStatus.Approaching;

            // Act
            schedule.UpdateStatus();

            // Assert
            schedule.Status.Should().Be(ScheduleStatus.Arrived);
        }

        [Test]
        public void UpdateStatus_WithLateScheduledDeparture_SetsToDelayed()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ScheduledDeparture = DateTime.Now.AddMinutes(-35); // 35 minutes ago
            schedule.ActualDeparture = null;
            schedule.ActualArrival = null;
            schedule.Status = ScheduleStatus.Scheduled;

            // Act
            schedule.UpdateStatus();

            // Assert
            schedule.Status.Should().Be(ScheduleStatus.Delayed);
        }

        [Test]
        public void UpdateStatus_WithApproachingTime_SetsToApproaching()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.ScheduledArrival = DateTime.Now.AddMinutes(-5); // 5 minutes ago (within approaching window)
            schedule.ActualArrival = null;
            schedule.Status = ScheduleStatus.Scheduled;

            // Act
            schedule.UpdateStatus();

            // Assert
            schedule.Status.Should().Be(ScheduleStatus.Approaching);
        }

        [Test]
        public void UpdateTimestamp_WhenCalled_UpdatesTimestamp()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var originalTimestamp = schedule.UpdatedAt;
            Thread.Sleep(10); // Ensure time difference

            // Act
            schedule.UpdateTimestamp();

            // Assert
            schedule.UpdatedAt.Should().BeAfter(originalTimestamp);
        }

        [Test]
        public void UpdateTimestamp_WithUser_UpdatesTimestampAndUser()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            var originalTimestamp = schedule.UpdatedAt;
            var updatedBy = "testuser";
            Thread.Sleep(10); // Ensure time difference

            // Act
            schedule.UpdateTimestamp(updatedBy);

            // Assert
            schedule.UpdatedAt.Should().BeAfter(originalTimestamp);
            schedule.UpdatedBy.Should().Be(updatedBy);
        }

        [Test]
        public void IsValid_WithValidData_ReturnsTrue()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.TrainId = 1;
            schedule.StationId = 1;
            schedule.ScheduledArrival = DateTime.Now.AddHours(1);

            // Act & Assert
            schedule.IsValid().Should().BeTrue();
        }

        [Test]
        [TestCase(0, 1, true, false)] // Invalid TrainId
        [TestCase(1, 0, true, false)] // Invalid StationId
        [TestCase(1, 1, false, false)] // No scheduled times
        [TestCase(1, 1, true, true)] // Valid
        public void IsValid_WithDifferentData_ReturnsExpectedResult(int trainId, int stationId, bool hasScheduledTime, bool expected)
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.TrainId = trainId;
            schedule.StationId = stationId;
            schedule.ScheduledArrival = hasScheduledTime ? DateTime.Now.AddHours(1) : null;
            schedule.ScheduledDeparture = null;

            // Act & Assert
            schedule.IsValid().Should().Be(expected);
        }

        [Test]
        public void GetSummary_WithCompleteData_ReturnsFormattedSummary()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Train = new Train { TrainNumber = "12345", TrainName = "Express" };
            schedule.Platform = new Platform { PlatformNumber = "3" };
            schedule.ScheduledArrival = DateTime.Today.AddHours(10);
            schedule.ScheduledDeparture = DateTime.Today.AddHours(10).AddMinutes(5);
            schedule.DelayMinutes = 15;

            // Act
            var summary = schedule.GetSummary();

            // Assert
            summary.Should().Contain("12345 - Express");
            summary.Should().Contain("Arr: 10:00");
            summary.Should().Contain("Dep: 10:05");
            summary.Should().Contain("Platform: 3");
            summary.Should().Contain("Delayed 15min");
        }

        [Test]
        [TestCase(0, "On Time")]
        [TestCase(5, "5 min late")]
        [TestCase(30, "30 min late")]
        [TestCase(60, "1h late")]
        [TestCase(90, "1h 30m late")]
        [TestCase(120, "2h late")]
        public void GetDelayDescription_WithDifferentDelays_ReturnsExpectedDescription(int delayMinutes, string expected)
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.DelayMinutes = delayMinutes;

            // Act
            var result = schedule.GetDelayDescription();

            // Assert
            result.Should().Be(expected);
        }

        [Test]
        public void ToString_ReturnsDisplayInfo()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Train = new Train { TrainNumber = "12345" };
            schedule.Station = new Station { Name = "Central" };
            schedule.Status = ScheduleStatus.OnTime;

            // Act
            var result = schedule.ToString();

            // Assert
            result.Should().Be("12345 - Central - OnTime");
        }

        #endregion

        #region Equality Tests

        [Test]
        public void Equals_WithSameId_ReturnsTrue()
        {
            // Arrange
            var schedule1 = _fixture.Create<Schedule>();
            schedule1.Id = 1;

            var schedule2 = _fixture.Create<Schedule>();
            schedule2.Id = 1;

            // Act & Assert
            schedule1.Equals(schedule2).Should().BeTrue();
        }

        [Test]
        public void Equals_WithDifferentId_ReturnsFalse()
        {
            // Arrange
            var schedule1 = _fixture.Create<Schedule>();
            schedule1.Id = 1;

            var schedule2 = _fixture.Create<Schedule>();
            schedule2.Id = 2;

            // Act & Assert
            schedule1.Equals(schedule2).Should().BeFalse();
        }

        [Test]
        public void GetHashCode_WithSameId_ReturnsSameHashCode()
        {
            // Arrange
            var schedule1 = _fixture.Create<Schedule>();
            schedule1.Id = 1;

            var schedule2 = _fixture.Create<Schedule>();
            schedule2.Id = 1;

            // Act & Assert
            schedule1.GetHashCode().Should().Be(schedule2.GetHashCode());
        }

        #endregion

        #region Edge Cases and Real-time Scenarios

        [Test]
        public void Schedule_RealTimeScenario_HandlesCorrectly()
        {
            // Arrange - Simulate a real train schedule scenario
            var schedule = new Schedule
            {
                TrainId = 1,
                StationId = 1,
                ScheduledArrival = DateTime.Now.AddMinutes(-10), // Should have arrived 10 minutes ago
                ScheduledDeparture = DateTime.Now.AddMinutes(5), // Should depart in 5 minutes
                Status = ScheduleStatus.Scheduled
            };

            // Act - Update status based on current time
            schedule.UpdateStatus();

            // Assert - Should be marked as delayed
            schedule.Status.Should().Be(ScheduleStatus.Delayed);
            schedule.DelayMinutes.Should().BeGreaterThan(5);
        }

        [Test]
        public void Schedule_WithNullNavigationProperties_ShouldHandleGracefully()
        {
            // Arrange
            var schedule = _fixture.Create<Schedule>();
            schedule.Train = null!;
            schedule.Station = null!;
            schedule.Platform = null!;

            // Act & Assert - Should not throw exceptions
            schedule.DisplayInfo.Should().Contain(" - ");
            schedule.PlatformDisplay.Should().Be("TBA");
        }

        #endregion
    }
}
