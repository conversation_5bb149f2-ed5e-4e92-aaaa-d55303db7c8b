E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.csproj.AssemblyReference.cache
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.GeneratedMSBuildEditorConfig.editorconfig
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.AssemblyInfoInputs.cache
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.AssemblyInfo.cs
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.csproj.CoreCompileInputs.cache
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.sourcelink.json
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.Forms.MainForm.resources
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.csproj.GenerateResource.cache
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\IPIS.WindowsApp.xml
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\appsettings.json
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\IPIS.WindowsApp.exe
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\IPIS.WindowsApp.deps.json
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\IPIS.WindowsApp.runtimeconfig.json
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\IPIS.WindowsApp.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\IPIS.WindowsApp.pdb
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\AutoMapper.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\BCrypt.Net-Next.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\CsvHelper.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\EPPlus.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\EPPlus.Interfaces.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\EPPlus.System.Drawing.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\FluentValidation.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\FluentValidation.DependencyInjectionExtensions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Humanizer.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Data.Sqlite.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Design.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Relational.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Sqlite.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Memory.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Binder.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.CommandLine.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.UserSecrets.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyModel.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Hosting.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Hosting.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Hosting.WindowsServices.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Http.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Http.Polly.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Configuration.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Console.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Debug.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.EventLog.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.EventSource.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.ConfigurationExtensions.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.Asio.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.Core.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.Midi.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.Wasapi.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.WinForms.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\NAudio.WinMM.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Polly.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Polly.Extensions.Http.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Serilog.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Serilog.Extensions.Logging.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Serilog.Settings.Configuration.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Serilog.Sinks.Console.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\Serilog.Sinks.File.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\SQLitePCLRaw.batteries_v2.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\SQLitePCLRaw.core.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\System.Diagnostics.DiagnosticSource.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\System.IO.Ports.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\System.ServiceProcess.ServiceController.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\System.Text.Json.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\alpine-arm\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\alpine-arm64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\alpine-x64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win-arm\native\e_sqlite3.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win-x64\native\e_sqlite3.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win-x86\native\e_sqlite3.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.Messages.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\unix\lib\net6.0\System.IO.Ports.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.IO.Ports.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.ServiceProcess.ServiceController.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.Win.08DEA438.Up2Date
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\refint\IPIS.WindowsApp.dll
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.pdb
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\IPIS.WindowsApp.genruntimeconfig.cache
E:\_work\Rail\rail\ipis v1\IPIS.WindowsApp\obj\Debug\net6.0-windows\ref\IPIS.WindowsApp.dll
