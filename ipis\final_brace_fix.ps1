# PowerShell script to add missing namespace closing braces

Write-Host "=== FINAL BRACE FIX ==="

# Get all C# files
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    if ([string]::IsNullOrEmpty($content)) { continue }
    
    # Count opening and closing braces
    $openBraces = ($content -split '\{').Count - 1
    $closeBraces = ($content -split '\}').Count - 1
    
    # Check if file has namespace declaration
    $hasNamespace = $content -match 'namespace\s+(ipis|ipis\.My)'
    
    if ($hasNamespace -and $openBraces -gt $closeBraces) {
        $missingBraces = $openBraces - $closeBraces
        Write-Host "  - Missing $missingBraces closing braces"
        
        # Add missing closing braces
        for ($i = 0; $i -lt $missingBraces; $i++) {
            $content += "`n}"
        }
        
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Fixed!"
    }
}

Write-Host "Final brace fix complete!"
