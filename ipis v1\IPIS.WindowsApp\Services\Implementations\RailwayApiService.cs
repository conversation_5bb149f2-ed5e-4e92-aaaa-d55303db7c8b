using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using IPIS.WindowsApp.Configuration;
using IPIS.WindowsApp.Models.DTOs.Railway;
using IPIS.WindowsApp.Services.Interfaces;
using Polly;
using Polly.CircuitBreaker;

namespace IPIS.WindowsApp.Services.Implementations
{
    /// <summary>
    /// Implementation of Railway API service for external API communication
    /// Provides comprehensive integration with Railway APIs for real-time data synchronization
    /// </summary>
    public class RailwayApiService : IRailwayApiService, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<RailwayApiService> _logger;
        private readonly RailwayApiConfiguration _config;
        private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
        private readonly JsonSerializerOptions _jsonOptions;
        private bool _disposed = false;

        public RailwayApiService(
            HttpClient httpClient,
            ILogger<RailwayApiService> logger,
            IOptions<RailwayApiConfiguration> config)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));

            ConfigureHttpClient();
            _retryPolicy = CreateRetryPolicy();
            _jsonOptions = CreateJsonOptions();

            _logger.LogInformation("Railway API Service initialized with base URL: {BaseUrl}", _config.BaseUrl);
        }

        #region Events

        public event EventHandler<ApiDataReceivedEventArgs>? DataReceived;
        public event EventHandler<ApiErrorEventArgs>? ApiError;
        public event EventHandler<ApiConnectionStatusEventArgs>? ConnectionStatusChanged;

        #endregion

        #region Schedule Operations

        public async Task<List<TrainScheduleDto>> GetTrainSchedulesAsync(string stationCode, DateTime date, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting train schedules for station {StationCode} on {Date}", stationCode, date);

                var endpoint = $"{_config.Endpoints.Schedules}?stationCode={stationCode}&date={date:yyyy-MM-dd}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var schedules = await response.Content.ReadFromJsonAsync<List<TrainScheduleDto>>(_jsonOptions, cancellationToken) ?? new List<TrainScheduleDto>();

                    OnDataReceived("TrainSchedules", schedules.Count, schedules);
                    _logger.LogInformation("Retrieved {Count} train schedules for station {StationCode}", schedules.Count, stationCode);

                    return schedules;
                }

                await HandleApiError(response, "GetTrainSchedules");
                return new List<TrainScheduleDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting train schedules for station {StationCode}", stationCode);
                OnApiError("GetTrainSchedules", ex);
                throw;
            }
        }

        public async Task<Dictionary<string, List<TrainScheduleDto>>> GetTrainSchedulesAsync(List<string> stationCodes, DateTime date, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting train schedules for {Count} stations on {Date}", stationCodes.Count, date);

                var result = new Dictionary<string, List<TrainScheduleDto>>();
                var tasks = stationCodes.Select(async stationCode =>
                {
                    var schedules = await GetTrainSchedulesAsync(stationCode, date, cancellationToken);
                    return new { StationCode = stationCode, Schedules = schedules };
                });

                var results = await Task.WhenAll(tasks);
                foreach (var item in results)
                {
                    result[item.StationCode] = item.Schedules;
                }

                _logger.LogInformation("Retrieved schedules for {Count} stations", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting train schedules for multiple stations");
                OnApiError("GetTrainSchedulesMultiple", ex);
                throw;
            }
        }

        public async Task<TrainScheduleDto?> GetTrainScheduleAsync(string trainNumber, string stationCode, DateTime date, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting schedule for train {TrainNumber} at station {StationCode} on {Date}", trainNumber, stationCode, date);

                var endpoint = $"{_config.Endpoints.Schedules}/{trainNumber}?stationCode={stationCode}&date={date:yyyy-MM-dd}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var schedule = await response.Content.ReadFromJsonAsync<TrainScheduleDto>(_jsonOptions, cancellationToken);

                    if (schedule != null)
                    {
                        OnDataReceived("TrainSchedule", 1, schedule);
                        _logger.LogInformation("Retrieved schedule for train {TrainNumber} at station {StationCode}", trainNumber, stationCode);
                    }

                    return schedule;
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning("Schedule not found for train {TrainNumber} at station {StationCode}", trainNumber, stationCode);
                    return null;
                }

                await HandleApiError(response, "GetTrainSchedule");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting schedule for train {TrainNumber} at station {StationCode}", trainNumber, stationCode);
                OnApiError("GetTrainSchedule", ex);
                throw;
            }
        }

        #endregion

        #region Train Status Operations

        public async Task<TrainStatusDto?> GetTrainStatusAsync(string trainNumber, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting status for train {TrainNumber}", trainNumber);

                var endpoint = $"{_config.Endpoints.TrainStatus}/{trainNumber}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var status = await response.Content.ReadFromJsonAsync<TrainStatusDto>(_jsonOptions, cancellationToken);

                    if (status != null)
                    {
                        OnDataReceived("TrainStatus", 1, status);
                        _logger.LogInformation("Retrieved status for train {TrainNumber}: {Status}", trainNumber, status.CurrentStatus);
                    }

                    return status;
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning("Status not found for train {TrainNumber}", trainNumber);
                    return null;
                }

                await HandleApiError(response, "GetTrainStatus");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting status for train {TrainNumber}", trainNumber);
                OnApiError("GetTrainStatus", ex);
                throw;
            }
        }

        public async Task<Dictionary<string, TrainStatusDto>> GetTrainStatusesAsync(List<string> trainNumbers, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting status for {Count} trains", trainNumbers.Count);

                var result = new Dictionary<string, TrainStatusDto>();
                var semaphore = new SemaphoreSlim(_config.MaxConcurrentRequests, _config.MaxConcurrentRequests);

                var tasks = trainNumbers.Select(async trainNumber =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        var status = await GetTrainStatusAsync(trainNumber, cancellationToken);
                        if (status != null)
                        {
                            lock (result)
                            {
                                result[trainNumber] = status;
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(tasks);

                _logger.LogInformation("Retrieved status for {Count} trains", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting status for multiple trains");
                OnApiError("GetTrainStatusesMultiple", ex);
                throw;
            }
        }

        public async Task<List<TrainStatusDto>> GetLiveTrainPositionsAsync(string regionCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting live train positions for region {RegionCode}", regionCode);

                var endpoint = $"{_config.Endpoints.LivePositions}?region={regionCode}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var positions = await response.Content.ReadFromJsonAsync<List<TrainStatusDto>>(_jsonOptions, cancellationToken) ?? new List<TrainStatusDto>();

                    OnDataReceived("LiveTrainPositions", positions.Count, positions);
                    _logger.LogInformation("Retrieved {Count} live train positions for region {RegionCode}", positions.Count, regionCode);

                    return positions;
                }

                await HandleApiError(response, "GetLiveTrainPositions");
                return new List<TrainStatusDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting live train positions for region {RegionCode}", regionCode);
                OnApiError("GetLiveTrainPositions", ex);
                throw;
            }
        }

        #endregion

        #region Delay Information Operations

        public async Task<List<DelayInfoDto>> GetDelayUpdatesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting delay updates");

                var endpoint = _config.Endpoints.Delays;
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var delays = await response.Content.ReadFromJsonAsync<List<DelayInfoDto>>(_jsonOptions, cancellationToken) ?? new List<DelayInfoDto>();

                    OnDataReceived("DelayUpdates", delays.Count, delays);
                    _logger.LogInformation("Retrieved {Count} delay updates", delays.Count);

                    return delays;
                }

                await HandleApiError(response, "GetDelayUpdates");
                return new List<DelayInfoDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting delay updates");
                OnApiError("GetDelayUpdates", ex);
                throw;
            }
        }

        public async Task<List<DelayInfoDto>> GetStationDelayUpdatesAsync(string stationCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting delay updates for station {StationCode}", stationCode);

                var endpoint = $"{_config.Endpoints.Delays}?stationCode={stationCode}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var delays = await response.Content.ReadFromJsonAsync<List<DelayInfoDto>>(_jsonOptions, cancellationToken) ?? new List<DelayInfoDto>();

                    OnDataReceived("StationDelayUpdates", delays.Count, delays);
                    _logger.LogInformation("Retrieved {Count} delay updates for station {StationCode}", delays.Count, stationCode);

                    return delays;
                }

                await HandleApiError(response, "GetStationDelayUpdates");
                return new List<DelayInfoDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting delay updates for station {StationCode}", stationCode);
                OnApiError("GetStationDelayUpdates", ex);
                throw;
            }
        }

        public async Task<List<DelayInfoDto>> GetTrainDelayUpdatesAsync(string trainNumber, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting delay updates for train {TrainNumber}", trainNumber);

                var endpoint = $"{_config.Endpoints.Delays}?trainNumber={trainNumber}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var delays = await response.Content.ReadFromJsonAsync<List<DelayInfoDto>>(_jsonOptions, cancellationToken) ?? new List<DelayInfoDto>();

                    OnDataReceived("TrainDelayUpdates", delays.Count, delays);
                    _logger.LogInformation("Retrieved {Count} delay updates for train {TrainNumber}", delays.Count, trainNumber);

                    return delays;
                }

                await HandleApiError(response, "GetTrainDelayUpdates");
                return new List<DelayInfoDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting delay updates for train {TrainNumber}", trainNumber);
                OnApiError("GetTrainDelayUpdates", ex);
                throw;
            }
        }

        #endregion

        #region Platform Assignment Operations

        public async Task<List<PlatformAssignmentDto>> GetPlatformAssignmentsAsync(string stationCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting platform assignments for station {StationCode}", stationCode);

                var endpoint = $"{_config.Endpoints.PlatformAssignments}?stationCode={stationCode}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var assignments = await response.Content.ReadFromJsonAsync<List<PlatformAssignmentDto>>(_jsonOptions, cancellationToken) ?? new List<PlatformAssignmentDto>();

                    OnDataReceived("PlatformAssignments", assignments.Count, assignments);
                    _logger.LogInformation("Retrieved {Count} platform assignments for station {StationCode}", assignments.Count, stationCode);

                    return assignments;
                }

                await HandleApiError(response, "GetPlatformAssignments");
                return new List<PlatformAssignmentDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting platform assignments for station {StationCode}", stationCode);
                OnApiError("GetPlatformAssignments", ex);
                throw;
            }
        }

        public async Task<PlatformAssignmentDto?> GetTrainPlatformAssignmentAsync(string trainNumber, string stationCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting platform assignment for train {TrainNumber} at station {StationCode}", trainNumber, stationCode);

                var endpoint = $"{_config.Endpoints.PlatformAssignments}/{trainNumber}?stationCode={stationCode}";
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var assignment = await response.Content.ReadFromJsonAsync<PlatformAssignmentDto>(_jsonOptions, cancellationToken);

                    if (assignment != null)
                    {
                        OnDataReceived("PlatformAssignment", 1, assignment);
                        _logger.LogInformation("Retrieved platform assignment for train {TrainNumber} at station {StationCode}: Platform {Platform}",
                            trainNumber, stationCode, assignment.PlatformNumber);
                    }

                    return assignment;
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning("Platform assignment not found for train {TrainNumber} at station {StationCode}", trainNumber, stationCode);
                    return null;
                }

                await HandleApiError(response, "GetTrainPlatformAssignment");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting platform assignment for train {TrainNumber} at station {StationCode}", trainNumber, stationCode);
                OnApiError("GetTrainPlatformAssignment", ex);
                throw;
            }
        }

        #endregion

        #region Synchronization Operations

        public async Task<bool> SyncScheduleDataAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting schedule data synchronization");

                // This would typically involve getting all stations and syncing their schedules
                // For now, we'll implement a basic version
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);

                // Get schedules for today and tomorrow
                var schedulesSynced = 0;
                var errors = new List<string>();

                // This is a simplified implementation - in reality, you'd get station codes from your database
                var stationCodes = new List<string> { "DEL", "BOM", "MAA", "CCU", "HYB" }; // Example station codes

                foreach (var stationCode in stationCodes)
                {
                    try
                    {
                        var todaySchedules = await GetTrainSchedulesAsync(stationCode, today, cancellationToken);
                        var tomorrowSchedules = await GetTrainSchedulesAsync(stationCode, tomorrow, cancellationToken);

                        schedulesSynced += todaySchedules.Count + tomorrowSchedules.Count;
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Failed to sync schedules for station {stationCode}: {ex.Message}");
                        _logger.LogError(ex, "Error syncing schedules for station {StationCode}", stationCode);
                    }
                }

                var isSuccessful = errors.Count == 0;
                _logger.LogInformation("Schedule synchronization completed. Synced: {Count}, Errors: {ErrorCount}",
                    schedulesSynced, errors.Count);

                return isSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during schedule data synchronization");
                OnApiError("SyncScheduleData", ex);
                return false;
            }
        }

        public async Task<bool> SyncTrainStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting train status synchronization");

                // Get all active trains and sync their status
                // This is a simplified implementation
                var trainNumbers = new List<string> { "12345", "12346", "12347", "12348", "12349" }; // Example train numbers

                var statusesSynced = 0;
                var errors = new List<string>();

                var trainStatuses = await GetTrainStatusesAsync(trainNumbers, cancellationToken);
                statusesSynced = trainStatuses.Count;

                var isSuccessful = statusesSynced > 0;
                _logger.LogInformation("Train status synchronization completed. Synced: {Count}", statusesSynced);

                return isSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during train status synchronization");
                OnApiError("SyncTrainStatus", ex);
                return false;
            }
        }

        public async Task<bool> SyncDelayInformationAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting delay information synchronization");

                var delays = await GetDelayUpdatesAsync(cancellationToken);

                _logger.LogInformation("Delay information synchronization completed. Synced: {Count}", delays.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during delay information synchronization");
                OnApiError("SyncDelayInformation", ex);
                return false;
            }
        }

        public async Task<bool> SyncPlatformAssignmentsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting platform assignments synchronization");

                var stationCodes = new List<string> { "DEL", "BOM", "MAA", "CCU", "HYB" }; // Example station codes
                var assignmentsSynced = 0;

                foreach (var stationCode in stationCodes)
                {
                    try
                    {
                        var assignments = await GetPlatformAssignmentsAsync(stationCode, cancellationToken);
                        assignmentsSynced += assignments.Count;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error syncing platform assignments for station {StationCode}", stationCode);
                    }
                }

                _logger.LogInformation("Platform assignments synchronization completed. Synced: {Count}", assignmentsSynced);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during platform assignments synchronization");
                OnApiError("SyncPlatformAssignments", ex);
                return false;
            }
        }

        #endregion

        #region Health and Monitoring Operations

        public async Task<ApiHealthDto> GetApiHealthAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Checking API health");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var endpoint = _config.Endpoints.Health;
                var response = await ExecuteApiRequestAsync(endpoint, cancellationToken);
                stopwatch.Stop();

                var health = new ApiHealthDto
                {
                    CheckTime = DateTime.UtcNow,
                    ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds,
                    HttpStatusCode = (int)response.StatusCode,
                    Endpoint = endpoint
                };

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        var healthData = await response.Content.ReadFromJsonAsync<Dictionary<string, object>>(_jsonOptions, cancellationToken);
                        if (healthData != null)
                        {
                            health.AdditionalInfo = healthData;
                            if (healthData.TryGetValue("version", out var version))
                                health.Version = version?.ToString() ?? "";
                            if (healthData.TryGetValue("status", out var status))
                                health.Status = status?.ToString() ?? "Unknown";
                        }
                    }
                    catch
                    {
                        // If we can't parse the health response, just use basic info
                        health.Status = "Healthy";
                    }

                    health.IsHealthy = true;
                    _logger.LogInformation("API health check successful. Response time: {ResponseTime}ms", health.ResponseTimeMs);
                }
                else
                {
                    health.IsHealthy = false;
                    health.Status = $"Unhealthy - {response.StatusCode}";
                    health.ErrorMessage = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("API health check failed. Status: {Status}, Response time: {ResponseTime}ms",
                        response.StatusCode, health.ResponseTimeMs);
                }

                return health;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during API health check");
                OnApiError("GetApiHealth", ex);

                return new ApiHealthDto
                {
                    IsHealthy = false,
                    Status = "Error",
                    CheckTime = DateTime.UtcNow,
                    ErrorMessage = ex.Message,
                    ResponseTimeMs = 0
                };
            }
        }

        public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var health = await GetApiHealthAsync(cancellationToken);
                return health.IsHealthy;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GetApiVersionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var health = await GetApiHealthAsync(cancellationToken);
                return health.Version;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API version");
                return "Unknown";
            }
        }

        #endregion

        #region Batch Operations

        public async Task<BatchUpdateResult> PerformBatchUpdateAsync(
            List<string> stationCodes,
            bool includeSchedules = true,
            bool includeStatus = true,
            bool includeDelays = true,
            bool includePlatforms = true,
            CancellationToken cancellationToken = default)
        {
            var result = new BatchUpdateResult
            {
                CompletedAt = DateTime.UtcNow
            };

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Starting batch update for {Count} stations", stationCodes.Count);

                var tasks = new List<Task>();
                var recordCounts = new Dictionary<string, int>();

                if (includeSchedules)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var success = await SyncScheduleDataAsync(cancellationToken);
                            if (success) recordCounts["schedules"] = 1;
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"Schedule sync failed: {ex.Message}");
                        }
                    }));
                }

                if (includeStatus)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var success = await SyncTrainStatusAsync(cancellationToken);
                            if (success) recordCounts["status"] = 1;
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"Status sync failed: {ex.Message}");
                        }
                    }));
                }

                if (includeDelays)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var success = await SyncDelayInformationAsync(cancellationToken);
                            if (success) recordCounts["delays"] = 1;
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"Delay sync failed: {ex.Message}");
                        }
                    }));
                }

                if (includePlatforms)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var success = await SyncPlatformAssignmentsAsync(cancellationToken);
                            if (success) recordCounts["platforms"] = 1;
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"Platform sync failed: {ex.Message}");
                        }
                    }));
                }

                await Task.WhenAll(tasks);

                result.TotalRecordsProcessed = recordCounts.Values.Sum();
                result.RecordsUpdated = result.TotalRecordsProcessed;
                result.IsSuccessful = result.Errors.Count == 0;

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("Batch update completed. Success: {Success}, Records: {Records}, Duration: {Duration}ms",
                    result.IsSuccessful, result.TotalRecordsProcessed, result.Duration.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.IsSuccessful = false;
                result.Errors.Add($"Batch update failed: {ex.Message}");

                _logger.LogError(ex, "Error during batch update");
                OnApiError("PerformBatchUpdate", ex);

                return result;
            }
        }

        #endregion

        #region Private Helper Methods

        private void ConfigureHttpClient()
        {
            _httpClient.BaseAddress = new Uri(_config.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);

            // Add authentication header
            switch (_config.Authentication.Type.ToLowerInvariant())
            {
                case "apikey":
                    _httpClient.DefaultRequestHeaders.Add(_config.Authentication.ApiKeyHeader, _config.ApiKey);
                    break;
                case "bearer":
                    if (!string.IsNullOrWhiteSpace(_config.Authentication.BearerToken))
                        _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.Authentication.BearerToken);
                    break;
            }

            // Add additional headers
            foreach (var header in _config.AdditionalHeaders)
            {
                _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
            }

            // Add user agent
            _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd(_config.UserAgent);
        }

        private IAsyncPolicy<HttpResponseMessage> CreateRetryPolicy()
        {
            var retryPolicy = Policy
                .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
                .Or<HttpRequestException>()
                .Or<TaskCanceledException>()
                .WaitAndRetryAsync(
                    _config.RetryAttempts,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(_config.RetryDelaySeconds, retryAttempt)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Retry attempt {RetryCount} for {Operation} after {Delay}ms",
                            retryCount, context.OperationKey, timespan.TotalMilliseconds);
                    });

            if (_config.EnableCircuitBreaker)
            {
                var circuitBreakerPolicy = Policy
                    .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
                    .CircuitBreakerAsync(
                        _config.CircuitBreakerFailureThreshold,
                        TimeSpan.FromMinutes(_config.CircuitBreakerTimeoutMinutes),
                        onBreak: (result, timespan) =>
                        {
                            _logger.LogError("Circuit breaker opened for {Duration}ms", timespan.TotalMilliseconds);
                            OnConnectionStatusChanged(false, "Circuit breaker opened");
                        },
                        onReset: () =>
                        {
                            _logger.LogInformation("Circuit breaker reset");
                            OnConnectionStatusChanged(true, "Circuit breaker reset");
                        });

                return Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);
            }

            return retryPolicy;
        }

        private static JsonSerializerOptions CreateJsonOptions()
        {
            return new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            };
        }

        private async Task<HttpResponseMessage> ExecuteApiRequestAsync(string endpoint, CancellationToken cancellationToken)
        {
            var url = _config.GetApiUrl(endpoint);

            if (_config.EnableLogging)
            {
                _logger.LogDebug("Making API request to: {Url}", _config.LogSensitiveData ? url : endpoint);
            }

            var context = new Context(endpoint);
            return await _retryPolicy.ExecuteAsync(async (ctx) =>
            {
                return await _httpClient.GetAsync(url, cancellationToken);
            }, context);
        }

        private async Task HandleApiError(HttpResponseMessage response, string operation)
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            var errorMessage = $"API request failed with status {response.StatusCode}: {errorContent}";

            _logger.LogError("API Error in {Operation}: {ErrorMessage}", operation, errorMessage);
            OnApiError(operation, new HttpRequestException(errorMessage));
        }

        private void OnDataReceived(string dataType, int recordCount, object data)
        {
            DataReceived?.Invoke(this, new ApiDataReceivedEventArgs
            {
                DataType = dataType,
                RecordCount = recordCount,
                ReceivedAt = DateTime.UtcNow,
                Data = data
            });
        }

        private void OnApiError(string operation, Exception exception)
        {
            ApiError?.Invoke(this, new ApiErrorEventArgs
            {
                ErrorMessage = exception.Message,
                Exception = exception,
                Operation = operation,
                OccurredAt = DateTime.UtcNow
            });
        }

        private void OnConnectionStatusChanged(bool isConnected, string status)
        {
            ConnectionStatusChanged?.Invoke(this, new ApiConnectionStatusEventArgs
            {
                IsConnected = isConnected,
                Status = status,
                StatusChangedAt = DateTime.UtcNow
            });
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _httpClient?.Dispose();
                _disposed = true;
            }
        }

        #endregion
    }
}
