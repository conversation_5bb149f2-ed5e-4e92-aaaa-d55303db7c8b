using Microsoft.Extensions.Logging;
using IPIS.WindowsApp.Models;
using IPIS.WindowsApp.Services.Interfaces;
using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Forms
{
    /// <summary>
    /// Form for adding new railway stations to the IPIS system
    /// Provides comprehensive station configuration and validation
    /// </summary>
    public partial class AddStationForm : Form
    {
        private readonly IStationService _stationService;
        private readonly ILogger<AddStationForm> _logger;
        private Station? _station;

        // Form controls
        private TextBox txtStationCode = null!;
        private TextBox txtStationName = null!;
        private TextBox txtLocation = null!;
        private CheckBox chkIsActive = null!;
        private Button btnSave = null!;
        private Button btnCancel = null!;

        /// <summary>
        /// Initializes a new instance of the AddStationForm
        /// </summary>
        /// <param name="stationService">Station service for data operations</param>
        /// <param name="logger">Logger for error tracking</param>
        public AddStationForm(IStationService stationService, ILogger<AddStationForm> logger)
        {
            _stationService = stationService ?? throw new ArgumentNullException(nameof(stationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            InitializeFormData();

            _logger.LogInformation("AddStationForm initialized");
        }

        /// <summary>
        /// Initializes a new instance for editing an existing station
        /// </summary>
        /// <param name="stationService">Station service for data operations</param>
        /// <param name="logger">Logger for error tracking</param>
        /// <param name="station">Station to edit</param>
        public AddStationForm(IStationService stationService, ILogger<AddStationForm> logger, Station station)
            : this(stationService, logger)
        {
            _station = station ?? throw new ArgumentNullException(nameof(station));
            LoadStationData();

            Text = $"Edit Station - {station.Name}";
            _logger.LogInformation("AddStationForm initialized for editing station {StationCode}", station.Code);
        }

        /// <summary>
        /// Gets the station that was created or edited
        /// </summary>
        public Station? Station => _station;

        /// <summary>
        /// Initializes the form components and layout
        /// </summary>
        private void InitializeComponent()
        {
            SuspendLayout();

            // Form properties
            Text = "Add New Station";
            Size = new Size(450, 300);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            ShowInTaskbar = false;
            AutoScaleMode = AutoScaleMode.Font;
            Font = new Font("Segoe UI", 9F);
            BackColor = SystemColors.Control;

            // Create and configure controls
            CreateFormControls();
            LayoutControls();
            AttachEventHandlers();

            ResumeLayout(false);
            PerformLayout();
        }

        /// <summary>
        /// Creates all form controls
        /// </summary>
        private void CreateFormControls()
        {
            // Text boxes with better styling
            txtStationCode = new TextBox
            {
                MaxLength = 10,
                CharacterCasing = CharacterCasing.Upper,
                Font = new Font("Segoe UI", 9F),
                BorderStyle = BorderStyle.FixedSingle
            };

            txtStationName = new TextBox
            {
                MaxLength = 100,
                Font = new Font("Segoe UI", 9F),
                BorderStyle = BorderStyle.FixedSingle
            };

            txtLocation = new TextBox
            {
                MaxLength = 200,
                Font = new Font("Segoe UI", 9F),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Check boxes
            chkIsActive = new CheckBox
            {
                Checked = true,
                Font = new Font("Segoe UI", 9F),
                FlatStyle = FlatStyle.System
            };

            // Buttons with better styling
            btnSave = new Button
            {
                Text = "Save",
                DialogResult = DialogResult.OK,
                Font = new Font("Segoe UI", 9F),
                FlatStyle = FlatStyle.System,
                UseVisualStyleBackColor = true
            };

            btnCancel = new Button
            {
                Text = "Cancel",
                DialogResult = DialogResult.Cancel,
                Font = new Font("Segoe UI", 9F),
                FlatStyle = FlatStyle.System,
                UseVisualStyleBackColor = true
            };
        }

        /// <summary>
        /// Layouts all controls on the form
        /// </summary>
        private void LayoutControls()
        {
            var y = 20;
            const int labelWidth = 120;
            const int controlWidth = 250;
            const int spacing = 35;
            const int leftMargin = 20;
            const int controlLeft = leftMargin + labelWidth + 10;

            // Station Code
            var lblStationCode = new Label
            {
                Text = "Station Code *:",
                Location = new Point(leftMargin, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9F),
                ForeColor = SystemColors.ControlText
            };
            Controls.Add(lblStationCode);

            txtStationCode.Location = new Point(controlLeft, y);
            txtStationCode.Size = new Size(controlWidth, 23);
            Controls.Add(txtStationCode);
            y += spacing;

            // Station Name
            var lblStationName = new Label
            {
                Text = "Station Name *:",
                Location = new Point(leftMargin, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9F),
                ForeColor = SystemColors.ControlText
            };
            Controls.Add(lblStationName);

            txtStationName.Location = new Point(controlLeft, y);
            txtStationName.Size = new Size(controlWidth, 23);
            Controls.Add(txtStationName);
            y += spacing;

            // Location
            var lblLocation = new Label
            {
                Text = "Location:",
                Location = new Point(leftMargin, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 9F),
                ForeColor = SystemColors.ControlText
            };
            Controls.Add(lblLocation);

            txtLocation.Location = new Point(controlLeft, y);
            txtLocation.Size = new Size(controlWidth, 23);
            Controls.Add(txtLocation);
            y += spacing;

            // Is Active checkbox
            chkIsActive.Text = "Station is Active";
            chkIsActive.Location = new Point(leftMargin, y);
            chkIsActive.Size = new Size(150, 23);
            chkIsActive.TextAlign = ContentAlignment.MiddleLeft;
            Controls.Add(chkIsActive);
            y += spacing + 10;

            // Buttons
            btnSave.Location = new Point(controlLeft + controlWidth - 160, y);
            btnSave.Size = new Size(75, 30);
            Controls.Add(btnSave);

            btnCancel.Location = new Point(controlLeft + controlWidth - 75, y);
            btnCancel.Size = new Size(75, 30);
            Controls.Add(btnCancel);
        }

        /// <summary>
        /// Attaches event handlers to form controls
        /// </summary>
        private void AttachEventHandlers()
        {
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            txtStationCode.TextChanged += TxtStationCode_TextChanged;
            Load += AddStationForm_Load;
        }

        /// <summary>
        /// Initializes form data
        /// </summary>
        private void InitializeFormData()
        {
            try
            {
                // No additional initialization needed for simplified form
                _logger.LogInformation("AddStationForm data initialized");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing form data");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Loads existing station data for editing
        /// </summary>
        private void LoadStationData()
        {
            if (_station == null) return;

            try
            {
                txtStationCode.Text = _station.Code;
                txtStationCode.ReadOnly = true; // Don't allow editing station code
                txtStationName.Text = _station.Name;
                txtLocation.Text = _station.Location ?? "";
                chkIsActive.Checked = _station.IsActive;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading station data for {StationCode}", _station.Code);
                MessageBox.Show($"Error loading station data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Validates form input data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        private bool ValidateInput()
        {
            var errors = new List<string>();

            // Required fields
            if (string.IsNullOrWhiteSpace(txtStationCode.Text))
                errors.Add("Station Code is required");
            else if (txtStationCode.Text.Length < 2 || txtStationCode.Text.Length > 10)
                errors.Add("Station Code must be between 2 and 10 characters");

            if (string.IsNullOrWhiteSpace(txtStationName.Text))
                errors.Add("Station Name is required");
            else if (txtStationName.Text.Length > 100)
                errors.Add("Station Name cannot exceed 100 characters");

            // Validate location if provided
            if (!string.IsNullOrWhiteSpace(txtLocation.Text) && txtLocation.Text.Length > 200)
                errors.Add("Location cannot exceed 200 characters");

            if (errors.Any())
            {
                MessageBox.Show($"Please correct the following errors:\n\n{string.Join("\n", errors)}",
                    "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Creates or updates station from form data
        /// </summary>
        /// <returns>Created or updated station</returns>
        private Station CreateStationFromForm()
        {
            var station = _station ?? new Station();

            station.Code = txtStationCode.Text.Trim().ToUpper();
            station.Name = txtStationName.Text.Trim();
            station.Location = string.IsNullOrWhiteSpace(txtLocation.Text) ? null : txtLocation.Text.Trim();
            station.IsActive = chkIsActive.Checked;

            // Set timestamps
            if (_station == null)
            {
                station.CreatedAt = DateTime.UtcNow;
            }
            station.UpdatedAt = DateTime.UtcNow;

            return station;
        }

        #region Event Handlers

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                btnSave.Enabled = false;
                btnSave.Text = "Saving...";

                var station = CreateStationFromForm();

                if (_station == null)
                {
                    // Create new station
                    _station = await _stationService.CreateStationAsync(station);
                    _logger.LogInformation("Created new station {StationCode}", station.Code);
                }
                else
                {
                    // Update existing station
                    _station = await _stationService.UpdateStationAsync(station);
                    _logger.LogInformation("Updated station {StationCode}", station.Code);
                }

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving station");
                MessageBox.Show($"Error saving station: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "Save";
            }
        }

        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void TxtStationCode_TextChanged(object? sender, EventArgs e)
        {
            // Auto-uppercase station code
            if (sender is TextBox textBox)
            {
                var selectionStart = textBox.SelectionStart;
                textBox.Text = textBox.Text.ToUpper();
                textBox.SelectionStart = selectionStart;
            }
        }

        private void AddStationForm_Load(object? sender, EventArgs e)
        {
            // Focus on first input
            txtStationCode.Focus();
        }

        #endregion
    }
}
