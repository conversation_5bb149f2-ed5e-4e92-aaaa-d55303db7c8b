using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Services.Interfaces
{
    /// <summary>
    /// Interface for background schedule synchronization service
    /// Manages continuous synchronization between local system and Railway API
    /// </summary>
    public interface IScheduleSyncService
    {
        #region Synchronization Control

        /// <summary>
        /// Starts the background synchronization process
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the start operation</returns>
        Task StartSynchronizationAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops the background synchronization process
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the stop operation</returns>
        Task StopSynchronizationAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Pauses the synchronization process temporarily
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the pause operation</returns>
        Task PauseSynchronizationAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Resumes the paused synchronization process
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the resume operation</returns>
        Task ResumeSynchronizationAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Status and Monitoring

        /// <summary>
        /// Gets the current synchronization status
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Current sync status information</returns>
        Task<SyncStatusDto> GetSyncStatusAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Indicates if synchronization is currently active
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// Indicates if synchronization is currently paused
        /// </summary>
        bool IsPaused { get; }

        /// <summary>
        /// Gets the last synchronization timestamp
        /// </summary>
        DateTime? LastSyncTime { get; }

        /// <summary>
        /// Gets the next scheduled synchronization timestamp
        /// </summary>
        DateTime? NextSyncTime { get; }

        #endregion

        #region Manual Synchronization

        /// <summary>
        /// Forces a full synchronization of all data
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Sync status after completion</returns>
        Task<SyncStatusDto> ForceFullSyncAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Forces an incremental synchronization
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Sync status after completion</returns>
        Task<SyncStatusDto> ForceIncrementalSyncAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Synchronizes data for specific stations only
        /// </summary>
        /// <param name="stationCodes">Station codes to synchronize</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Sync status after completion</returns>
        Task<SyncStatusDto> SyncStationsAsync(List<string> stationCodes, CancellationToken cancellationToken = default);

        /// <summary>
        /// Synchronizes data for specific trains only
        /// </summary>
        /// <param name="trainNumbers">Train numbers to synchronize</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Sync status after completion</returns>
        Task<SyncStatusDto> SyncTrainsAsync(List<string> trainNumbers, CancellationToken cancellationToken = default);

        #endregion

        #region Configuration

        /// <summary>
        /// Updates the synchronization interval
        /// </summary>
        /// <param name="intervalMinutes">New interval in minutes</param>
        /// <returns>Task representing the update operation</returns>
        Task UpdateSyncIntervalAsync(int intervalMinutes);

        /// <summary>
        /// Enables or disables real-time synchronization
        /// </summary>
        /// <param name="enabled">True to enable, false to disable</param>
        /// <returns>Task representing the update operation</returns>
        Task SetRealTimeSyncAsync(bool enabled);

        /// <summary>
        /// Sets the maximum number of concurrent sync operations
        /// </summary>
        /// <param name="maxConcurrent">Maximum concurrent operations</param>
        /// <returns>Task representing the update operation</returns>
        Task SetMaxConcurrentOperationsAsync(int maxConcurrent);

        #endregion

        #region Statistics and Reporting

        /// <summary>
        /// Gets synchronization statistics for the current day
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Daily sync statistics</returns>
        Task<SyncStatistics> GetDailyStatisticsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets synchronization statistics for a date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Sync statistics for the period</returns>
        Task<SyncStatistics> GetStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the synchronization history
        /// </summary>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of sync history records</returns>
        Task<List<SyncHistoryRecord>> GetSyncHistoryAsync(int limit = 100, CancellationToken cancellationToken = default);

        #endregion

        #region Events

        /// <summary>
        /// Event raised when synchronization starts
        /// </summary>
        event EventHandler<SyncStartedEventArgs>? SyncStarted;

        /// <summary>
        /// Event raised when synchronization completes
        /// </summary>
        event EventHandler<SyncCompletedEventArgs>? SyncCompleted;

        /// <summary>
        /// Event raised when synchronization fails
        /// </summary>
        event EventHandler<SyncFailedEventArgs>? SyncFailed;

        /// <summary>
        /// Event raised during synchronization to report progress
        /// </summary>
        event EventHandler<SyncProgressEventArgs>? SyncProgress;

        /// <summary>
        /// Event raised when sync status changes
        /// </summary>
        event EventHandler<SyncStatusChangedEventArgs>? StatusChanged;

        #endregion
    }

    /// <summary>
    /// Synchronization statistics
    /// </summary>
    public class SyncStatistics
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalSyncOperations { get; set; }
        public int SuccessfulSyncs { get; set; }
        public int FailedSyncs { get; set; }
        public int TotalRecordsProcessed { get; set; }
        public int TotalRecordsUpdated { get; set; }
        public int TotalRecordsAdded { get; set; }
        public int TotalConflictsDetected { get; set; }
        public int TotalConflictsResolved { get; set; }
        public TimeSpan TotalSyncTime { get; set; }
        public TimeSpan AverageSyncTime { get; set; }
        public double SuccessRate => TotalSyncOperations > 0 ? (double)SuccessfulSyncs / TotalSyncOperations * 100 : 0;
    }

    /// <summary>
    /// Synchronization history record
    /// </summary>
    public class SyncHistoryRecord
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string SyncType { get; set; } = string.Empty;
        public bool IsSuccessful { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsAdded { get; set; }
        public int ConflictsDetected { get; set; }
        public int ConflictsResolved { get; set; }
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration => EndTime.HasValue ? EndTime.Value - StartTime : TimeSpan.Zero;
    }

    #region Event Arguments

    /// <summary>
    /// Event arguments for sync started events
    /// </summary>
    public class SyncStartedEventArgs : EventArgs
    {
        public string SyncType { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public List<string>? StationCodes { get; set; }
        public List<string>? TrainNumbers { get; set; }
    }

    /// <summary>
    /// Event arguments for sync completed events
    /// </summary>
    public class SyncCompletedEventArgs : EventArgs
    {
        public string SyncType { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool IsSuccessful { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsAdded { get; set; }
        public int ConflictsDetected { get; set; }
        public int ConflictsResolved { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Event arguments for sync failed events
    /// </summary>
    public class SyncFailedEventArgs : EventArgs
    {
        public string SyncType { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime FailTime { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public int RetryAttempt { get; set; }
        public bool WillRetry { get; set; }
    }

    /// <summary>
    /// Event arguments for sync progress events
    /// </summary>
    public class SyncProgressEventArgs : EventArgs
    {
        public string SyncType { get; set; } = string.Empty;
        public int ProgressPercentage { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public int RecordsProcessed { get; set; }
        public int TotalRecords { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    /// <summary>
    /// Event arguments for sync status changed events
    /// </summary>
    public class SyncStatusChangedEventArgs : EventArgs
    {
        public string PreviousStatus { get; set; } = string.Empty;
        public string NewStatus { get; set; } = string.Empty;
        public DateTime StatusChangedAt { get; set; }
        public string? Reason { get; set; }
    }

    #endregion
}
