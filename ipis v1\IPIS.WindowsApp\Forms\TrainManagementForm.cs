using Microsoft.Extensions.Logging;
using IPIS.WindowsApp.Services.Interfaces;
using IPIS.WindowsApp.Models.DTOs.Railway;

namespace IPIS.WindowsApp.Forms
{
    /// <summary>
    /// Form for managing trains and Railway API integration
    /// </summary>
    public partial class TrainManagementForm : Form
    {
        private readonly IRailwayApiService _railwayApiService;
        private readonly IAutoRefreshService _autoRefreshService;
        private readonly ILogger<TrainManagementForm> _logger;

        /// <summary>
        /// Initializes a new instance of the TrainManagementForm class
        /// </summary>
        public TrainManagementForm(
            IRailwayApiService railwayApiService,
            IAutoRefreshService autoRefreshService,
            ILogger<TrainManagementForm> logger)
        {
            _railwayApiService = railwayApiService ?? throw new ArgumentNullException(nameof(railwayApiService));
            _autoRefreshService = autoRefreshService ?? throw new ArgumentNullException(nameof(autoRefreshService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            InitializeFormAsync();
        }

        /// <summary>
        /// Initializes the form asynchronously
        /// </summary>
        private async void InitializeFormAsync()
        {
            try
            {
                _logger.LogInformation("Initializing train management form");

                // Set up event handlers for Railway API service
                _railwayApiService.DataReceived += OnApiDataReceived;
                _railwayApiService.ApiError += OnApiError;
                _railwayApiService.ConnectionStatusChanged += OnConnectionStatusChanged;

                // Set up event handlers for Auto-refresh service
                _autoRefreshService.DataUpdated += OnAutoRefreshDataUpdated;
                _autoRefreshService.RefreshError += OnAutoRefreshError;
                _autoRefreshService.StatusChanged += OnAutoRefreshStatusChanged;

                await LoadInitialDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing train management form");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Loads initial data for the form
        /// </summary>
        private async Task LoadInitialDataAsync()
        {
            try
            {
                statusLabel.Text = "Loading train data...";

                // Test API connection
                var isConnected = await _railwayApiService.TestConnectionAsync();
                connectionStatusLabel.Text = isConnected ? "Connected" : "Disconnected";
                connectionStatusLabel.ForeColor = isConnected ? Color.Green : Color.Red;

                // Get auto-refresh statistics
                var stats = await _autoRefreshService.GetRefreshStatsAsync();
                refreshStatsLabel.Text = $"Active Configs: {stats.ActiveConfigurations}, " +
                                       $"Total Failures: {stats.TotalFailures}, " +
                                       $"Status: {stats.ServiceStatus}";

                statusLabel.Text = "Ready";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading initial data");
                statusLabel.Text = "Error loading data";
                MessageBox.Show($"Error loading data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Get Train Status button click
        /// </summary>
        private async void getTrainStatusButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(trainNumberTextBox.Text))
            {
                MessageBox.Show("Please enter a train number.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                statusLabel.Text = "Getting train status...";
                var trainNumber = trainNumberTextBox.Text.Trim();

                var status = await _railwayApiService.GetLiveTrainStatusAsync(trainNumber);
                if (status != null)
                {
                    trainStatusTextBox.Text = $"Train: {status.TrainNumber} - {status.TrainName}\n" +
                                            $"Status: {status.CurrentStatus}\n" +
                                            $"Current Location: {status.LocationDescription}\n" +
                                            $"Delay: {status.DelayDescription}\n" +
                                            $"Last Updated: {status.LastLocationUpdate:yyyy-MM-dd HH:mm:ss}\n" +
                                            $"Route: {status.RouteDescription}\n" +
                                            $"Running: {(status.IsRunning ? "Yes" : "No")}";
                }
                else
                {
                    trainStatusTextBox.Text = "Train status not found.";
                }

                statusLabel.Text = "Train status retrieved";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting train status for {TrainNumber}", trainNumberTextBox.Text);
                statusLabel.Text = "Error getting train status";
                MessageBox.Show($"Error getting train status: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Enable Auto-Refresh button click
        /// </summary>
        private async void enableAutoRefreshButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(trainNumberTextBox.Text))
            {
                MessageBox.Show("Please enter a train number.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                statusLabel.Text = "Enabling auto-refresh...";
                var trainNumber = trainNumberTextBox.Text.Trim();

                var success = await _autoRefreshService.EnableTrainAutoRefreshAsync(
                    trainNumber,
                    intervalMinutes: 15,
                    startHoursBefore: 2,
                    stopHoursAfter: 1);

                if (success)
                {
                    MessageBox.Show($"Auto-refresh enabled for train {trainNumber}.\n" +
                                  $"Refresh interval: 15 minutes\n" +
                                  $"Monitoring window: 2 hours before to 1 hour after",
                                  "Auto-Refresh Enabled", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Failed to enable auto-refresh.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                statusLabel.Text = "Auto-refresh configuration updated";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enabling auto-refresh for train {TrainNumber}", trainNumberTextBox.Text);
                statusLabel.Text = "Error enabling auto-refresh";
                MessageBox.Show($"Error enabling auto-refresh: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Railway API data received events
        /// </summary>
        private void OnApiDataReceived(object? sender, ApiDataReceivedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnApiDataReceived(sender, e)));
                return;
            }

            apiEventsTextBox.AppendText($"[{e.ReceivedAt:HH:mm:ss}] Data Received: {e.DataType} ({e.RecordCount} records)\r\n");
            apiEventsTextBox.ScrollToCaret();
        }

        /// <summary>
        /// Handles Railway API error events
        /// </summary>
        private void OnApiError(object? sender, ApiErrorEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnApiError(sender, e)));
                return;
            }

            apiEventsTextBox.AppendText($"[{e.OccurredAt:HH:mm:ss}] API Error: {e.ErrorMessage} (Operation: {e.Operation})\r\n");
            apiEventsTextBox.ScrollToCaret();
        }

        /// <summary>
        /// Handles Railway API connection status change events
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, ApiConnectionStatusEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnConnectionStatusChanged(sender, e)));
                return;
            }

            connectionStatusLabel.Text = e.IsConnected ? "Connected" : "Disconnected";
            connectionStatusLabel.ForeColor = e.IsConnected ? Color.Green : Color.Red;

            apiEventsTextBox.AppendText($"[{e.StatusChangedAt:HH:mm:ss}] Connection Status: {e.Status}\r\n");
            apiEventsTextBox.ScrollToCaret();
        }

        /// <summary>
        /// Handles auto-refresh data updated events
        /// </summary>
        private void OnAutoRefreshDataUpdated(object? sender, AutoRefreshDataUpdatedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnAutoRefreshDataUpdated(sender, e)));
                return;
            }

            autoRefreshEventsTextBox.AppendText($"[{DateTime.Now:HH:mm:ss}] Data Updated: {e.EntityType} {e.EntityId} - {e.DataType} ({e.RecordCount} records)\r\n");
            autoRefreshEventsTextBox.ScrollToCaret();
        }

        /// <summary>
        /// Handles auto-refresh error events
        /// </summary>
        private void OnAutoRefreshError(object? sender, AutoRefreshErrorEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnAutoRefreshError(sender, e)));
                return;
            }

            autoRefreshEventsTextBox.AppendText($"[{DateTime.Now:HH:mm:ss}] Refresh Error: {e.EntityType} {e.EntityId} - {e.Operation} (Retry: {e.RetryAttempt})\r\n");
            autoRefreshEventsTextBox.ScrollToCaret();
        }

        /// <summary>
        /// Handles auto-refresh status change events
        /// </summary>
        private void OnAutoRefreshStatusChanged(object? sender, AutoRefreshStatusChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnAutoRefreshStatusChanged(sender, e)));
                return;
            }

            autoRefreshEventsTextBox.AppendText($"[{DateTime.Now:HH:mm:ss}] Status Changed: {e.OldStatus} -> {e.NewStatus} ({e.Reason})\r\n");
            autoRefreshEventsTextBox.ScrollToCaret();
        }

        /// <summary>
        /// Handles the form closing event
        /// </summary>
        private void TrainManagementForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // Unsubscribe from events
                _railwayApiService.DataReceived -= OnApiDataReceived;
                _railwayApiService.ApiError -= OnApiError;
                _railwayApiService.ConnectionStatusChanged -= OnConnectionStatusChanged;

                _autoRefreshService.DataUpdated -= OnAutoRefreshDataUpdated;
                _autoRefreshService.RefreshError -= OnAutoRefreshError;
                _autoRefreshService.StatusChanged -= OnAutoRefreshStatusChanged;

                _logger.LogInformation("Train management form closing");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during form closing");
            }
        }
    }
}
