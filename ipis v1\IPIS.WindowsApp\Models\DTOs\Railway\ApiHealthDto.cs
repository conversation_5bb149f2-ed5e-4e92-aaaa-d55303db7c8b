using System.ComponentModel.DataAnnotations;

namespace IPIS.WindowsApp.Models.DTOs.Railway
{
    /// <summary>
    /// Data Transfer Object for Railway API health information
    /// Monitors API availability and performance metrics
    /// </summary>
    public class ApiHealthDto
    {
        /// <summary>
        /// Indicates if the API is healthy and operational
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// Current status description
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when health check was performed
        /// </summary>
        public DateTime CheckTime { get; set; }

        /// <summary>
        /// Response time in milliseconds
        /// </summary>
        [Range(0, 60000)]
        public int ResponseTimeMs { get; set; }

        /// <summary>
        /// API version information
        /// </summary>
        [StringLength(20)]
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// Additional health information
        /// </summary>
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();

        /// <summary>
        /// API endpoint that was checked
        /// </summary>
        [StringLength(200)]
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// HTTP status code from health check
        /// </summary>
        [Range(100, 599)]
        public int HttpStatusCode { get; set; }

        /// <summary>
        /// Error message if health check failed
        /// </summary>
        [StringLength(500)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Number of consecutive successful checks
        /// </summary>
        [Range(0, int.MaxValue)]
        public int ConsecutiveSuccesses { get; set; }

        /// <summary>
        /// Number of consecutive failed checks
        /// </summary>
        [Range(0, int.MaxValue)]
        public int ConsecutiveFailures { get; set; }

        /// <summary>
        /// Uptime percentage over the last 24 hours
        /// </summary>
        [Range(0, 100)]
        public double UptimePercentage { get; set; }

        /// <summary>
        /// Average response time over the last hour
        /// </summary>
        [Range(0, 60000)]
        public double AverageResponseTimeMs { get; set; }

        /// <summary>
        /// Peak response time in the last hour
        /// </summary>
        [Range(0, 60000)]
        public int PeakResponseTimeMs { get; set; }

        /// <summary>
        /// Number of requests processed in the last hour
        /// </summary>
        [Range(0, int.MaxValue)]
        public int RequestsPerHour { get; set; }

        /// <summary>
        /// Error rate percentage in the last hour
        /// </summary>
        [Range(0, 100)]
        public double ErrorRatePercentage { get; set; }

        /// <summary>
        /// Last successful check timestamp
        /// </summary>
        public DateTime? LastSuccessfulCheck { get; set; }

        /// <summary>
        /// Last failed check timestamp
        /// </summary>
        public DateTime? LastFailedCheck { get; set; }

        /// <summary>
        /// Computed property: Health status color
        /// </summary>
        public string HealthColor => IsHealthy switch
        {
            true when ResponseTimeMs <= 1000 => "#28a745",  // Green - Excellent
            true when ResponseTimeMs <= 3000 => "#ffc107",  // Yellow - Good
            true => "#fd7e14",                              // Orange - Slow but working
            false => "#dc3545"                              // Red - Failed
        };

        /// <summary>
        /// Computed property: Performance rating
        /// </summary>
        public string PerformanceRating
        {
            get
            {
                if (!IsHealthy) return "Failed";
                if (ResponseTimeMs <= 500) return "Excellent";
                if (ResponseTimeMs <= 1000) return "Good";
                if (ResponseTimeMs <= 3000) return "Fair";
                return "Poor";
            }
        }

        /// <summary>
        /// Computed property: Indicates if API is experiencing issues
        /// </summary>
        public bool HasIssues => !IsHealthy || ResponseTimeMs > 5000 || ErrorRatePercentage > 5;

        /// <summary>
        /// Computed property: Time since last check
        /// </summary>
        public TimeSpan TimeSinceCheck => DateTime.UtcNow - CheckTime;

        /// <summary>
        /// Validates the DTO data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Status) &&
                   CheckTime != default &&
                   ResponseTimeMs >= 0;
        }

        /// <summary>
        /// Gets a summary of the API health
        /// </summary>
        /// <returns>Health summary string</returns>
        public string GetSummary()
        {
            var summary = $"API Status: {Status}";
            summary += $", Response Time: {ResponseTimeMs}ms";
            summary += $", Performance: {PerformanceRating}";
            
            if (!string.IsNullOrWhiteSpace(Version))
                summary += $", Version: {Version}";
            
            if (UptimePercentage > 0)
                summary += $", Uptime: {UptimePercentage:F1}%";
            
            return summary;
        }

        /// <summary>
        /// Gets detailed health information
        /// </summary>
        /// <returns>Detailed health report</returns>
        public string GetDetailedReport()
        {
            var report = $"Railway API Health Report\n";
            report += $"Status: {Status} ({(IsHealthy ? "Healthy" : "Unhealthy")})\n";
            report += $"Check Time: {CheckTime:yyyy-MM-dd HH:mm:ss}\n";
            report += $"Response Time: {ResponseTimeMs}ms\n";
            report += $"Performance: {PerformanceRating}\n";
            
            if (!string.IsNullOrWhiteSpace(Version))
                report += $"Version: {Version}\n";
            
            if (!string.IsNullOrWhiteSpace(Endpoint))
                report += $"Endpoint: {Endpoint}\n";
            
            report += $"HTTP Status: {HttpStatusCode}\n";
            report += $"Uptime (24h): {UptimePercentage:F2}%\n";
            report += $"Avg Response Time (1h): {AverageResponseTimeMs:F0}ms\n";
            report += $"Peak Response Time (1h): {PeakResponseTimeMs}ms\n";
            report += $"Requests/Hour: {RequestsPerHour}\n";
            report += $"Error Rate (1h): {ErrorRatePercentage:F2}%\n";
            report += $"Consecutive Successes: {ConsecutiveSuccesses}\n";
            report += $"Consecutive Failures: {ConsecutiveFailures}\n";
            
            if (LastSuccessfulCheck.HasValue)
                report += $"Last Success: {LastSuccessfulCheck:yyyy-MM-dd HH:mm:ss}\n";
            
            if (LastFailedCheck.HasValue)
                report += $"Last Failure: {LastFailedCheck:yyyy-MM-dd HH:mm:ss}\n";
            
            if (!string.IsNullOrWhiteSpace(ErrorMessage))
                report += $"Error: {ErrorMessage}\n";
            
            return report;
        }

        /// <summary>
        /// Returns a string representation of the API health
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }

    /// <summary>
    /// Data Transfer Object for synchronization status information
    /// Tracks data synchronization between local system and Railway API
    /// </summary>
    public class SyncStatusDto
    {
        /// <summary>
        /// Indicates if synchronization is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Last synchronization timestamp
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// Next scheduled synchronization timestamp
        /// </summary>
        public DateTime? NextSyncTime { get; set; }

        /// <summary>
        /// Number of records processed in last sync
        /// </summary>
        [Range(0, int.MaxValue)]
        public int RecordsProcessed { get; set; }

        /// <summary>
        /// Number of records updated in last sync
        /// </summary>
        [Range(0, int.MaxValue)]
        public int RecordsUpdated { get; set; }

        /// <summary>
        /// Number of records added in last sync
        /// </summary>
        [Range(0, int.MaxValue)]
        public int RecordsAdded { get; set; }

        /// <summary>
        /// Number of conflicts detected in last sync
        /// </summary>
        [Range(0, int.MaxValue)]
        public int ConflictsDetected { get; set; }

        /// <summary>
        /// Number of conflicts resolved in last sync
        /// </summary>
        [Range(0, int.MaxValue)]
        public int ConflictsResolved { get; set; }

        /// <summary>
        /// Errors encountered during last sync
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Duration of last synchronization
        /// </summary>
        public TimeSpan LastSyncDuration { get; set; }

        /// <summary>
        /// Current synchronization status
        /// </summary>
        [StringLength(50)]
        public string CurrentStatus { get; set; } = "Idle";

        /// <summary>
        /// Synchronization type (Full, Incremental, Manual)
        /// </summary>
        [StringLength(20)]
        public string SyncType { get; set; } = "Incremental";

        /// <summary>
        /// Progress percentage for active sync (0-100)
        /// </summary>
        [Range(0, 100)]
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Current operation being performed
        /// </summary>
        [StringLength(100)]
        public string? CurrentOperation { get; set; }

        /// <summary>
        /// Number of successful syncs in the last 24 hours
        /// </summary>
        [Range(0, int.MaxValue)]
        public int SuccessfulSyncsToday { get; set; }

        /// <summary>
        /// Number of failed syncs in the last 24 hours
        /// </summary>
        [Range(0, int.MaxValue)]
        public int FailedSyncsToday { get; set; }

        /// <summary>
        /// Average sync duration over the last 10 syncs
        /// </summary>
        public TimeSpan AverageSyncDuration { get; set; }

        /// <summary>
        /// Computed property: Indicates if sync was successful
        /// </summary>
        public bool WasLastSyncSuccessful => Errors.Count == 0;

        /// <summary>
        /// Computed property: Time since last sync
        /// </summary>
        public TimeSpan? TimeSinceLastSync => LastSyncTime.HasValue 
            ? DateTime.UtcNow - LastSyncTime.Value 
            : null;

        /// <summary>
        /// Computed property: Time until next sync
        /// </summary>
        public TimeSpan? TimeUntilNextSync => NextSyncTime.HasValue 
            ? NextSyncTime.Value - DateTime.UtcNow 
            : null;

        /// <summary>
        /// Computed property: Success rate today
        /// </summary>
        public double SuccessRateToday
        {
            get
            {
                var totalSyncs = SuccessfulSyncsToday + FailedSyncsToday;
                return totalSyncs > 0 ? (double)SuccessfulSyncsToday / totalSyncs * 100 : 0;
            }
        }

        /// <summary>
        /// Computed property: Indicates if there are unresolved conflicts
        /// </summary>
        public bool HasUnresolvedConflicts => ConflictsDetected > ConflictsResolved;

        /// <summary>
        /// Validates the DTO data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(CurrentStatus) &&
                   RecordsProcessed >= 0 &&
                   RecordsUpdated >= 0 &&
                   RecordsAdded >= 0;
        }

        /// <summary>
        /// Gets a summary of the sync status
        /// </summary>
        /// <returns>Sync status summary string</returns>
        public string GetSummary()
        {
            var summary = $"Sync Status: {CurrentStatus}";
            
            if (IsActive)
                summary += $" ({ProgressPercentage}%)";
            
            if (LastSyncTime.HasValue)
                summary += $", Last: {LastSyncTime:HH:mm:ss}";
            
            if (NextSyncTime.HasValue)
                summary += $", Next: {NextSyncTime:HH:mm:ss}";
            
            if (RecordsProcessed > 0)
                summary += $", Processed: {RecordsProcessed}";
            
            if (Errors.Count > 0)
                summary += $", Errors: {Errors.Count}";
            
            return summary;
        }

        /// <summary>
        /// Returns a string representation of the sync status
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }
}
