using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using IPIS.WindowsApp.Services.Interfaces;
using IPIS.WindowsApp.Services.Implementations;
using IPIS.WindowsApp.Configuration;
using IPIS.WindowsApp.Models.DTOs.Railway;
using System.Text.Json;

namespace IPIS.WindowsApp.Tests
{
    /// <summary>
    /// Comprehensive integration tests for Railway API functionality
    /// Tests all Railway API services and their integration with the IPIS system
    /// </summary>
    public class RailwayApiIntegrationTests : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IRailwayApiService _railwayApiService;
        private readonly IScheduleSyncService _scheduleSyncService;
        private readonly ILogger<RailwayApiIntegrationTests> _logger;

        public RailwayApiIntegrationTests()
        {
            // Setup test service provider
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));

            // Add configuration
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["RailwayApi:BaseUrl"] = "https://api.railway.test.com",
                    ["RailwayApi:ApiKey"] = "test-api-key-12345",
                    ["RailwayApi:Timeout"] = "30",
                    ["RailwayApi:MaxRetries"] = "3",
                    ["RailwayApi:RetryDelay"] = "1000",
                    ["RailwayApi:EnableCaching"] = "true",
                    ["RailwayApi:CacheExpiration"] = "300",
                    ["Sync:AutoSyncEnabled"] = "true",
                    ["Sync:SyncInterval"] = "60",
                    ["Sync:BatchSize"] = "100"
                })
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // Add Railway API configuration
            services.Configure<RailwayApiConfiguration>(configuration.GetSection("RailwayApi"));
            services.Configure<SyncConfiguration>(configuration.GetSection("Sync"));

            // Add HTTP client
            services.AddHttpClient();

            // Add Railway API services
            services.AddScoped<IRailwayApiService, RailwayApiService>();
            services.AddScoped<IScheduleSyncService, ScheduleSyncService>();

            _serviceProvider = services.BuildServiceProvider();
            _railwayApiService = _serviceProvider.GetRequiredService<IRailwayApiService>();
            _scheduleSyncService = _serviceProvider.GetRequiredService<IScheduleSyncService>();
            _logger = _serviceProvider.GetRequiredService<ILogger<RailwayApiIntegrationTests>>();
        }

        /// <summary>
        /// Tests Railway API service initialization and configuration
        /// </summary>
        public async Task<bool> TestRailwayApiServiceInitialization()
        {
            try
            {
                _logger.LogInformation("Testing Railway API Service Initialization...");

                // Test service is not null
                if (_railwayApiService == null)
                {
                    _logger.LogError("Railway API Service is null");
                    return false;
                }

                // Test configuration validation
                var isConfigValid = await _railwayApiService.ValidateConfigurationAsync();
                if (!isConfigValid)
                {
                    _logger.LogError("Railway API configuration validation failed");
                    return false;
                }

                _logger.LogInformation("✅ Railway API Service initialization test passed");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Railway API Service initialization test failed");
                return false;
            }
        }

        /// <summary>
        /// Tests train schedule retrieval functionality
        /// </summary>
        public async Task<bool> TestTrainScheduleRetrieval()
        {
            try
            {
                _logger.LogInformation("Testing Train Schedule Retrieval...");

                // Test getting schedules for a station
                var testStationCode = "TEST";
                var schedules = await _railwayApiService.GetTrainSchedulesAsync(testStationCode);

                // Verify response structure (even if empty for test environment)
                if (schedules == null)
                {
                    _logger.LogError("Train schedules response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved {schedules.Count()} train schedules for station {testStationCode}");

                // Test getting schedules for date range
                var startDate = DateTime.Today;
                var endDate = DateTime.Today.AddDays(1);
                var dateRangeSchedules = await _railwayApiService.GetTrainSchedulesAsync(testStationCode, startDate, endDate);

                if (dateRangeSchedules == null)
                {
                    _logger.LogError("Date range train schedules response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved {dateRangeSchedules.Count()} train schedules for date range");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Train schedule retrieval test failed");
                return false;
            }
        }

        /// <summary>
        /// Tests train status monitoring functionality
        /// </summary>
        public async Task<bool> TestTrainStatusMonitoring()
        {
            try
            {
                _logger.LogInformation("Testing Train Status Monitoring...");

                // Test getting status for a specific train
                var testTrainNumber = "12345";
                var trainStatus = await _railwayApiService.GetTrainStatusAsync(testTrainNumber);

                // Verify response structure
                if (trainStatus == null)
                {
                    _logger.LogError("Train status response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved train status for train {testTrainNumber}");

                // Test getting status for multiple trains
                var trainNumbers = new[] { "12345", "67890", "11111" };
                var multipleStatuses = await _railwayApiService.GetTrainStatusesAsync(trainNumbers);

                if (multipleStatuses == null)
                {
                    _logger.LogError("Multiple train statuses response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved statuses for {multipleStatuses.Count()} trains");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Train status monitoring test failed");
                return false;
            }
        }

        /// <summary>
        /// Tests delay information retrieval functionality
        /// </summary>
        public async Task<bool> TestDelayInformationRetrieval()
        {
            try
            {
                _logger.LogInformation("Testing Delay Information Retrieval...");

                // Test getting delay info for a station
                var testStationCode = "TEST";
                var delayInfo = await _railwayApiService.GetDelayInformationAsync(testStationCode);

                if (delayInfo == null)
                {
                    _logger.LogError("Delay information response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved delay information for station {testStationCode}");

                // Test getting delay info for a specific train
                var testTrainNumber = "12345";
                var trainDelayInfo = await _railwayApiService.GetTrainDelayInfoAsync(testTrainNumber);

                if (trainDelayInfo == null)
                {
                    _logger.LogError("Train delay information response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved delay information for train {testTrainNumber}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Delay information retrieval test failed");
                return false;
            }
        }

        /// <summary>
        /// Tests platform assignment functionality
        /// </summary>
        public async Task<bool> TestPlatformAssignments()
        {
            try
            {
                _logger.LogInformation("Testing Platform Assignments...");

                // Test getting platform assignments for a station
                var testStationCode = "TEST";
                var platformAssignments = await _railwayApiService.GetPlatformAssignmentsAsync(testStationCode);

                if (platformAssignments == null)
                {
                    _logger.LogError("Platform assignments response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved {platformAssignments.Count()} platform assignments for station {testStationCode}");

                // Test getting platform assignment for specific train
                var testTrainNumber = "12345";
                var trainPlatform = await _railwayApiService.GetTrainPlatformAssignmentAsync(testTrainNumber, testStationCode);

                if (trainPlatform == null)
                {
                    _logger.LogError("Train platform assignment response is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved platform assignment for train {testTrainNumber}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Platform assignments test failed");
                return false;
            }
        }

        /// <summary>
        /// Tests schedule synchronization functionality
        /// </summary>
        public async Task<bool> TestScheduleSynchronization()
        {
            try
            {
                _logger.LogInformation("Testing Schedule Synchronization...");

                // Test manual sync for a station
                var testStationCode = "TEST";
                var syncResult = await _scheduleSyncService.SyncStationSchedulesAsync(testStationCode);

                if (!syncResult)
                {
                    _logger.LogError("Station schedule sync failed");
                    return false;
                }

                _logger.LogInformation($"✅ Successfully synced schedules for station {testStationCode}");

                // Test sync status
                var syncStatus = await _scheduleSyncService.GetSyncStatusAsync();
                if (syncStatus == null)
                {
                    _logger.LogError("Sync status is null");
                    return false;
                }

                _logger.LogInformation($"✅ Retrieved sync status: {syncStatus}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Schedule synchronization test failed");
                return false;
            }
        }

        /// <summary>
        /// Tests error handling and resilience
        /// </summary>
        public async Task<bool> TestErrorHandlingAndResilience()
        {
            try
            {
                _logger.LogInformation("Testing Error Handling and Resilience...");

                // Test with invalid station code
                var invalidStationCode = "INVALID_STATION_CODE_12345";
                var schedules = await _railwayApiService.GetTrainSchedulesAsync(invalidStationCode);

                // Should handle gracefully and return empty collection or null
                _logger.LogInformation($"✅ Handled invalid station code gracefully");

                // Test with invalid train number
                var invalidTrainNumber = "INVALID_TRAIN_99999";
                var trainStatus = await _railwayApiService.GetTrainStatusAsync(invalidTrainNumber);

                // Should handle gracefully
                _logger.LogInformation($"✅ Handled invalid train number gracefully");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error handling and resilience test failed");
                return false;
            }
        }

        /// <summary>
        /// Runs all Railway API integration tests
        /// </summary>
        public async Task<bool> RunAllTests()
        {
            _logger.LogInformation("🚀 Starting Railway API Integration Tests...");

            var testResults = new List<(string TestName, bool Result)>();

            // Run all tests
            testResults.Add(("Railway API Service Initialization", await TestRailwayApiServiceInitialization()));
            testResults.Add(("Train Schedule Retrieval", await TestTrainScheduleRetrieval()));
            testResults.Add(("Train Status Monitoring", await TestTrainStatusMonitoring()));
            testResults.Add(("Delay Information Retrieval", await TestDelayInformationRetrieval()));
            testResults.Add(("Platform Assignments", await TestPlatformAssignments()));
            testResults.Add(("Schedule Synchronization", await TestScheduleSynchronization()));
            testResults.Add(("Error Handling and Resilience", await TestErrorHandlingAndResilience()));

            // Report results
            var passedTests = testResults.Count(t => t.Result);
            var totalTests = testResults.Count;

            _logger.LogInformation($"\n📊 Railway API Integration Test Results:");
            _logger.LogInformation($"✅ Passed: {passedTests}/{totalTests}");
            _logger.LogInformation($"❌ Failed: {totalTests - passedTests}/{totalTests}");

            foreach (var (testName, result) in testResults)
            {
                var status = result ? "✅ PASS" : "❌ FAIL";
                _logger.LogInformation($"  {status}: {testName}");
            }

            var allTestsPassed = passedTests == totalTests;
            if (allTestsPassed)
            {
                _logger.LogInformation($"\n🎉 All Railway API integration tests passed successfully!");
            }
            else
            {
                _logger.LogWarning($"\n⚠️ Some Railway API integration tests failed. Please review the logs above.");
            }

            return allTestsPassed;
        }

        /// <summary>
        /// Disposes test resources
        /// </summary>
        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}
