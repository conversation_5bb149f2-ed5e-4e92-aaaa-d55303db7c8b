# PowerShell script to fix lambda expressions and property accessor issues in C# files

# Get all .cs files in the current directory and subdirectories
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

Write-Host "Found $($csFiles.Count) C# files to process for lambda expressions..."

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read the file content as lines
    $lines = Get-Content $file.FullName
    
    # Skip if file is empty
    if ($lines -eq $null -or $lines.Count -eq 0) {
        continue
    }
    
    $modified = $false
    
    # Process each line
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        
        # Fix lambda expressions like: method(...) => expression;
        if ($line -match '^\s*private\s+void\s+\w+\([^)]*\)\s*=>\s*(.+);?\s*$') {
            $methodSignature = $line -replace '\s*=>\s*(.+);?\s*$', ''
            $expression = $matches[1] -replace ';$', ''
            
            # Replace with proper method body
            $lines[$i] = $methodSignature
            $lines = $lines[0..$i] + "{" + "  $expression;" + "}" + $lines[($i+1)..($lines.Count-1)]
            $modified = $true
            Write-Host "  - Fixed lambda expression on line $($i + 1)"
            $i += 2  # Skip the added lines
        }
        
        # Fix property accessor syntax issues like: get => expression;
        elseif ($line -match '^\s*\[DebuggerNonUserCode\]\s*get\s*=>\s*(.+);?\s*$') {
            $expression = $matches[1] -replace ';$', ''
            $lines[$i] = $line -replace 'get\s*=>\s*(.+);?\s*$', 'get { return ' + $expression + '; }'
            $modified = $true
            Write-Host "  - Fixed property get accessor on line $($i + 1)"
        }
        elseif ($line -match '^\s*\[DebuggerNonUserCode\]\s*set\s*=>\s*(.+);?\s*$') {
            $expression = $matches[1] -replace ';$', ''
            $lines[$i] = $line -replace 'set\s*=>\s*(.+);?\s*$', 'set { ' + $expression + '; }'
            $modified = $true
            Write-Host "  - Fixed property set accessor on line $($i + 1)"
        }
        elseif ($line -match '^\s*get\s*=>\s*(.+);?\s*$') {
            $expression = $matches[1] -replace ';$', ''
            $lines[$i] = $line -replace 'get\s*=>\s*(.+);?\s*$', 'get { return ' + $expression + '; }'
            $modified = $true
            Write-Host "  - Fixed property get accessor on line $($i + 1)"
        }
        elseif ($line -match '^\s*set\s*=>\s*(.+);?\s*$') {
            $expression = $matches[1] -replace ';$', ''
            $lines[$i] = $line -replace 'set\s*=>\s*(.+);?\s*$', 'set { ' + $expression + '; }'
            $modified = $true
            Write-Host "  - Fixed property set accessor on line $($i + 1)"
        }
    }
    
    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $lines
        Write-Host "  - File updated"
    }
}

Write-Host "Lambda expressions processing complete!"
